package com.bocloud.admin.business.service;

import java.util.List;
import com.bocloud.domain.web.VerificationRecord;

/**
 * 认证记录 Service 接口
 * <p>
 * 提供对认证记录的查询、更新等操作。
 * </p>
 *
 * <AUTHOR>
 */
public interface IVerificationRecordService {

    /**
     * 根据用户ID和状态查询最新的认证记录
     *
     * @param userId 用户ID
     * @param status 认证状态（如：1-待认证，2-审核中，3-已通过，-1-已拒绝）
     * @return 认证记录，若无则返回 null
     */
    VerificationRecord selectVerificationRecordByUserIdAndStatus(Long userId, String status);

    /**
     * 更新认证记录
     *
     * @param verificationRecord 认证记录对象
     * @return 更新影响的行数
     */
    int updateVerificationRecord(VerificationRecord verificationRecord);

    /**
     * 查询认证记录列表
     *
     * @param verificationRecord 查询条件（可按用户ID、状态等筛选）
     * @return 认证记录列表
     */
    List<VerificationRecord> selectVerificationRecordList(VerificationRecord verificationRecord);
} 