package com.bocloud.portal.controller.api;

import com.bocloud.common.annotation.Anonymous;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.system.post.Posts;
import com.bocloud.portal.business.service.IPostsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 信息发布接口
 */
@Slf4j
@RestController
@RequestMapping("/api/posts")
@Api(tags = "信息发布接口")
public class ApiPostsController extends BaseController {

    @Autowired
    private IPostsService postsService;

    /**
     * 获取信息发布列表
     */
    @Anonymous
    @GetMapping("/list")
    @ApiOperation("获取信息发布列表")
    public TableDataInfo list(Posts posts) {
        posts.setStatus("2"); // 只查询发布状态的文章
        startPage();
        List<Posts> list = postsService.selectPostsList(posts);
        return getDataTable(list);
    }

    /**
     * 获取信息发布详情
     */
    @Anonymous
    @GetMapping(value = "/{postId}")
    @ApiOperation("获取信息发布详情")
    public AjaxResult getInfo(@PathVariable("postId") Long postId) {
        Posts posts = postsService.selectPostsByPostId(postId);
        if (posts == null) {
            return AjaxResult.error("文章不存在");
        }
        // 检查文章状态是否为发布状态
        if (!Posts.STATUS_PUBLISH.equals(posts.getStatus())) {
            return AjaxResult.error("文章未发布或已下架");
        }
        // 更新浏览次数
        postsService.updatePostViews(postId);
        return success(posts);
    }

    /**
     * 获取相关新闻列表
     */
    @Anonymous
    @GetMapping("/related/{postId}")
    @ApiOperation("获取相关新闻列表")
    public AjaxResult getRelatedPosts(@PathVariable("postId") Long postId) {
        Posts currentPost = postsService.selectPostsByPostId(postId);
        if (currentPost == null) {
            return AjaxResult.error("文章不存在");
        }
        // 检查文章状态是否为发布状态
        if (!Posts.STATUS_PUBLISH.equals(currentPost.getStatus())) {
            return AjaxResult.error("文章未发布或已下架");
        }
        // 设置状态为发布状态
        currentPost.setStatus(Posts.STATUS_PUBLISH);
        List<Posts> relatedPosts = postsService.selectRelatedPosts(currentPost);
        return success(relatedPosts);
    }

    /**
     * 获取热门资讯列表
     */
    @Anonymous
    @GetMapping("/hot")
    @ApiOperation("获取热门资讯列表")
    public TableDataInfo getHotPosts() {
        startPage();
        List<Posts> hotPosts = postsService.selectHotPosts();
        return getDataTable(hotPosts);
    }
} 