package com.bocloud.admin.business.service;

import java.util.List;
import com.bocloud.domain.system.pilot_application.PilotApplication;
import com.bocloud.domain.system.pilot_application.PilotApplicationAuditRequest;

/**
 * 中试申请Service接口
 * 
 * <AUTHOR>
 */
public interface IPilotApplicationService 
{
    /**
     * 查询中试申请
     * 
     * @param id 中试申请主键
     * @return 中试申请
     */
    public PilotApplication selectPilotApplicationById(Long id);

    /**
     * 查询中试申请列表
     * 
     * @param pilotApplication 中试申请
     * @return 中试申请集合
     */
    public List<PilotApplication> selectPilotApplicationList(PilotApplication pilotApplication);

    /**
     * 修改中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    public int updatePilotApplication(PilotApplication pilotApplication);

    /**
     * 审核中试申请
     * 
     * @param request 审核请求
     * @return 结果
     */
    public int auditPilotApplication(PilotApplicationAuditRequest request);

    /**
     * 审核付款凭证
     */
    int auditPayment(PilotApplication pilotApplication);
} 