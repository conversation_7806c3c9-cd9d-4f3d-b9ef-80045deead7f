package com.bocloud.domain.system.demand;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 需求收费对象 demand_charge
 * 
 * <AUTHOR>
 */
@Data
public class DemandCharge extends BaseEntity {
    private static final long serialVersionUID = 1L;

    // 收费类型
    public static final String CHARGE_TYPE_FIXED = "1";      // 固定金额
    public static final String CHARGE_TYPE_PERCENT = "2";    // 百分比

    // 收费状态
    public static final String STATUS_PENDING = "0";      // 待收费（合同审核通过后）
    public static final String STATUS_CHARGED = "1";      // 已收费（已支付）
    public static final String STATUS_INVOICED = "2";     // 已开票
    public static final String STATUS_COMPLETED = "3";    // 已完成（收费流程结束）
    public static final String STATUS_CANCELLED = "-1";   // 已取消

    /** 收费ID */
    private Long chargeId;

    /** 需求ID */
    @Excel(name = "需求ID")
    private Long demandId;

    /** 合同ID */
    @Excel(name = "合同ID")
    private Long contractId;

    /** 收费类型（1-固定金额，2-百分比） */
    @Excel(name = "收费类型", readConverterExp = "1=固定金额,2=百分比")
    private String chargeType;

    /** 收费金额 */
    @Excel(name = "收费金额")
    private BigDecimal chargeAmount;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal contractAmount;

    /** 收费比例（百分比时使用） */
    @Excel(name = "收费比例")
    private BigDecimal chargeRate;

    /** 状态（0-待收费，1-已收费，2-已开票，3-已完成，-1-已取消） */
    @Excel(name = "状态", readConverterExp = "0=待收费,1=已收费,2=已开票,3=已完成,-1=已取消")
    private String status;

    /** 收费时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收费时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date chargeTime;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("chargeId", getChargeId())
            .append("demandId", getDemandId())
            .append("contractId", getContractId())
            .append("chargeType", getChargeType())
            .append("chargeAmount", getChargeAmount())
            .append("contractAmount", getContractAmount())
            .append("chargeRate", getChargeRate())
            .append("status", getStatus())
            .append("chargeTime", getChargeTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .toString();
    }
} 