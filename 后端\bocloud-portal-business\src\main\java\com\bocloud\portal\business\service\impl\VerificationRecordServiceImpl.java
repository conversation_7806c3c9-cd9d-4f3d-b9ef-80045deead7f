package com.bocloud.portal.business.service.impl;

import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.portal.business.mapper.VerificationRecordMapper;
import com.bocloud.portal.business.service.IVerificationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 认证状态Service实现
 */
@Service
public class VerificationRecordServiceImpl implements IVerificationRecordService {

    @Autowired
    private VerificationRecordMapper verificationRecordMapper;

    @Override
    public VerificationRecord selectVerificationRecordByUserId(Long userId) {
        return verificationRecordMapper.selectVerificationRecordByUserId(userId);
    }

    @Override
    public int insertVerificationRecord(VerificationRecord verificationRecord) {
        return verificationRecordMapper.insertVerificationRecord(verificationRecord);
    }

    @Override
    public int updateVerificationRecord(VerificationRecord verificationRecord) {
        return verificationRecordMapper.updateVerificationRecord(verificationRecord);
    }
} 