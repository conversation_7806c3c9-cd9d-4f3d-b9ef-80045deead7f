package com.bocloud.domain.system.platform_service;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告申请对象 ads
 *
 * @date 2025-03-11
 */
public class Ads extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 广告唯一标识 */
    private Long adId;

    /** 广告申请用户ID，外键关联 `users` 表 */
    @Excel(name = "广告申请用户ID，外键关联 `users` 表")
    private Long userId;

    /** 广告投放位置，外键关联 `ad_positions` 表 */
    @Excel(name = "广告投放位置，外键关联 `ad_positions` 表")
    private Long positionId;

    /** 广告内容描述 */
    @Excel(name = "广告内容描述")
    private String content;

    /** 广告图片URL */
    @Excel(name = "广告图片URL")
    private String imageUrl;

    /** 广告视频URL */
    @Excel(name = "广告视频URL")
    private String videoUrl;

    /** 广告投放预算 */
    @Excel(name = "广告投放预算")
    private BigDecimal budget;

    /** 广告投放开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "广告投放开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 广告投放结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "广告投放结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 广告状态（待审核、已批准、已拒绝、投放中、已过期） */
    @Excel(name = "广告状态", readConverterExp = "待=审核、已批准、已拒绝、投放中、已过期")
    private String status;

    public void setAdId(Long adId)
    {
        this.adId = adId;
    }

    public Long getAdId()
    {
        return adId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setPositionId(Long positionId)
    {
        this.positionId = positionId;
    }

    public Long getPositionId()
    {
        return positionId;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setImageUrl(String imageUrl)
    {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl()
    {
        return imageUrl;
    }
    public void setVideoUrl(String videoUrl)
    {
        this.videoUrl = videoUrl;
    }

    public String getVideoUrl()
    {
        return videoUrl;
    }
    public void setBudget(BigDecimal budget)
    {
        this.budget = budget;
    }

    public BigDecimal getBudget()
    {
        return budget;
    }
    public void setStartDate(Date startDate)
    {
        this.startDate = startDate;
    }

    public Date getStartDate()
    {
        return startDate;
    }
    public void setEndDate(Date endDate)
    {
        this.endDate = endDate;
    }

    public Date getEndDate()
    {
        return endDate;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("adId", getAdId())
                .append("userId", getUserId())
                .append("positionId", getPositionId())
                .append("content", getContent())
                .append("imageUrl", getImageUrl())
                .append("videoUrl", getVideoUrl())
                .append("budget", getBudget())
                .append("startDate", getStartDate())
                .append("endDate", getEndDate())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}