package com.bocloud.admin.business.service;

import com.bocloud.domain.web.EnterpriseInfo;

/**
 * 企业信息服务接口
 */
public interface IEnterpriseInfoService {
    /**
     * 根据用户ID获取当前有效的企业信息
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectActiveByUserId(Long userId);

    /**
     * 根据用户ID和状态获取最新的企业信息
     * @param userId 用户ID
     * @param status 状态
     * @return 企业信息
     */
    EnterpriseInfo selectLatestByUserIdAndStatus(Long userId, String status);

    /**
     * 根据用户ID获取当前最优的企业信息（优先级：待审核>已通过且有效>最新未通过）
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectBestByUserId(Long userId);
} 