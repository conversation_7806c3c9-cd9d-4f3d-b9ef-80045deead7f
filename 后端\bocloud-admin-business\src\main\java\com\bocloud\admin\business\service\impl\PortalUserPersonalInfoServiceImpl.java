package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.PortalUserPersonalInfoMapper;
import com.bocloud.admin.business.service.IPortalUserPersonalInfoService;
import com.bocloud.common.enums.PersonalInfoStatus;
import com.bocloud.domain.web.PersonalInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户个人信息 服务实现
 */
@Service
public class PortalUserPersonalInfoServiceImpl implements IPortalUserPersonalInfoService {
    
    @Autowired
    private PortalUserPersonalInfoMapper portalUserPersonalInfoMapper;


    /**
     * 查询用户个人信息
     * 
     * @param userId 用户ID
     * @return 用户个人信息
     */
    @Override
    public PersonalInfo selectPendingPersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_PENDING.getCode());
    }

    @Override
    public PersonalInfo selectActivePersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectActiveByUserId(userId);
    }

    @Override
    public PersonalInfo selectLatestRejectedPersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_REJECTED.getCode());
    }

    /**
     * 聚合优先级查询个人信息：
     * 1. 优先返回"待审核"状态的个人信息（status=0）
     * 2. 如果没有待审核，则返回"已通过且有效"状态的个人信息
     * 3. 如果没有已通过，则返回"最新未通过"状态的个人信息（status=2）
     * 4. 如果都没有，返回null
     *
     * @param userId 用户ID
     * @return 个人信息对象，优先级：待审核 > 已通过 > 最新未通过 > null
     */
    @Override
    public PersonalInfo selectBestPersonalInfoByUserId(Long userId) {
        PersonalInfo pending = selectPendingPersonalInfoByUserId(userId);
        if (pending != null) return pending;
        PersonalInfo active = selectActivePersonalInfoByUserId(userId);
        if (active != null) return active;
        PersonalInfo rejected = selectLatestRejectedPersonalInfoByUserId(userId);
        if (rejected != null) return rejected;
        return null;
    }

    /**
     * 根据用户ID和状态查询最新的个人信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 个人信息
     */
    @Override
    public PersonalInfo selectLatestByUserIdAndStatus(Long userId, String status) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, status);
    }

    @Override
    public PersonalInfo selectActiveByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectActiveByUserId(userId);
    }

    /**
     * 修改用户个人信息
     * 
     * @param personalInfo 用户个人信息
     * @return 结果
     */
    @Override
    public int updatePortalUserPersonalInfo(PersonalInfo personalInfo) {
        return portalUserPersonalInfoMapper.updatePortalUserPersonalInfo(personalInfo);
    }

    /**
     * 根据状态查询个人信息列表
     * 
     * @param status 状态
     * @return 个人信息列表
     */
    @Override
    public List<PersonalInfo> selectListByStatus(String status) {
        return portalUserPersonalInfoMapper.selectListByStatus(status);
    }

}