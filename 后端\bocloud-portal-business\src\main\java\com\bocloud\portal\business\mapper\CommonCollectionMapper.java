package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.CommonCollection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CommonCollectionMapper {
    /**
     * 查询收藏列表
     * @param commonCollection
     * @return
     */
    public List<CommonCollection> selectCollectionList(CommonCollection commonCollection);
    /**
     * 新增收藏
     * @param commonCollection
     * @return
     */
    public int insertCommonCollection(CommonCollection commonCollection);
    /**
     * 删除收藏
     * @param commonCollection
     * @return
     */
    public int deleteCommonCollectionByBussiness(CommonCollection commonCollection);

    /**
     * 查询某业务类型下的收藏数
     */
    int countByBusiness(@Param("businessId") Long businessId, @Param("businessType") String businessType);
}
