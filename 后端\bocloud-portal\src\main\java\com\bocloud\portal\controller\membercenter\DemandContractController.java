package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.DemandContractStatus;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.portal.business.service.IDemandContractService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.validation.Valid;

/**
 * 需求方合同管理
 */
@RestController
@RequestMapping("/membercenter/contract")
public class DemandContractController extends BaseController {

    @Autowired
    private IDemandContractService demandContractService;

    /**
     * 需求方上传合同
     */
    @PostMapping("/upload")
    public AjaxResult uploadContract(@Valid @RequestBody DemandContract contract) {
        // 设置创建人
        contract.setCreateBy(SecurityUtils.getUsername());
        // 设置初始状态为待审核
        contract.setStatus(DemandContractStatus.PENDING.getCode());
        // 设置未删除
        contract.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        return demandContractService.uploadContract(contract);
    }

    /**
     * 需求方合同列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DemandContract contract) {
        contract.setMemberIdA(SecurityUtils.getUserId());
        startPage();
        List<DemandContract> list = demandContractService.selectDemandContractList(contract);
        return getDataTable(list);
    }

    /**
     * 需求方合同详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@PathVariable("id") Long id) {
        DemandContract contract = demandContractService.detail(id);
        if (contract == null) {
            return AjaxResult.error("合同不存在");
        }
        if (!SecurityUtils.getUserId().equals(contract.getMemberIdA())) {
            return AjaxResult.error("无权限查看");
        }
        return AjaxResult.success(contract);
    }
    
}