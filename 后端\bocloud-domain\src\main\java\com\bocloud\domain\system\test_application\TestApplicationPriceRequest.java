package com.bocloud.domain.system.test_application;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 测试申请管理员报价请求对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestApplicationPriceRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long testApplicationId;

    /** 管理员报价金额 */
    @NotNull(message = "管理员报价金额不能为空")
    @DecimalMin(value = "0.01", message = "管理员报价金额必须大于0")
    private BigDecimal adminPrice;

    /** 报价备注 */
    private String adminPriceRemark;
} 