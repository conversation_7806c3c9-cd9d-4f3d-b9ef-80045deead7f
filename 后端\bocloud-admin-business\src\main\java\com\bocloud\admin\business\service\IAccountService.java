package com.bocloud.admin.business.service;

import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.ExpertInfo;

import java.util.List;
import java.util.Map;

/**
 * 账号管理服务接口
 */
public interface IAccountService {
    
    /**
     * 获取账号列表
     *
     * @param portalUser 查询条件
     * @return 分页数据
     */
    List<PortalUser> list(PortalUser portalUser);

    /**
     * 获取会员详细信息
     */
    PortalUser getInfo(Long userId);

    /**
     * 审核账号信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @param remark 备注
     * @return 结果
     */
    int audit(Long userId, String status, String remark);

    /**
     * 获取专家列表
     *
     * @param expertInfo 专家信息查询条件
     * @return 专家用户列表
     */
    List<PortalUser> expertList(ExpertInfo expertInfo);

    /**
     * 根据用户ID获取待审核信息
     * 
     * @param userId 用户ID
     * @return 待审核信息
     */
    Map<String, Object> getPendingInfo(Long userId);

    /**
     * 获取所有待审核的认证信息列表
     * 
     * @param type 认证类型：personal-个人认证，enterprise-企业认证，不传则查询所有
     * @return 待审核信息列表
     */
    List<Map<String, Object>> getPendingList(String type);
}