package com.bocloud.domain.system.online_education;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程对象 courses
 *
 * @date 2025-03-18
 */
public class Courses extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 课程ID */
    private Integer courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程分类 */
    private String categoryId;

    /** 课程描述 */
    @Excel(name = "课程描述")
    private String description;

    /** 课程创建者ID（关联用户表） */
    @Excel(name = "课程创建者ID", readConverterExp = "关联用户表")
    private String creatorId;

    /** 课程创建者姓名 */
    @Excel(name = "课程创建者姓名")
    private String creatorName;

    /** 课程状态（待审核、已批准、已拒绝） */
    @Excel(name = "课程状态", readConverterExp = "待审核、已批准、已拒绝")
    private String status;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal price;

    /** 课程观看次数 */
    @Excel(name = "课程观看次数")
    private Integer views;

    /** 是否推荐（1=推荐，0=不推荐） */
    @Excel(name = "是否推荐", readConverterExp = "1=推荐,0=不推荐")
    private String recommend;

    /** 课程视频列表 */
    private List<CourseVideo> videos;

    public void setCourseId(Integer courseId)
    {
        this.courseId = courseId;
    }

    public Integer getCourseId()
    {
        return courseId;
    }
    public void setCourseName(String courseName)
    {
        this.courseName = courseName;
    }

    public String getCourseName()
    {
        return courseName;
    }
    public void setCategoryId(String categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryId()
    {
        return categoryId;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setCreatorId(String creatorId)
    {
        this.creatorId = creatorId;
    }

    public String getCreatorId()
    {
        return creatorId;
    }

    public void setCreatorName(String creatorName)
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName()
    {
        return creatorName;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }
    public void setViews(Integer views)
    {
        this.views = views;
    }

    public Integer getViews()
    {
        return views;
    }

    public void setRecommend(String recommend) 
    {
        this.recommend = recommend;
    }

    public String getRecommend() 
    {
        return recommend;
    }

    public List<CourseVideo> getVideos() {
        return videos;
    }

    public void setVideos(List<CourseVideo> videos) {
        this.videos = videos;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("courseId", getCourseId())
                .append("courseName", getCourseName())
                .append("description", getDescription())
                .append("creatorId", getCreatorId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("status", getStatus())
                .append("price", getPrice())
                .append("views", getViews())
                .append("recommend", getRecommend())
                .toString();
    }
}
