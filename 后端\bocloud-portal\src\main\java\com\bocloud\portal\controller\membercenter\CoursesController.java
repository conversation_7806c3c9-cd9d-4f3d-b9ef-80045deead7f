package com.bocloud.portal.controller.membercenter;

import com.bocloud.portal.business.service.ICourseVideoService;
import com.bocloud.portal.business.service.ICoursesService;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.system.online_education.Courses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 个人中心，课程控制器
 */
@RestController
@RequestMapping("/membercenter/courses")
public class CoursesController extends BaseController {

    @Autowired
    private ICoursesService coursesService;

    @Autowired
    private ICourseVideoService courseVideoService;

    /**
     * 获取个人课程历史记录
     */
    @GetMapping("/history")
    public TableDataInfo getCourseHistory() {
        startPage();
        Courses courses = new Courses();
        // 设置查询条件：查询状态为1(待审核)和2(已审核)的课程
        courses.setStatus("1,2");
        List<Courses> list = coursesService.selectCoursesList(courses);
        return getDataTable(list);
    }
}
