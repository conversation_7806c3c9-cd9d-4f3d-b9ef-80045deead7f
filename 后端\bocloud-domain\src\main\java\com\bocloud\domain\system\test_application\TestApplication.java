package com.bocloud.domain.system.test_application;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.system.CommonAttachment;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 检测申请对象 test_application
 *
 * @date 2025-03-12
 */
@Data
public class TestApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 申请编号 */
    @Excel(name = "申请编号")
    private String applicationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long portalUserId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String enterpriseName;

    /** 检测项目名称 */
    @Excel(name = "检测项目名称")
    private String testProjectName;

    /** 检测类型 */
    @Excel(name = "检测类型")
    private String testType;

    /** 期望完成时间 */
    @Excel(name = "期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedCompletionTime;

    /** 样品描述（类型、数量、状态等） */
    @Excel(name = "样品描述", readConverterExp = "类型、数量、状态等")
    private String sampleDesc;

    /** 检测目的（研发、质量控制、认证等） */
    @Excel(name = "检测目的", readConverterExp = "研发、质量控制、认证等")
    private String testPurpose;

    /** 最低预算 */
    @Excel(name = "最低预算")
    private BigDecimal minBudget;

    /** 最高预算 */
    @Excel(name = "最高预算")
    private BigDecimal maxBudget;

    /** 时间要求（如加急、常规） */
    @Excel(name = "时间要求", readConverterExp = "如加急、常规")
    private String timeRequirement;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String contactPhone;

    /** 联系人邮箱 */
    @Excel(name = "联系人邮箱")
    private String contactEmail;

    /** 关键字搜索 */
    private String keyword;

    /** 申请状态，0=待审核,1=待付款,2=已付款,3=进行中,4=已完成,-1=已拒绝 */
    @Excel(name = "申请状态", readConverterExp = "0=待审核,1=待付款,2=已付款,3=进行中,4=已完成,-1=已拒绝，-2=已取消")
    private String status;

    /** 付款状态，0=待付款,1=已上传凭证,2=已确认收款,3=付款凭证被拒绝 */
    @Excel(name = "付款状态", readConverterExp = "0=待付款,1=已上传凭证,2=已确认收款,3=付款凭证被拒绝")
    private String paymentStatus;

    /** 是否删除：0-否/1-是 */
    private String delFlag;

    /** 管理员报价 */
    private BigDecimal adminPrice;

    /** 实际确认收款金额 */
    private BigDecimal confirmedAmount;

    /** 审核凭证时间 */
    private Date auditPaymentDate;

    /** 付款凭证 */
    private String paymentVoucher;

    /** 管理员报价备注 */
    private String adminPriceRemark;

    /** 确认收款备注 */
    private String confirmPaymentRemark;

    /** 付款凭证拒绝原因 */
    @Excel(name = "付款凭证拒绝原因")
    private String paymentRejectReason;

    // 状态常量定义
    public static final String STATUS_PENDING_REVIEW = "0";      // 待审核
    public static final String STATUS_PENDING_PAYMENT = "1";     // 待付款
    public static final String STATUS_PAID = "2";                // 已付款
    public static final String STATUS_IN_PROGRESS = "3";         // 进行中
    public static final String STATUS_COMPLETED = "4";           // 已完成
    public static final String STATUS_REJECTED = "-1";           // 已拒绝
    public static final String STATUS_CANCELLED = "-2";          // 已取消

    // 付款状态常量定义
    public static final String PAYMENT_STATUS_PENDING = "0";         // 待付款
    public static final String PAYMENT_STATUS_UPLOADED = "1";        // 已上传凭证(待审核)
    public static final String PAYMENT_STATUS_CONFIRMED = "2";       // 已确认收款
    public static final String PAYMENT_STATUS_REJECTED = "3";        // 付款凭证被拒绝

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 备注说明 */
    @Excel(name = "备注说明")
    private String remark;

    /** 附件列表 */
    private List<CommonAttachment> attachments;

    /** 平台报告 */
    private List<CommonAttachment> reports;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("applicationId", getApplicationId())
                .append("portalUserId", getPortalUserId())
                .append("enterpriseName", getEnterpriseName())
                .append("testProjectName", getTestProjectName())
                .append("testType", getTestType())
                .append("sampleDesc", getSampleDesc())
                .append("testPurpose", getTestPurpose())
                .append("timeRequirement", getTimeRequirement())
                .append("contactName", getContactName())
                .append("contactPhone", getContactPhone())
                .append("contactEmail", getContactEmail())
                .append("status", getStatus())
                .append("paymentStatus", getPaymentStatus())
                .append("adminPrice", getAdminPrice())
                .append("confirmedAmount", getConfirmedAmount())
                .append("paymentVoucher", getPaymentVoucher())
                .append("adminPriceRemark", getAdminPriceRemark())
                .append("confirmPaymentRemark", getConfirmPaymentRemark())
                .append("paymentRejectReason", getPaymentRejectReason())
                .append("reviewComment", getReviewComment())
                .append("remark", getRemark())
                .append("attachments", getAttachments())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
