package com.bocloud.domain.system.platform_service;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 广告位置信息对象 ad_positions
 *
 * @date 2025-03-11
 */
public class AdPositions extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 广告位置唯一标识 */
    private Long positionId;

    /** 广告位置名称（如首页轮播、侧边栏等） */
    @Excel(name = "广告位置名称", readConverterExp = "如=首页轮播、侧边栏等")
    private String name;

    /** 广告位置描述 */
    @Excel(name = "广告位置描述")
    private String description;

    /** 广告位置示例图片URL */
    @Excel(name = "广告位置示例图片URL")
    private String exampleImage;

    public void setPositionId(Long positionId)
    {
        this.positionId = positionId;
    }

    public Long getPositionId()
    {
        return positionId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setExampleImage(String exampleImage)
    {
        this.exampleImage = exampleImage;
    }

    public String getExampleImage()
    {
        return exampleImage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("positionId", getPositionId())
                .append("name", getName())
                .append("description", getDescription())
                .append("exampleImage", getExampleImage())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}