package com.bocloud.portal.business.service.impl;

import com.bocloud.common.constant.CacheConstants;
import com.bocloud.common.constant.Constants;
import com.bocloud.common.constant.UserConstants;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.domain.entity.SysUser;
import com.bocloud.common.core.domain.model.LoginUser;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.exception.user.UserNotExistsException;
import com.bocloud.common.exception.user.UserPasswordNotMatchException;
import com.bocloud.common.utils.AuthenticationContextHolder;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.common.utils.ip.IpUtils;
import com.bocloud.common.utils.smsConfig.SmsCodeAuthenticationToken;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.PersonalInfoStatus;
import com.bocloud.common.enums.UserType;
import com.bocloud.common.enums.AccountStatus;
import com.bocloud.common.enums.InfoStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.framework.web.service.TokenService;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.ISmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    IPortalUserPersonalInfoService personalInfoService;

    @Autowired
    private ISmsService smsService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        // validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new BadCredentialsException(e.getMessage());
            } else {
                //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = true;
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                //throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                //throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            //AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            //throw new UserPasswordNotMatchException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        //userService.updateUserProfile(sysUser);
    }

    /**
     * 手机号登录验证
     *
     * @param mobile 手机号
     * @param code   验证码
     * @param smsUuid   唯一标识
     * @param openId 小程序openId（可选，用于用户合并）
     * @return 结果
     */
    @Transactional
    public AjaxResult smsLogin(String mobile, String code, String smsUuid, String openId) {
        log.info("[smsLogin] 开始短信验证码登录，mobile: {}, openId: {}", mobile, openId);
        
        // 验证短信验证码
        try {
            smsService.checkSmsCode(mobile, code, smsUuid);
        } catch (ServiceException e) {
            throw new BadCredentialsException(e.getMessage());
        }
        
        // 查询用户是否存在
        PortalUser portalUser = portalUserService.selectUserByPhone(mobile);
        
        // 如果有openId且用户不存在，才执行合并逻辑
        if (StringUtils.isNotEmpty(openId) && portalUser == null) {
            log.info("[smsLogin] 检测到openId且用户不存在，使用用户合并逻辑，mobile: {}, openId: {}", mobile, openId);
            portalUser = mergeUserIfNeeded(mobile, openId);
            log.info("[smsLogin] 用户合并完成，userId: {}, mobile: {}", portalUser.getId(), mobile);
        } else if (portalUser == null) {
            log.info("[smsLogin] 用户不存在，开始创建新用户，mobile: {}", mobile);
            // 如果用户不存在，进行静默注册
            portalUser = new PortalUser();
            portalUser.setPhone(mobile);
            portalUser.setUsername(mobile); // 使用手机号作为用户名
                    portalUser.setType(UserType.PERSONAL.getCode()); // 默认为个人用户
        portalUser.setStatus(AccountStatus.NORMAL.getCode()); // 正常状态，使用Integer类型
        portalUser.setInfoStatus(InfoStatus.INCOMPLETE.getCode()); //未完善
            portalUser.setLastLoginTime(DateUtils.getNowDate());
            portalUser.setCreateBy("system");
            portalUserService.insertPortalUser(portalUser);

            //完成personalInfo信息初始化
            PersonalInfo personalInfo = new PersonalInfo();
            personalInfo.setUserId(portalUser.getId());
            personalInfo.setStatus(CommonStatus.NORMAL.getCode());
            personalInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
            personalInfo.setActive(PersonalInfoStatus.ACTIVE_YES.getCode());
            personalInfoService.insertPortalUserPersonalInfo(personalInfo);
            log.info("[smsLogin] 新用户创建完成，userId: {}, mobile: {}", portalUser.getId(), mobile);
        } else {
            log.info("[smsLogin] 用户已存在，更新最后登录时间，userId: {}, mobile: {}", portalUser.getId(), mobile);
            // 更新最后登录时间
            PortalUser updateUser = new PortalUser();
            updateUser.setId(portalUser.getId());
            updateUser.setLastLoginTime(DateUtils.getNowDate());
            portalUserService.updatePortalUser(updateUser);
        }

        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new SmsCodeAuthenticationToken(mobile, code, smsUuid));
        } catch (Exception e) {
            log.error("[smsLogin] 用户验证失败，mobile: {}", mobile, e);
            //AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_FAIL, e.getMessage()));
            throw new ServiceException(e.getMessage());
        }
        //AsyncManager.me().execute(AsyncFactory.recordLogininfor(mobile, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        
        // 生成token
        String token = tokenService.createToken(loginUser);
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, token);
        //ajax.put("user", loginUser);
        redisCache.deleteObject(Constants.SMS_CAPTCHA_CODE_KEY + mobile + ":" + smsUuid);
        log.info("[smsLogin] 短信验证码登录成功，userId: {}, mobile: {}", portalUser.getId(), mobile);
        return ajax;
    }

    /**
     * 合并用户（如果需要）
     * 处理手机号和openId可能对应不同用户的情况
     * @param phone 手机号
     * @param openId 微信openId
     * @return 合并后的用户
     */
    private PortalUser mergeUserIfNeeded(String phone, String openId) {
        log.info("[mergeUserIfNeeded] 开始检查用户合并，phone: {}, openId: {}", phone, openId);
        
        PortalUser phoneUser = portalUserService.selectUserByPhone(phone);
        PortalUser openIdUser = portalUserService.selectUserByWxOpenId(openId);
        
        if (phoneUser != null && openIdUser != null) {
            // 两个用户都存在，需要合并
            if (phoneUser.getId().equals(openIdUser.getId())) {
                // 已经是同一用户，直接返回
                log.info("[mergeUserIfNeeded] 用户已存在且已绑定，无需合并，userId: {}", phoneUser.getId());
                return phoneUser;
            } else {
                // 需要合并两个不同的用户
                log.info("[mergeUserIfNeeded] 发现需要合并的用户，手机号用户ID: {}, openId用户ID: {}", 
                        phoneUser.getId(), openIdUser.getId());
                
                // 保留手机号用户，绑定openId，删除openId用户
                phoneUser.setWechatOpenid(openId);
                phoneUser.setWechatUnionid(openIdUser.getWechatUnionid());
                portalUserService.updatePortalUser(phoneUser);
                
                // 逻辑删除openId用户
                openIdUser.setDelFlag("1");
                portalUserService.updatePortalUser(openIdUser);
                
                log.info("[mergeUserIfNeeded] 用户合并完成，保留用户ID: {}, 删除用户ID: {}", 
                        phoneUser.getId(), openIdUser.getId());
                return phoneUser;
            }
        } else if (phoneUser != null) {
            // 只有手机号用户存在，绑定openId
            log.info("[mergeUserIfNeeded] 手机号用户存在，绑定openId，userId: {}", phoneUser.getId());
            phoneUser.setWechatOpenid(openId);
            portalUserService.updatePortalUser(phoneUser);
            return phoneUser;
        } else if (openIdUser != null) {
            // 只有openId用户存在，绑定手机号
            log.info("[mergeUserIfNeeded] openId用户存在，绑定手机号，userId: {}", openIdUser.getId());
            openIdUser.setPhone(phone);
            openIdUser.setUsername(phone); // 用手机号做用户名
            portalUserService.updatePortalUser(openIdUser);
            return openIdUser;
        } else {
            // 都不存在，创建新用户
            log.info("[mergeUserIfNeeded] 用户不存在，创建新用户");
            PortalUser portalUser = new PortalUser();
            portalUser.setPhone(phone);
            portalUser.setUsername(phone);
            portalUser.setWechatOpenid(openId);
                    portalUser.setType(UserType.PERSONAL.getCode());
        portalUser.setStatus(AccountStatus.NORMAL.getCode());
        portalUser.setInfoStatus(InfoStatus.INCOMPLETE.getCode());
        portalUser.setVerificationType(VerificationType.PERSONAL.getCode());
        portalUser.setVerificationStatus(VerificationStatus.PENDING.getCode());
            portalUser.setCreateBy("miniapp-sms");
            portalUser.setCreateTime(DateUtils.getNowDate());
            portalUser.setDelFlag("0");
            
            int insertResult = portalUserService.insertPortalUser(portalUser);
            log.info("[mergeUserIfNeeded] 新用户创建完成，userId: {}, insertResult: {}", 
                    portalUser.getId(), insertResult);
            return portalUser;
        }
    }
}
