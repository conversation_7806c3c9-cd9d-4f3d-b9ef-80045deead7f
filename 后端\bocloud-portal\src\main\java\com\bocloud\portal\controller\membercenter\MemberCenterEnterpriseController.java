package com.bocloud.portal.controller.membercenter;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.dto.EnterpriseVerificationDTO;
import com.bocloud.domain.web.dto.EnterpriseInfoUpdateRequest;
import com.bocloud.portal.business.service.IEnterpriseInfoService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.ISmsService;
import com.bocloud.common.enums.UserType;

@RestController
@RequestMapping("/membercenter/enterprise")
public class MemberCenterEnterpriseController extends BaseController {

    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    private ISmsService smsService;

    /**
     * 获取企业信息
     */
    @GetMapping("/info")
    public AjaxResult getInfo() {
        Long userId = SecurityUtils.getUserId();
        
        // 获取用户基本信息
        PortalUser user = portalUserService.selectUserById(userId);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }
        
        // 判断账号类型
        if (!UserType.ENTERPRISE.getCode().equals(user.getType())) {
            return AjaxResult.error("当前账号非企业类型，请先实名认证");
        }
        
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getEnterpriseInfo(userId);
        return success(enterpriseInfo);
    }

    /**
     * 更新企业普通信息
     * 注意：此接口只能更新普通信息（如联系方式、地址等），
     * 不能更新认证信息（如公司名称、营业执照、法人信息等）。
     * 如需更新认证信息，请使用认证接口。
     */
    @PutMapping("/info")
    public AjaxResult updateInfo(@RequestBody EnterpriseInfoUpdateRequest request) {
        // 校验验证码
        //smsService.checkSmsCode(request.getContactPhone(), request.getSmsCode(), request.getSmsUuid());
        int rows = enterpriseInfoService.updateEnterpriseInfo(request);
        return rows > 0 ? AjaxResult.success() : AjaxResult.error("更新企业信息失败");
    }

    /**
     * 提交企业实名认证
     */
    @PostMapping("/verify")
    public AjaxResult submitEnterpriseVerification(@Validated @RequestBody EnterpriseVerificationDTO verificationDTO) {
        Long userId = getUserId();
        enterpriseInfoService.submitEnterpriseVerification(userId, verificationDTO);
        return AjaxResult.success();
    }
}
