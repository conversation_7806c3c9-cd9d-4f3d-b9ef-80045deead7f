package com.bocloud.common.enums;

/**
 * 企业信息状态枚举
 * 
 * <AUTHOR>
 */
public enum EnterpriseInfoStatus {
    
    /**
     * 状态枚举
     */
    STATUS_PENDING("0", "待审核"),
    STATUS_NORMAL("1", "正常"),
    STATUS_REJECTED("-1", "未通过"),
    
    /**
     * 是否当前有效枚举
     */
    ACTIVE_YES("1", "是"),
    ACTIVE_NO("0", "否");
    
    private final String code;
    private final String info;
    
    EnterpriseInfoStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
    
    /**
     * 根据状态码获取状态枚举
     */
    public static EnterpriseInfoStatus getStatusByCode(String code) {
        for (EnterpriseInfoStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据状态码获取状态信息
     */
    public static String getStatusInfo(String code) {
        EnterpriseInfoStatus status = getStatusByCode(code);
        return status != null ? status.getInfo() : "";
    }
} 