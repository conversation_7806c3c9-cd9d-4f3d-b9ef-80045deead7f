package com.bocloud.domain.system.demand;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 企业需求对象 enterprise_demand
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EnterpriseDemand extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 需求状态常量已迁移到 EnterpriseDemandStatus 枚举

    /** 需求ID */
    private Long demandId;

    /** 需求编号 */
    @Excel(name = "需求编号")
    private String demandNo;

    /** 门户用户ID */
    @Excel(name = "门户用户ID")
    private Long portalUserId;

    /** 需求标题 */
    @Excel(name = "需求标题")
    private String demandTitle;

    /** 需求描述 */
    @Excel(name = "需求描述")
    private String demandDescription;

    /** 需求类别 */
    @Excel(name = "需求类别")
    private String demandCategory;

    /** 具体需求（最多5条） */
    private List<String> specificRequirements;

    /** 技术指标（最多5条） */
    private List<String> technicalIndicators;

    /** 其他要求（最多5条） */
    private List<String> otherRequirements;

    /** 行业分类 */
    @Excel(name = "行业分类")
    private String industry;

    /** 技术领域 */
    @Excel(name = "技术领域")
    private String technologyField;

    /** 最低预算 */
    @Excel(name = "最低预算")
    private BigDecimal minBudget;

    /** 最高预算 */
    @Excel(name = "最高预算")
    private BigDecimal maxBudget;

    /** 期望交付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望交付时间")
    private Date deliveryTime;

    /** 状态 */
    @Excel(name = "状态, 0-待审核，1-待公开，2-投标中，3-选标中，4-待签约，5-进行中，6-已完成，-1-审核失败")
    private String status;

    /** 状态列表（用于多状态查询） */
    private List<String> statusList;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 附件URL列表 */
    private List<CommonAttachment> attachments = new ArrayList<>();

    /** 时间范围类型（all/7/30/90） */
    private String dateRangeType;

    /** 审核意见 */
    private String reviewComment;

    /** 删除标志（0代表存在 1代表删除） */
    @Excel(name = "删除标志", readConverterExp = "0=存在,1=删除")
    private String delFlag;

    /** 方案数量 */
    @Excel(name = "方案数量")
    private int solutionCount;

    /** 搜索关键词 */
    private String keyword;

    /** 关联合同信息 */
    private DemandContract contract;

    /**
     * 获取当前状态的中文描述
     */
    public String getStatusDesc() {
        return EnterpriseDemandStatus.getDescByCode(this.status);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("demandId", getDemandId())
            .append("demandNo", getDemandNo())
            .append("portalUserId", getPortalUserId())
            .append("demandTitle", getDemandTitle())
            .append("demandDescription", getDemandDescription())
            .append("demandCategory", getDemandCategory())
            .append("industry", getIndustry())
            .append("technologyField", getTechnologyField())
            .append("minBudget", getMinBudget())
            .append("maxBudget", getMaxBudget())
            .append("deliveryTime", getDeliveryTime())
            .append("status", getStatus())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("solutionCount", getSolutionCount())
            .append("keyword", getKeyword())
            .toString();
    }
} 