package com.bocloud.portal.business.service.impl;

import com.bocloud.domain.system.online_education.Courses;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.portal.business.mapper.CoursesMapper;
import com.bocloud.portal.business.service.ICoursesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 课程Service业务层处理
 *
 * @date 2025-03-18
 */
@Service
public class CoursesServiceImpl implements ICoursesService
{
    @Autowired
    private CoursesMapper coursesMapper;

    /**
     * 查询课程
     *
     * @param courseId 课程主键
     * @return 课程
     */
    @Override
    public Courses selectCoursesByCourseId(Integer courseId)
    {
        return coursesMapper.selectCoursesByCourseId(courseId);
    }

    /**
     * 查询课程列表
     *
     * @param courses 课程
     * @return 课程
     */
    @Override
    public List<Courses> selectCoursesList(Courses courses)
    {
        return coursesMapper.selectCoursesList(courses);
    }

    /**
     * 新增课程
     *
     * @param courses 课程
     * @return 结果
     */
    @Override
    public int insertCourses(Courses courses)
    {
        courses.setCreateTime(DateUtils.getNowDate());
        return coursesMapper.insertCourses(courses);
    }

    /**
     * 修改课程
     *
     * @param courses 课程
     * @return 结果
     */
    @Override
    public int updateCourses(Courses courses)
    {
        courses.setUpdateTime(DateUtils.getNowDate());
        return coursesMapper.updateCourses(courses);
    }

    /**
     * 批量删除课程
     *
     * @param courseIds 需要删除的课程主键
     * @return 结果
     */
    @Override
    public int deleteCoursesByCourseIds(Integer[] courseIds)
    {
        return coursesMapper.deleteCoursesByCourseIds(courseIds);
    }

    /**
     * 删除课程信息
     *
     * @param courseId 课程主键
     * @return 结果
     */
    @Override
    public int deleteCoursesByCourseId(Integer courseId)
    {
        return coursesMapper.deleteCoursesByCourseId(courseId);
    }

    /**
     * 审核通过课程
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int auditCourse(Integer courseId) {
        Courses course = new Courses();
        course.setCourseId(courseId);
        course.setStatus("2"); // 设置状态为审核通过
        return coursesMapper.updateCourses(course);
    }

    /**
     * 下架课程
     *
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int takedownCourse(Integer courseId) {
        Courses course = new Courses();
        course.setCourseId(courseId);
        course.setStatus("3"); // 设置状态为审核通过
        return coursesMapper.updateCourses(course);
    }

}
