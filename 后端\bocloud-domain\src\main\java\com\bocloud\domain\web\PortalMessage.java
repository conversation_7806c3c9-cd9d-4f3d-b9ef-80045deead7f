package com.bocloud.domain.web;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PortalMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private Long messageId;

    /** 消息标题 */
    @Excel(name = "消息标题")
    private String title;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String content;

    /** 消息类型（1=系统通知 2=活动通知 3=审核通知） */
    @Excel(name = "消息类型", readConverterExp = "1=系统通知,2=活动通知,3=审核通知")
    private String type;

    /** 消息状态 */
    private String status;

    public String getStatus() {
        return status == null ? "0" : status;
    }

    /** 发送者ID */
    private Long senderId;

    /** 接收者ID（为空表示全体用户） */
    private Long receiverId;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;
} 