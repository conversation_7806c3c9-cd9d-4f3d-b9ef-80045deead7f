package com.bocloud.portal.business.mapper;

import java.util.List;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.ExpertWorkExperience;
import com.bocloud.domain.web.ExpertPaper;

/**
 * 专家信息Mapper接口
 */
public interface ExpertInfoMapper {
    /**
     * 查询专家信息列表
     * 
     * @param expertInfo 专家信息
     * @return 专家信息集合
     */
    public List<ExpertInfo> selectExpertInfoList(ExpertInfo expertInfo);

    /**
     * 查询专家信息
     * 
     * @param expertId 专家ID
     * @return 专家信息
     */
    public ExpertInfo selectExpertInfoById(Long expertId);

    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    public int insertExpertInfo(ExpertInfo expertInfo);

    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    public int updateExpertInfo(ExpertInfo expertInfo);

    /**
     * 删除专家信息
     * 
     * @param expertId 专家ID
     * @return 结果
     */
    public int deleteExpertInfoById(Long expertId);

    /**
     * 批量删除专家信息
     * 
     * @param expertIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteExpertInfoByIds(Long[] expertIds);

    /**
     * 根据用户ID查询专家信息
     */
    ExpertInfo selectExpertInfoByUserId(Long userId);

 
} 