package com.bocloud.admin.business.service;

import com.bocloud.domain.system.achievement.Achievements;

import java.util.List;

/**
 * @description 成果发布Service接口
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface IAchievementsService {
    /**
     * 查询成果发布
     *
     * @param achievementId 成果发布主键
     * @return 成果发布
     */
    public Achievements selectAchievementsByAchievementId(Long achievementId);

    /**
     * 查询成果发布列表
     *
     * @param achievements 成果发布
     * @return 成果发布集合
     */
    public List<Achievements> selectAchievementsList(Achievements achievements);

    /**
     * 新增成果发布
     *
     * @param achievements 成果发布
     * @return 结果
     */
    public int insertAchievements(Achievements achievements);

    /**
     * 修改成果发布
     *
     * @param achievements 成果发布
     * @return 结果
     */
    public int updateAchievements(Achievements achievements);

    /**
     * 批量删除成果发布
     *
     * @param achievementIds 需要删除的成果发布主键集合
     * @return 结果
     */
    public int deleteAchievementsByAchievementIds(Long[] achievementIds);

    /**
     * 删除成果发布成果
     *
     * @param achievementId 成果发布主键
     * @return 结果
     */
    public int deleteAchievementsByAchievementId(Long achievementId);


}
