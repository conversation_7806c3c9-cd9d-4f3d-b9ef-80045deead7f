package com.bocloud.admin.business.service.impl;

import java.util.List;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bocloud.admin.business.mapper.DemandContractMapper;
import com.bocloud.admin.business.service.IDemandContractService;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.domain.system.demand.DemandCharge;
import com.bocloud.admin.business.service.IDemandChargeService;
import com.bocloud.common.enums.AuditStatus;
import com.bocloud.common.enums.DemandContractStatus;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.admin.business.service.ISysConfigService;
import com.bocloud.admin.business.mapper.EnterpriseDemandMapper;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.admin.business.mapper.DemandSolutionMapper;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.dto.ContractAuditDTO;
import com.bocloud.common.utils.SecurityUtils;
import java.util.Date;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.domain.system.SysAudit;

/**
 * 需求合同Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class DemandContractServiceImpl implements IDemandContractService {
    @Autowired
    private DemandContractMapper demandContractMapper;

    @Autowired
    private IDemandChargeService demandChargeService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;

    @Autowired
    private DemandSolutionMapper demandSolutionMapper;

    @Autowired
    private ISysAuditService sysAuditService;
    
    @Autowired
    private IMessageNotificationService messageNotificationService;

    /**
     * 查询需求合同列表
     * 
     * @param demandContract 需求合同
     * @return 需求合同
     */
    @Override
    public List<DemandContract> selectDemandContractList(DemandContract demandContract) {
        return demandContractMapper.selectDemandContractList(demandContract);
    }

    /**
     * 查询需求合同
     * 
     * @param contractId 需求合同主键
     * @return 需求合同
     */
    @Override
    public DemandContract selectDemandContractById(Long contractId) {
        return demandContractMapper.selectDemandContractById(contractId);
    }

    /**
     * 查询需求合同
     * 
     * @param demandId 需求ID
     * @return 需求合同
     */
    @Override
    public DemandContract selectDemandContractByDemandId(Long demandId) {
        return demandContractMapper.selectDemandContractByDemandId(demandId);
    }

    /**
     * 修改需求合同
     * 
     * @param demandContract 需求合同
     * @return 结果
     */
    @Override
    public int updateDemandContract(DemandContract demandContract) {
        return demandContractMapper.updateDemandContract(demandContract);
    }

    /**
     * 批量审核通过
     * 
     * @param contractIds 合同ID数组
     * @param chargeType 收费类型（1固定收费 2比例收费）
     * @param fixedAmount 固定收费金额
     * @return 结果
     */
    @Override
    public int approveContracts(Long[] contractIds, String chargeType, BigDecimal fixedAmount) {
        int rows = 0;
        for (Long contractId : contractIds) {
            DemandContract contract = demandContractMapper.selectDemandContractById(contractId);
            if (contract != null) {
                // 更新合同状态为已审核
                contract.setStatus("1");
                rows += demandContractMapper.updateDemandContract(contract);

                // 创建收费记录
                DemandCharge charge = new DemandCharge();
                charge.setDemandId(contract.getDemandId());
                charge.setContractId(contractId);
                charge.setChargeType(chargeType);
                if ("1".equals(chargeType)) {
                    // 固定收费
                    charge.setChargeAmount(fixedAmount);
                } else {
                    // 比例收费
                    String chargeRateStr = configService.selectConfigByKey("demand.charge.rate");
                    if (StringUtils.isNotEmpty(chargeRateStr)) {
                        BigDecimal chargeRate = new BigDecimal(chargeRateStr);
                        charge.setChargeAmount(contract.getContractAmount().multiply(chargeRate).divide(new BigDecimal("100")));
                    } else {
                        // 如果未配置收费比例，使用默认值5%
                        charge.setChargeAmount(contract.getContractAmount().multiply(new BigDecimal("5")).divide(new BigDecimal("100")));
                    }
                }
                charge.setStatus("0"); // 待收费
                demandChargeService.insertDemandCharge(charge);
            }
        }
        return rows;
    }

    @Override
    @Transactional
    public int auditContract(ContractAuditDTO auditDTO) {
        // 1. 查询合同
        DemandContract contract = demandContractMapper.selectDemandContractById(auditDTO.getContractId());
        if (contract == null) {
            throw new ServiceException("合同不存在");
        }
        // 2. 只允许待审核状态审核
        if (!DemandContractStatus.PENDING.getCode().equals(contract.getStatus())) {
            throw new ServiceException("只能审核待审核状态的合同");
        }
        // 3. 设置平台收费类型和金额
        contract.setChargeType(auditDTO.getChargeType());
        contract.setFixedAmount(auditDTO.getFixedAmount());
        contract.setChargeRate(auditDTO.getChargeRate());
        // 4. 审核结果处理
        if (AuditStatus.PASSED.getCode().equals(auditDTO.getAuditResult())) {
            contract.setStatus(DemandContractStatus.EFFECTIVE.getCode()); // 2=审核通过(进行中)
        } else if (AuditStatus.NOT_PASS.getCode().equals(auditDTO.getAuditResult())) {
            contract.setStatus(DemandContractStatus.REJECTED.getCode()); // 3=审核不通过
        } else {
            throw new ServiceException("无效的审核结果");
        }
        // 5. 设置审核意见
        contract.setRemark(auditDTO.getReviewComment());
        contract.setUpdateBy(SecurityUtils.getUsername());
        // 6. 更新合同
        int result = demandContractMapper.updateDemandContract(contract);
        
        // 7. 记录审核日志
        SysAudit audit = new SysAudit();
        audit.setBusinessType("DEMAND_CONTRACT");
        audit.setBusinessId(auditDTO.getContractId());
        audit.setAuditResult(auditDTO.getAuditResult());
        audit.setFromStatus(contract.getStatus()); // 审核前状态：待审核
        audit.setToStatus(AuditStatus.PASSED.getCode().equals(auditDTO.getAuditResult()) ? 
            DemandContractStatus.EFFECTIVE.getCode() : DemandContractStatus.REJECTED.getCode()); // 审核后状态
        audit.setAuditType("0");
        audit.setReviewComment(auditDTO.getReviewComment());
        audit.setAuditUserName(SecurityUtils.getUsername());
        audit.setAuditUserId(SecurityUtils.getUserId());
        sysAuditService.insertSysAudit(audit);

        // 8. 更新需求状态和方案状态
        if (AuditStatus.PASSED.getCode().equals(auditDTO.getAuditResult())) {
            // 审核通过：更新方案状态为已中标，需求状态为进行中
            DemandSolution solution = new DemandSolution();
            solution.setSolutionId(contract.getSolutionId());
            solution.setStatus(DemandSolutionStatus.BID_WIN.getCode()); // 已中标
            demandSolutionMapper.updateDemandSolution(solution);
            
            EnterpriseDemand demand = new EnterpriseDemand();
            demand.setDemandId(contract.getDemandId());
            demand.setStatus(EnterpriseDemandStatus.IN_PROGRESS.getCode()); // 进行中
            enterpriseDemandMapper.updateEnterpriseDemandStatusByDemandId(demand);
        } else if (AuditStatus.NOT_PASS.getCode().equals(auditDTO.getAuditResult())) {
            // 审核拒绝：需求状态回退到待签约，允许重新上传合同
            EnterpriseDemand demand = new EnterpriseDemand();
            demand.setDemandId(contract.getDemandId());
            demand.setStatus(EnterpriseDemandStatus.WAIT_SIGN.getCode()); // 待签约
            enterpriseDemandMapper.updateEnterpriseDemandStatusByDemandId(demand);
        }
        
        // 发送合同审核结果通知
        try {
            boolean approved = AuditStatus.PASSED.getCode().equals(auditDTO.getAuditResult());
            // 获取需求信息用于消息通知
            EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(contract.getDemandId());
            if (demand != null) {
                messageNotificationService.sendContractAuditNotification(
                    auditDTO.getContractId(),
                    demand.getDemandTitle(),
                    demand.getPortalUserId(),
                    approved,
                    auditDTO.getReviewComment()
                );
            }
        } catch (Exception e) {
            // 消息发送失败不影响主业务流程
            // 可以记录日志，但不抛出异常
        }
        
        return result;
    }
} 