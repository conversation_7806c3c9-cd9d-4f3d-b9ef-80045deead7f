package com.bocloud.portal.business.mapper;

import java.util.List;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.RegisterUser;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.EnterpriseInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 门户用户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface PortalUserMapper
{

    /**
     * 查询门户用户信息列表
     * 
     * @param portalUser 门户用户信息
     * @return 门户用户信息集合
     */
    public List<PortalUser> selectPortalUserList(PortalUser portalUser);

    /**
     * 通过手机号码查询用户
     *
     * @param phone 手机号
     * @return 用户对象信息
     */
    public PortalUser selectPortalUserByPhone(String phone);


    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public PortalUser selectUserByUserName(String userName);

    /**
     * 新增门户用户信息
     * 
     * @param registerUser 门户用户信息
     * @return 结果
     */
    public int insertPortalUser(RegisterUser registerUser);

    /**
     * 修改门户用户信息
     * 
     * @param portalUser 门户用户信息
     * @return 结果
     */
    public int updatePortalUser(PortalUser portalUser);

    public int resetPassword(PortalUser portalUser);

    /**
     * 删除门户用户信息
     * 
     * @param userId 门户用户信息主键
     * @return 结果
     */
    public int deletePortalUserByUserId(Long userId);

    /**
     * 批量删除门户用户信息
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePortalUserByUserIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public PortalUser checkUserNameUnique(String userName);

    /**
     * 查询用户信息
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    public PortalUser selectPortalUserById(Long id);

    /**
     * 新增用户
     * 
     * @param portalUser 用户信息
     * @return 结果
     */
    public int insertPortalUser(PortalUser portalUser);

    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 结果
     */
    public int deletePortalUserById(Long id);

    /**
     * 通过微信openId查询用户
     *
     * @param openId 微信openId
     * @return 用户对象信息
     */
    PortalUser selectUserByWxOpenId(@Param("openId") String openId);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    PortalUser selectUserById(@Param("userId") Long userId);
}
