package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class WxUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 微信openId */
    private String openId;

    /** 微信unionId */
    private String unionId;

    /** 昵称 */
    private String nickname;

    /** 头像 */
    private String headimgurl;

    /** 是否关注(1:关注; 0:未关注) */
    private String subscribeStatus;

    /** 关注时间 */
    private Long subscribeTime;

    /** 用户状态(0:正常; 1:禁用) */
    private String status;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;
} 