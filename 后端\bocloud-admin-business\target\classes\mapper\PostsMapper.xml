<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PostsMapper">

    <resultMap type="Posts" id="PostsResult">
        <result property="postId"    column="post_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="categoryId"    column="category_id"    />
        <result property="status"    column="status"    />
        <result property="keywords"    column="keywords"    />
        <result property="mainImage"    column="main_image"    />
        <result property="overview"    column="overview"    />
        <result property="source"    column="source"    />
        <result property="views"    column="views"    />
        <result property="hot"    column="hot"    />
        <result property="topType"    column="top_type"    />
        <result property="topOrder"    column="top_order"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPostsVo">
        select p.post_id, p.title, p.category_id, p.status, p.keywords, p.main_image, p.overview, p.source, p.views, 
               p.hot, p.top_type, p.top_order, p.del_flag, p.create_by, p.create_time, p.update_by, p.update_time
        from posts p
    </sql>

    <select id="selectPostsList" parameterType="Posts" resultMap="PostsResult">
        <include refid="selectPostsVo"/>
        <where>
            <if test="title != null  and title != ''"> and p.title = #{title}</if>
            <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
            <if test="status != null "> and p.status = #{status}</if>
            <if test="keywords != null and keywords != ''">
                and (
                    <foreach collection="keywords.split(',')" item="keyword" separator="or">
                        p.keywords like concat('%', #{keyword}, '%')
                        or p.title like concat('%', #{keyword}, '%')
                    </foreach>
                )
            </if>
            <if test="mainImage != null and mainImage != ''"> and p.main_image = #{mainImage}</if>
            <if test="overview != null and overview != ''"> and p.overview like concat('%', #{overview}, '%')</if>
            <if test="source != null and source != ''"> and p.source = #{source}</if>
            <if test="views != null "> and p.views = #{views}</if>
            <if test="hot != null "> and p.hot = #{hot}</if>
            <if test="topType != null "> and p.top_type = #{topType}</if>
            and p.del_flag = '0'
        </where>
        order by 
            case 
                when p.top_type = '1' then 1  -- 全局置顶
                when p.top_type = '2' and p.category_id = #{categoryId} then 2  -- 分类置顶
                else 3  -- 普通文章
            end,
            p.top_order desc,  -- 置顶文章按置顶顺序排序
            p.create_time desc  -- 最后按创建时间倒序
    </select>

    <select id="selectPostsByPostId" parameterType="Long" resultMap="PostsResult">
        <include refid="selectPostsVo"/>
        where p.post_id = #{postId}
    </select>

    <insert id="insertPosts" parameterType="Posts" useGeneratedKeys="true" keyProperty="postId">
        insert into posts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="status != null">status,</if>
            <if test="keywords != null">keywords,</if>
            <if test="mainImage != null">main_image,</if>
            <if test="overview != null">overview,</if>
            <if test="source != null">source,</if>
            <if test="views != null">views,</if>
            <if test="hot != null">hot,</if>
            <if test="topType != null">top_type,</if>
            <if test="topOrder != null">top_order,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="status != null">#{status},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="mainImage != null">#{mainImage},</if>
            <if test="overview != null">#{overview},</if>
            <if test="source != null">#{source},</if>
            <if test="views != null">#{views},</if>
            <if test="hot != null">#{hot},</if>
            <if test="topType != null">#{topType},</if>
            <if test="topOrder != null">#{topOrder},</if>
            '0',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePosts" parameterType="Posts">
        update posts
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="mainImage != null">main_image = #{mainImage},</if>
            <if test="overview != null">overview = #{overview},</if>
            <if test="source != null">source = #{source},</if>
            <if test="views != null">views = #{views},</if>
            <if test="hot != null">hot = #{hot},</if>
            <if test="topType != null">top_type = #{topType},</if>
            <if test="topOrder != null">top_order = #{topOrder},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where post_id = #{postId}
    </update>

    <update id="deletePostsByPostId" parameterType="Long">
        update posts set del_flag = '1' where post_id = #{postId}
    </update>

    <update id="deletePostsByPostIds" parameterType="String">
        update posts set del_flag = '1' where post_id in
        <foreach item="postId" collection="array" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </update>
</mapper>