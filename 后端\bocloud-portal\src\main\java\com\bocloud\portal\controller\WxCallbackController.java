package com.bocloud.portal.controller;

import com.bocloud.portal.business.service.IWxLoginService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/wx/callback")
public class WxCallbackController {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private IWxLoginService wxLoginService;

    private WxMpMessageRouter router;

    @Autowired
    public WxCallbackController(WxMpService wxMpService, IWxLoginService wxLoginService) {
        this.wxMpService = wxMpService;
        this.wxLoginService = wxLoginService;
        
        this.router = new WxMpMessageRouter(wxMpService);
        
        // 处理扫码事件
        this.router.rule()
                .async(false)
                .event("SCAN")
                .handler((wxMessage, context, wxService, sessionManager) -> {
                    String sceneStr = wxMessage.getEventKey();
                    String openId = wxMessage.getFromUser();
                    wxLoginService.handleScanEvent(openId, sceneStr, "SCAN");
                    return null;
                })
                .end();
        
        // 处理关注事件
        this.router.rule()
                .async(false)
                .event("subscribe")
                .handler((wxMessage, context, wxService, sessionManager) -> {
                    String eventKey = wxMessage.getEventKey();
                    String openId = wxMessage.getFromUser();
                    // 扫码关注，eventKey格式为：qrscene_SCENE_STR
                    if (StringUtils.isNotEmpty(eventKey) && eventKey.startsWith("qrscene_")) {
                        String sceneStr = eventKey.substring(8);
                        wxLoginService.handleScanEvent(openId, sceneStr, "subscribe");
                    }
                    return null;
                })
                .end();
    }

    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(@RequestParam(name = "signature") String signature,
                         @RequestParam(name = "timestamp") String timestamp,
                         @RequestParam(name = "nonce") String nonce,
                         @RequestParam(name = "echostr") String echostr) {
        
        log.info("[authGet] 接收到来自微信服务器的认证消息，signature: {}, timestamp: {}, nonce: {}, echostr: {}", 
                signature, timestamp, nonce, echostr);
        
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            log.error("[authGet] 请求参数非法，缺少必要参数");
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        
        boolean isValid = wxMpService.checkSignature(timestamp, nonce, signature);
        log.info("[authGet] 微信签名验证结果: {}", isValid);
        
        if (isValid) {
            log.info("[authGet] 微信认证成功，返回echostr: {}", echostr);
            return echostr;
        }
        
        log.error("[authGet] 微信签名验证失败");
        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(@RequestBody String requestBody,
                      @RequestParam(name = "signature", required = false) String signature,
                      @RequestParam(name = "timestamp", required = false) String timestamp,
                      @RequestParam(name = "nonce", required = false) String nonce,
                      @RequestParam(name = "openid", required = false) String openid,
                      @RequestParam(name = "encrypt_type", required = false) String encType,
                      @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        
        log.info("[post] 接收微信回调消息，openid: {}, signature: {}, timestamp: {}, nonce: {}, encType: {}, requestBody: {}", 
                openid, signature, timestamp, nonce, encType, requestBody);
        
        boolean isValid = wxMpService.checkSignature(timestamp, nonce, signature);
        log.info("[post] 微信签名验证结果: {}", isValid);
        
        if (!isValid) {
            log.error("[post] 微信签名验证失败，可能属于伪造的请求");
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }
        
        String out = null;
        try {
            if (encType == null) {
                // 明文传输的消息
                log.info("[post] 处理明文消息");
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
                log.info("[post] 解析消息成功，消息类型: {}, 事件类型: {}, 用户openid: {}", 
                        inMessage.getMsgType(), inMessage.getEvent(), inMessage.getFromUser());
                
                WxMpXmlOutMessage outMessage = this.route(inMessage);
                if (outMessage == null) {
                    log.info("[post] 无需回复消息");
                    return "";
                }
                out = outMessage.toXml();
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                log.info("[post] 处理加密消息，msgSignature: {}", msgSignature);
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxMpService.getWxMpConfigStorage(),
                        timestamp, nonce, msgSignature);
                log.info("[post] 解密消息成功，消息类型: {}, 事件类型: {}, 用户openid: {}", 
                        inMessage.getMsgType(), inMessage.getEvent(), inMessage.getFromUser());
                
                WxMpXmlOutMessage outMessage = this.route(inMessage);
                if (outMessage == null) {
                    log.info("[post] 无需回复消息");
                    return "";
                }
                out = outMessage.toEncryptedXml(wxMpService.getWxMpConfigStorage());
            }
            
            log.info("[post] 组装回复信息: {}", out);
            return out;
        } catch (Exception e) {
            log.error("[post] 处理微信回调消息失败", e);
            return "";
        }
    }

    private WxMpXmlOutMessage route(WxMpXmlMessage message) {
        try {
            log.info("[route] 开始路由消息，消息类型: {}, 事件类型: {}", message.getMsgType(), message.getEvent());
            WxMpXmlOutMessage outMessage = this.router.route(message);
            log.info("[route] 消息路由完成，outMessage: {}", outMessage);
            return outMessage;
        } catch (Exception e) {
            log.error("[route] 消息路由失败", e);
            return null;
        }
    }
} 