package com.bocloud.domain.web;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教育经历对象 education_experience
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EducationExperience extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 教育经历ID */
    private Long educationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 学校名称 */
    @Excel(name = "学校名称")
    private String schoolName;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 学历 */
    @Excel(name = "学历")
    private String degree;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    private String endTime;

    /** 描述 */
    @Excel(name = "描述")
    private String description;
} 