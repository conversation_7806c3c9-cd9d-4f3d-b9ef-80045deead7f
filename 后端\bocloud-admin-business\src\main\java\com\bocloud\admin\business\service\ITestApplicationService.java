package com.bocloud.admin.business.service;

import com.bocloud.domain.system.test_application.TestApplication;
import com.bocloud.domain.system.test_application.TestApplicationAuditRequest;

import java.util.List;

/**
 * 检测申请Service接口
 *
 * @date 2025-03-12
 */
public interface ITestApplicationService
{
    /**
     * 查询检测申请
     *
     * @param id 检测申请主键
     * @return 检测申请
     */
    public TestApplication selectTestApplicationById(Long id);

    /**
     * 查询检测申请列表
     *
     * @param testApplication 检测申请
     * @return 检测申请集合
     */
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication);

    /**
     * 新增检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    public int insertTestApplication(TestApplication testApplication);

    /**
     * 修改检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    public int updateTestApplication(TestApplication testApplication);

    /**
     * 批量删除检测申请
     *
     * @param ids 需要删除的检测申请主键集合
     * @return 结果
     */
    public int deleteTestApplicationByIds(Long[] ids);

    /**
     * 删除检测申请信息
     *
     * @param id 检测申请主键
     * @return 结果
     */
    public int deleteTestApplicationById(Long id);

    /**
     * 审核检测申请
     *
     * @param request 审核请求信息
     * @return 结果
     */
    public int auditTestApplication(TestApplicationAuditRequest request);

    /**
     * 审核付款凭证
     *
     * @param testApplication 审核信息
     * @return 结果
     */
    int auditPayment(TestApplication testApplication);
}
