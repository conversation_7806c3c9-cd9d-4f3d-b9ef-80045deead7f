package com.bocloud.admin.business.service;

import java.util.List;

import com.bocloud.domain.web.ExpertInfo;

public interface IExpertService {

    List<ExpertInfo> list(ExpertInfo expertInfo);

    /**
     * 根据专家ID查询专家详情
     */
    ExpertInfo getExpertDetailByExpertId(Long expertId);

    /**
     * 更新专家信息
     */
    int updateExpertInfo(ExpertInfo expertInfo);

    /**
     * 审核专家
     */
    void auditExpert(ExpertInfo expertInfo);
}
