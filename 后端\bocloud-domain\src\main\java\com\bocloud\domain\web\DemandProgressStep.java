package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;

import lombok.Data;

@Data
public class DemandProgressStep extends BaseEntity {

    /** 主键ID */
    private Long id;

    /** 进度详情ID */
    private Long demandProgressDetailId;

    /** 需求ID */
    private Long demandId;

    /** 阶段名称 */
    private String title;

    /** 阶段日期 */
    private String date;

    /** 阶段状态：done/current/pending */
    private String status;

    /** 阶段描述 */
    private String desc;
} 