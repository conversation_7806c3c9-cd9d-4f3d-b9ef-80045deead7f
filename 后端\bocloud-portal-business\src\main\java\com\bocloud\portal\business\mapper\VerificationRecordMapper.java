package com.bocloud.portal.business.mapper;

import com.bocloud.domain.web.VerificationRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 认证记录Mapper接口
 */
@Mapper
public interface VerificationRecordMapper {
    /**
     * 根据用户ID查询认证记录
     * @param userId 用户ID
     * @return 认证记录
     */
    VerificationRecord selectVerificationRecordByUserId(Long userId);

    int insertVerificationRecord(VerificationRecord verificationRecord);

    int updateVerificationRecord(VerificationRecord verificationRecord);
} 