package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.EnterpriseInfoMapper;
import com.bocloud.admin.business.service.IEnterpriseInfoService;
import com.bocloud.domain.web.EnterpriseInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EnterpriseInfoServiceImpl implements IEnterpriseInfoService {

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Override
    public EnterpriseInfo selectActiveByUserId(Long userId) {
        return enterpriseInfoMapper.selectActiveByUserId(userId);
    }

    @Override
    public EnterpriseInfo selectLatestByUserIdAndStatus(Long userId, String status) {
        return enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, status);
    }

    @Override
    public EnterpriseInfo selectBestByUserId(Long userId) {
        // 1. 优先查待审核
        EnterpriseInfo pending = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, "0");
        if (pending != null) {
            return pending;
        }
        // 2. 查已通过且有效
        EnterpriseInfo approved = enterpriseInfoMapper.selectActiveByUserId(userId);
        if (approved != null) {
            return approved;
        }
        // 3. 查最新未通过
        EnterpriseInfo rejected = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, "2");
        if (rejected != null) {
            return rejected;
        }
        // 4. 都没有
        return null;
    }
} 