package com.bocloud.common.enums;

/**
 * 信息完善状态枚举
 * 
 * <AUTHOR>
 */
public enum InfoStatus {
    
    INCOMPLETE("-1", "未完善"),
    PENDING_REVIEW("0", "待审核"),
    NORMAL("1", "已完善");
    
    private final String code;
    private final String desc;
    
    InfoStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static InfoStatus getByCode(String code) {
        for (InfoStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        InfoStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断信息是否完善
     */
    public static boolean isComplete(String code) {
        return NORMAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为待审核状态
     */
    public static boolean isPending(String code) {
        return PENDING_REVIEW.getCode().equals(code);
    }
    
    /**
     * 判断信息是否未完善
     */
    public static boolean isIncomplete(String code) {
        return INCOMPLETE.getCode().equals(code);
    }
} 