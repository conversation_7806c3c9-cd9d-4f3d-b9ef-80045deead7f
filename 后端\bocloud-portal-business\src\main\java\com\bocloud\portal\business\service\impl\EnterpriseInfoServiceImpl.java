package com.bocloud.portal.business.service.impl;

import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.EnterpriseInfoStatus;
import com.bocloud.common.utils.bean.BeanUtils;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.domain.web.dto.EnterpriseVerificationDTO;
import com.bocloud.portal.business.mapper.EnterpriseInfoMapper;
import com.bocloud.portal.business.service.IEnterpriseInfoService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.IVerificationRecordService;
import com.bocloud.portal.business.service.ISmsService;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 企业信息服务实现
 */
@Service
public class EnterpriseInfoServiceImpl implements IEnterpriseInfoService {

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    private IVerificationRecordService verificationRecordService;

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    private ISmsService smsService;

    @Override
    public EnterpriseInfo getEnterpriseInfo(Long userId) {
        // 1. 查找待审核
        EnterpriseInfo pending = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId,
                EnterpriseInfoStatus.STATUS_PENDING.getCode());
        if (pending != null) {
            return pending;
        }
        // 2. 查找已通过且有效
        EnterpriseInfo approved = enterpriseInfoMapper.selectActiveByUserId(userId);
        if (approved != null) {
            return approved;
        }
        // 3. 查找最新未通过
        EnterpriseInfo rejected = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId,
                EnterpriseInfoStatus.STATUS_REJECTED.getCode());
        if (rejected != null) {
            return rejected;
        }
        // 4. 都没有
        return null;
    }

    @Override
    public EnterpriseInfo getEnterpriseInfoByStatus(Long userId, String status) {
        return enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, status);
    }

    @Override
    public int updateEnterpriseInfo(EnterpriseInfo enterpriseInfo) {
        // 从当前登录用户获取用户ID，确保安全性
        Long userId = SecurityUtils.getUserId();

        // 获取当前有效的企业信息
        EnterpriseInfo currentInfo = enterpriseInfoMapper.selectActiveByUserId(userId);

        enterpriseInfo.setUpdateBy(SecurityUtils.getUsername());

        if (currentInfo == null) {
            // 没有当前有效信息，插入新记录
            enterpriseInfo.setUserId(userId);
            enterpriseInfo.setCreateBy(SecurityUtils.getUsername());
            // 可根据业务需要设置初始状态、active等
            enterpriseInfo.setStatus(EnterpriseInfoStatus.STATUS_NORMAL.getCode());
            enterpriseInfo.setActive(EnterpriseInfoStatus.ACTIVE_YES.getCode());
            enterpriseInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());  
            return enterpriseInfoMapper.insertEnterpriseInfo(enterpriseInfo);
        } else {
            // 有当前有效信息，更新
            enterpriseInfo.setEnterpriseId(currentInfo.getEnterpriseId());

            // 清空认证相关字段，防止误更新
            enterpriseInfo.setCompanyName(null);
            enterpriseInfo.setCreditCode(null);
            enterpriseInfo.setLegalPersonName(null);
            enterpriseInfo.setLegalPersonIdNumber(null);
            enterpriseInfo.setBusinessLicense(null);
            enterpriseInfo.setLegalPersonIdCardPortrait(null);
            enterpriseInfo.setLegalPersonIdCardEmblem(null);

            return enterpriseInfoMapper.updateEnterpriseInfo(enterpriseInfo);
        }
    }

    /**
     * 判断是否为认证信息更新
     */
    private boolean isVerificationUpdate(EnterpriseInfo newInfo, EnterpriseInfo currentInfo) {
        // 检查关键认证字段是否有变化
        return !equals(newInfo.getCompanyName(), currentInfo.getCompanyName()) ||
                !equals(newInfo.getCreditCode(), currentInfo.getCreditCode()) ||
                !equals(newInfo.getLegalPersonName(), currentInfo.getLegalPersonName()) ||
                !equals(newInfo.getLegalPersonIdNumber(), currentInfo.getLegalPersonIdNumber()) ||
                !equals(newInfo.getBusinessLicense(), currentInfo.getBusinessLicense()) ||
                !equals(newInfo.getLegalPersonIdCardPortrait(), currentInfo.getLegalPersonIdCardPortrait()) ||
                !equals(newInfo.getLegalPersonIdCardEmblem(), currentInfo.getLegalPersonIdCardEmblem());
    }

    /**
     * 安全的字符串比较
     */
    private boolean equals(String str1, String str2) {
        if (str1 == null)
            return str2 == null;
        return str1.equals(str2);
    }

    /**
     * 
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitEnterpriseVerification(Long userId, EnterpriseVerificationDTO verificationDTO) {
        // 0. 验证短信验证码
        smsService.checkSmsCode(verificationDTO.getLegalPersonPhone(), verificationDTO.getLegalPersonPhoneCode(),
                verificationDTO.getSmsUuid());

        // 1. 检查是否已有待审核的认证信息
        EnterpriseInfo pending = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId,
                EnterpriseInfoStatus.STATUS_PENDING.getCode());
        if (pending != null) {
            throw new ServiceException("您已有待审核的认证信息，请勿重复提交");
        }

        // 1. 更新portal user表的状态
        PortalUser portalUser = portalUserService.selectPortalUserById(userId);
        portalUser.setVerificationStatus(VerificationStatus.REVIEWING.getCode());
        // 认证类型是 企业
        portalUser.setVerificationType(VerificationType.ENTERPRISE.getCode());
        portalUserService.updatePortalUser(portalUser);

        // 2. 转换为EnterpriseInfo对象
        EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectActiveByUserId(userId);
        if (enterpriseInfo == null) {
            enterpriseInfo = new EnterpriseInfo();
            enterpriseInfo.setUserId(userId);
        }
        BeanUtils.copyProperties(verificationDTO, enterpriseInfo);
        // 3. 设置用户ID和创建人信息
        enterpriseInfo.setUserId(userId);
        // 4. 设置认证状态为待审核
        enterpriseInfo.setStatus(EnterpriseInfoStatus.STATUS_PENDING.getCode());
        // 5. 设置删除标志为未删除
        enterpriseInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        // 6. 设置是否当前有效为否
        enterpriseInfo.setActive(EnterpriseInfoStatus.ACTIVE_NO.getCode());
        // 7. 保存认证信息
        int rows = enterpriseInfoMapper.insertEnterpriseInfo(enterpriseInfo);
        if (rows <= 0) {
            throw new ServiceException("提交认证信息失败");
        }
        // 7. 创建认证状态记录
        VerificationRecord verificationRecord = new VerificationRecord();
        verificationRecord.setUserId(userId);
        verificationRecord.setVerificationType(VerificationType.ENTERPRISE.getCode());
        verificationRecord.setStatus(VerificationStatus.REVIEWING.getCode());
        verificationRecord.setCreateTime(new java.util.Date());
        verificationRecord.setName(enterpriseInfo.getCompanyName());
        verificationRecord.setIdCardNumber(enterpriseInfo.getLegalPersonIdNumber());
        verificationRecord.setLegalPersonName(enterpriseInfo.getLegalPersonName());
        verificationRecord.setCreditCode(enterpriseInfo.getCreditCode());
        verificationRecord.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        verificationRecordService.insertVerificationRecord(verificationRecord);
        // 成功直接 return
        return;
    }
}