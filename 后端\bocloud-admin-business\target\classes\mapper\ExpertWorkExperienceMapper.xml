<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.ExpertWorkExperienceMapper">
    <resultMap type="com.bocloud.domain.web.ExpertWorkExperience" id="ExpertWorkExperienceResult">
        <id property="id" column="id"/>
        <result property="expertId" column="expert_id"/>
        <result property="companyName" column="company_name"/>
        <result property="position" column="position"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <select id="selectExpertWorkExperienceList" parameterType="long" resultMap="ExpertWorkExperienceResult">
        select * from expert_work_experience where expert_id = #{expertId} and del_flag = '0'
    </select>
</mapper> 