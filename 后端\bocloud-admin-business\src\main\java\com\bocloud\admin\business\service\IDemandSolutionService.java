package com.bocloud.admin.business.service;

import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.DemandSolutionAuditRequest;
import java.util.List;

/**
 * 需求方案Service接口
 */
public interface IDemandSolutionService {
    /**
     * 查询方案列表
     * 
     * @param solution 查询条件
     * @return 方案列表
     */
    List<DemandSolution> selectDemandSolutionList(DemandSolution solution);

    /**
     * 查询方案详细信息
     * 
     * @param solutionId 方案ID
     * @return 方案信息
     */
    DemandSolution selectDemandSolutionById(Long solutionId);

    /**
     * 更新方案
     * 
     * @param solution 方案信息
     * @return 结果
     */
    int updateDemandSolution(DemandSolution solution);

    /**
     * 审核方案，包含方案和需求状态联动
     * @param request 审核请求
     * @param auditUserId 审核人ID
     * @param auditUserName 审核人姓名
     * @return 影响行数
     */
    int auditSolution(DemandSolutionAuditRequest request, Long auditUserId, String auditUserName);
} 