package com.bocloud.domain.web;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量邀请请求实体
 */
@Data
public class BatchInviteRequest {
    
    /** 需求ID */
    @NotNull(message = "需求ID不能为空")
    private Long demandId;

    /** 用户ID列表 */
    @NotEmpty(message = "用户ID列表不能为空")
    private List<Long> userIds;

    /** 备注信息 */
    private String remark;

} 