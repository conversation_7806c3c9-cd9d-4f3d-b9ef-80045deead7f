package com.bocloud.portal.business.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;

import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.portal.business.mapper.DemandSolutionMapper;
import com.bocloud.portal.business.service.IDemandSolutionService;
import com.bocloud.portal.business.service.ICommonAttachmentService;
import com.bocloud.portal.business.service.IDemandSolutionInviteService;
import org.springframework.util.CollectionUtils;
import com.bocloud.portal.business.mapper.EnterpriseDemandMapper;

/**
 * 需求方案Service业务层处理
 */
@Service
public class DemandSolutionServiceImpl implements IDemandSolutionService {
    
    private static final Logger log = LoggerFactory.getLogger(DemandSolutionServiceImpl.class);
    
    @Autowired
    private DemandSolutionMapper demandSolutionMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;

    @Autowired
    private IDemandSolutionInviteService demandSolutionInviteService;

    /**
     * 查询需求方案
     * 
     * @param solutionId 需求方案主键
     * @return 需求方案
     */
    @Override
    public DemandSolution selectDemandSolutionById(Long solutionId) {
        return demandSolutionMapper.selectDemandSolutionById(solutionId);
    }

    /**
     * 查询需求方案列表
     * 
     * @param solution 需求方案
     * @return 需求方案
     */
    @Override
    public List<DemandSolution> selectDemandSolutionList(DemandSolution solution) {
        return demandSolutionMapper.selectDemandSolutionList(solution);
    }

    /**
     * 获取下一个方案编号
     * 
     * @param prefix 前缀 (例如 "SOLUTION-yyyyMMdd-")
     * @return 当天最大序号 + 1
     */
    private synchronized int getNextSolutionSeq(String prefix) {
        DemandSolution query = new DemandSolution();
        query.setSolutionNo(prefix + "%");
        List<DemandSolution> list = demandSolutionMapper.selectDemandSolutionList(query);
        int maxSeq = 0;
        if (!CollectionUtils.isEmpty(list)) {
            for (DemandSolution solution : list) {
                String solutionNo = solution.getSolutionNo();
                if (solutionNo != null && solutionNo.startsWith(prefix)) {
                    try {
                        int seq = Integer.parseInt(solutionNo.substring(prefix.length()));
                        maxSeq = Math.max(maxSeq, seq);
                    } catch (NumberFormatException e) {
                        // 忽略无法解析为数字的序号
                    }
                }
            }
        }
        return maxSeq + 1;
    }

    /**
     * 新增需求方案（包含附件）
     * 
     * @param demandSolution 需求方案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDemandSolutionWithAttachments(DemandSolution demandSolution) {
        // 1. 数据验证
        if (demandSolution.getDemandId() == null) {
            throw new RuntimeException("需求ID不能为空");
        }
        
        if (demandSolution.getPortalUserId() == null) {
            throw new RuntimeException("提交者ID不能为空");
        }
        
        if (demandSolution.getSolutionTitle() == null || demandSolution.getSolutionTitle().trim().isEmpty()) {
            throw new RuntimeException("方案标题不能为空");
        }
        
        // 验证需求是否存在
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandSolution.getDemandId());
        if (demand == null) {
            throw new RuntimeException("需求不存在");
        }
        
        // 验证需求状态是否允许提交方案
        if (!EnterpriseDemandStatus.PENDING.getCode().equals(demand.getStatus()) 
            && !EnterpriseDemandStatus.WAIT_PUBLIC.getCode().equals(demand.getStatus())
            && !EnterpriseDemandStatus.BIDDING.getCode().equals(demand.getStatus())) {
            throw new RuntimeException("该需求当前状态不允许提交方案");
        }
        
        // 检查是否已经为该需求提交过方案（同一用户）
        DemandSolution existingQuery = new DemandSolution();
        existingQuery.setDemandId(demandSolution.getDemandId());
        existingQuery.setPortalUserId(demandSolution.getPortalUserId());
        List<DemandSolution> existingSolutions = demandSolutionMapper.selectDemandSolutionList(existingQuery);
        // 只要有不是已驳回的方案，不允许重复提交
        boolean hasNonRejected = existingSolutions.stream().anyMatch(s -> !DemandSolutionStatus.REJECTED.getCode().equals(s.getStatus()));
        if (hasNonRejected) {
            throw new RuntimeException("您已经为该需求提交过方案，不能重复提交");
        }
        
        // 2. 设置基础字段
        demandSolution.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode()); // 设置删除标志为0（存在）
        demandSolution.setStatus(DemandSolutionStatus.PENDING.getCode()); // 设置初始状态为待审核
        
        // 3. 生成方案编号
        String datePrefix = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prefix = "SOLUTION-" + datePrefix + "-";
        int seq = getNextSolutionSeq(prefix);
        demandSolution.setSolutionNo(prefix + String.format("%04d", seq));

        // 4. 设置需求编号
        demandSolution.setDemandNo(demand.getDemandNo());

        // 5. 保存方案基本信息
        int rows = demandSolutionMapper.insertDemandSolution(demandSolution);
        if (rows > 0) {
            // 保存附件信息
            if (!CollectionUtils.isEmpty(demandSolution.getAttachments())) {
                for (CommonAttachment commonAttachment : demandSolution.getAttachments()) {
                    try {
                        // 设置业务ID和业务类型
                        commonAttachment.setBusinessId(demandSolution.getSolutionId());
                        commonAttachment.setBusinessType(CommonAttachmentBusinessType.DEMAND_SOLUTION);
                        commonAttachment.setUploadBy(demandSolution.getPortalUserId());
                        commonAttachment.setFileSize(commonAttachment.getFileSize());
                        // 保存附件信息
                        attachmentService.insertCommonAttachment(commonAttachment);
                    } catch (Exception e) {
                        throw new RuntimeException("附件保存失败: " + e.getMessage(), e);
                    }
                }
            }
            
            // 如果有邀请ID，更新邀请记录状态为已提交
            if (demandSolution.getInviteId() != null) {
                try {
                    demandSolutionInviteService.submitDemandSolutionInvite(demandSolution.getInviteId());
                    log.info("方案提交成功，邀请状态已更新为已提交，邀请ID: {}", demandSolution.getInviteId());
                } catch (Exception e) {
                    // 邀请状态更新失败不影响方案提交，只记录日志
                    log.error("更新邀请状态失败，邀请ID: {}, 错误信息: {}", demandSolution.getInviteId(), e.getMessage(), e);
                }
            }
        }
        return rows;
    }

    /**
     * 修改需求方案（包含附件）
     * 
     * @param demandSolution 需求方案
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDemandSolutionWithAttachments(DemandSolution demandSolution) {
        // 更新方案基本信息
        // 更新状态
        demandSolution.setStatus(DemandSolutionStatus.PENDING.getCode());
        int rows = demandSolutionMapper.updateDemandSolution(demandSolution);
        
        if (rows > 0 && !CollectionUtils.isEmpty(demandSolution.getAttachments())) {
            // 删除原有附件
            attachmentService.deleteAttachmentByBusiness(demandSolution.getSolutionId(), CommonAttachmentBusinessType.DEMAND_SOLUTION);
            
            // 保存新的附件信息
            for (CommonAttachment commonAttachment : demandSolution.getAttachments()) {
                try {
                    // 设置业务ID和业务类型
                    commonAttachment.setBusinessId(demandSolution.getSolutionId());
                    commonAttachment.setBusinessType(CommonAttachmentBusinessType.DEMAND_SOLUTION);
                    commonAttachment.setUploadBy(demandSolution.getPortalUserId());
                    commonAttachment.setFileSize(commonAttachment.getFileSize());
                    
                    attachmentService.insertCommonAttachment(commonAttachment);
                } catch (Exception e) {
                    throw new RuntimeException("附件保存失败: " + e.getMessage(), e);
                }
            }
        }
        return rows;
    }

    /**
     * 批量删除需求方案
     * 
     * @param solutionIds 需要删除的需求方案主键
     * @return 结果
     */
    @Override
    public int deleteDemandSolutionByIds(Long[] solutionIds) {
        return demandSolutionMapper.deleteDemandSolutionByIds(solutionIds);
    }

    /**
     * 删除需求方案信息
     * 
     * @param solutionId 需求方案主键
     * @return 结果
     */
    @Override
    public int deleteDemandSolutionById(Long solutionId) {
        return demandSolutionMapper.deleteDemandSolutionById(solutionId);
    }

    @Override
    public int updateDemandSolution(DemandSolution demandSolution) {
        return demandSolutionMapper.updateDemandSolution(demandSolution);
    }

    @Override
    public DemandSolution getMySolutionDetailWithAttachments(Long solutionId, Long userId) {
        DemandSolution solution = demandSolutionMapper.selectDemandSolutionById(solutionId);
        if (solution == null) {
            throw new ServiceException("方案不存在");
        }
        
        // 验证权限：方案创建者或需求发起人可以查看
        boolean isSolutionCreator = solution.getPortalUserId().equals(userId);
        boolean isDemandOwner = false;
        
        if (!isSolutionCreator) {
            // 如果不是方案创建者，检查是否是需求发起人
            EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(solution.getDemandId());
            if (demand != null) {
                isDemandOwner = demand.getPortalUserId().equals(userId);
            }
        }
        
        if (!isSolutionCreator && !isDemandOwner) {
            throw new ServiceException("无权访问该方案");
        }
        
        // 查询附件
        CommonAttachment query = new CommonAttachment();
        query.setBusinessType(CommonAttachmentBusinessType.DEMAND_SOLUTION);
        query.setBusinessId(solutionId);
        List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
        solution.setAttachments(attachments);
        return solution;
    }
} 