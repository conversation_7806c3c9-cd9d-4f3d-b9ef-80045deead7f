package com.bocloud.common.core.domain.model;

import lombok.Data;

/**
 * 用户登录对象
 * 
 * <AUTHOR>
 */
@Data
public class LoginBody
{
    /**
     * 用户名
     */
    private String username;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机验证码
     */
    private String smsCode;

    /**
     * 手机验证码uuid
     */
    private String smsUuid;

    /**
     * 小程序openId（用于小程序短信验证码登录时的用户合并）
     */
    private String openId;
}
