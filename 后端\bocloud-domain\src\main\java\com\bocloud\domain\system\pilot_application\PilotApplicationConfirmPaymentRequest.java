package com.bocloud.domain.system.pilot_application;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中试申请确认收款请求对象
 * 
 * <AUTHOR>
 */
@Data
public class PilotApplicationConfirmPaymentRequest
{
    /** 中试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long pilotApplicationId;

    /** 实际确认收款金额 */
    @NotNull(message = "确认收款金额不能为空")
    private BigDecimal confirmedAmount;

    /** 确认收款备注 */
    private String confirmPaymentRemark;
} 