package com.bocloud.portal.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.portal.business.mapper.ExpertWorkExperienceMapper;
import com.bocloud.domain.web.ExpertWorkExperience;
import com.bocloud.portal.business.service.IExpertWorkExperienceService;

/**
 * 专家工作履历Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ExpertWorkExperienceServiceImpl implements IExpertWorkExperienceService {
    
    @Autowired
    private ExpertWorkExperienceMapper expertWorkExperienceMapper;


    /**
     * 批量新增专家工作履历
     * 
     * @param expertWorkExperienceList 专家工作履历列表
     * @return 结果
     */
    @Override
    public int insertExpertWorkExperiences(List<ExpertWorkExperience> expertWorkExperienceList) {
        return expertWorkExperienceMapper.insertExpertWorkExperiences(expertWorkExperienceList);
    }

    /**
     * 批量更新专家工作履历
     * 
     * @param expertWorkExperienceList 专家工作履历列表
     * @return 结果
     */
    @Override
    public int updateExpertWorkExperiences(List<ExpertWorkExperience> expertWorkExperienceList) {
        int rows = 0;
        for (ExpertWorkExperience experience : expertWorkExperienceList) {
            rows += expertWorkExperienceMapper.updateExpertWorkExperience(experience);
        }
        return rows;
    }

    /**
     * 根据专家ID删除工作履历
     * 
     * @param expertId 专家ID
     * @return 结果
     */
    @Override
    public int deleteExpertWorkExperiencesByExpertId(Long expertId) {
        return expertWorkExperienceMapper.deleteExpertWorkExperiencesByExpertId(expertId);
    }

} 