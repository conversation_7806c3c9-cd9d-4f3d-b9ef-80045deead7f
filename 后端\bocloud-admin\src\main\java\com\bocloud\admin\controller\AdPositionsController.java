package com.bocloud.admin.controller;

import com.bocloud.common.annotation.Log;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import  com.bocloud.domain.system.platform_service.AdPositions;
import com.bocloud.admin.business.service.IAdPositionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 广告位置信息Controller
 *
 * @date 2025-03-11
 */
@RestController
@RequestMapping("/system/adspositions")
public class AdPositionsController extends BaseController
{
    @Autowired
    private IAdPositionsService adPositionsService;

    /**
     * 查询广告位置信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:positions:list')")
    @GetMapping("/list")
    public AjaxResult list(AdPositions adPositions)
    {
        List<AdPositions> list = adPositionsService.selectAdPositionsList(adPositions);
        return success(list);
    }

    /**
     * 导出广告位置信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:positions:export')")
    @Log(title = "广告位置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdPositions adPositions)
    {
        List<AdPositions> list = adPositionsService.selectAdPositionsList(adPositions);
        ExcelUtil<AdPositions> util = new ExcelUtil<AdPositions>(AdPositions.class);
        util.exportExcel(response, list, "广告位置信息数据");
    }

    /**
     * 获取广告位置信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:positions:query')")
    @GetMapping(value = "/{positionId}")
    public AjaxResult getInfo(@PathVariable("positionId") Long positionId)
    {
        return success(adPositionsService.selectAdPositionsByPositionId(positionId));
    }

    /**
     * 新增广告位置信息
     */
    @PreAuthorize("@ss.hasPermi('system:positions:add')")
    @Log(title = "广告位置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdPositions adPositions)
    {
        return toAjax(adPositionsService.insertAdPositions(adPositions));
    }

    /**
     * 修改广告位置信息
     */
    @PreAuthorize("@ss.hasPermi('system:positions:edit')")
    @Log(title = "广告位置信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdPositions adPositions)
    {
        return toAjax(adPositionsService.updateAdPositions(adPositions));
    }

    /**
     * 删除广告位置信息
     */
    @PreAuthorize("@ss.hasPermi('system:positions:remove')")
    @Log(title = "广告位置信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{positionIds}")
    public AjaxResult remove(@PathVariable Long[] positionIds)
    {
        return toAjax(adPositionsService.deleteAdPositionsByPositionIds(positionIds));
    }
}