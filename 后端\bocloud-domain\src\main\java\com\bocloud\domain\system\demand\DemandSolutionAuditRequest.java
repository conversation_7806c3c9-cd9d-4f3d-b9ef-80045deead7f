package com.bocloud.domain.system.demand;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 需求方案审核请求参数
 */
@Data
public class DemandSolutionAuditRequest {
    
    /** 方案ID */
    @NotNull(message = "方案ID不能为空")
    private Long solutionId;

    /** 审核结果(0:通过, 1:驳回) */
    @NotNull(message = "审核结果不能为空")
    private String auditResult;

    /** 审核意见 */
    private String reviewComment;
} 