package com.bocloud.portal.controller.membercenter;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.portal.business.service.IDemandSolutionService;
import com.bocloud.portal.business.service.IEnterpriseDemandService;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.portal.business.service.ICommonAttachmentService;

/**
 * 需求方案Controller
 */
@RestController
@RequestMapping("/membercenter/solution")
public class MemberCenterSolutionController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MemberCenterSolutionController.class);

    @Autowired
    private IDemandSolutionService demandSolutionService;

    @Autowired
    private ICommonAttachmentService attachmentService;

    @Autowired
    private IEnterpriseDemandService demandService;

    /**
     * 查询需求方案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DemandSolution solution) {
        startPage();
        // 只能查看自己提交的方案
        solution.setPortalUserId(SecurityUtils.getUserId());
        List<DemandSolution> list = demandSolutionService.selectDemandSolutionList(solution);
        
        return getDataTable(list);
    }

    /**
     * 获取需求方案详细信息
     */
    @GetMapping(value = "/{solutionId}")
    public AjaxResult getInfo(@PathVariable("solutionId") Long solutionId) {
        try {
            DemandSolution solution = demandSolutionService.getMySolutionDetailWithAttachments(solutionId, SecurityUtils.getUserId());
            return AjaxResult.success(solution);
        } catch (Exception e) {
            logger.error("获取方案详情失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 新增需求方案
     */
    @PostMapping(value = "add")
    public AjaxResult add(@RequestBody DemandSolution solution) {
        try {
            // 设置提交者ID
            solution.setPortalUserId(SecurityUtils.getUserId());
            // 设置初始状态为待审核
            solution.setStatus(DemandSolutionStatus.PENDING.getCode());
            
            // 调用service层方法处理业务逻辑和事务
            int rows = demandSolutionService.insertDemandSolutionWithAttachments(solution);
            if (rows > 0) {
                // 查询并返回创建的方案信息（不包含附件信息）
                DemandSolution createdSolution = demandSolutionService.selectDemandSolutionById(solution.getSolutionId());
                return AjaxResult.success("方案提交成功，请等待审核", createdSolution);
            }
            return AjaxResult.error("方案提交失败，请稍后重试");
        } catch (Exception e) {
            logger.error("保存需求方案失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改需求方案
     */
    @PutMapping(value = "edit")
    public AjaxResult edit(@RequestBody DemandSolution solution) {
        try {
            // 验证是否是自己的方案
            DemandSolution original = demandSolutionService.selectDemandSolutionById(solution.getSolutionId());
            if (original == null || !original.getPortalUserId().equals(SecurityUtils.getUserId())) {
                return AjaxResult.error("无权修改该方案");
            }

            // 待审核 已驳回 未中标 可以修改
            if (!DemandSolutionStatus.PENDING.getCode().equals(original.getStatus()) && !DemandSolutionStatus.REJECTED.getCode().equals(original.getStatus()) 
            && !DemandSolutionStatus.BID_LOSE.getCode().equals(original.getStatus())) {
                return AjaxResult.error("只有待审核、已驳回、未中标的方案可以修改");
            }

            // 调用service层方法处理业务逻辑和事务
            return toAjax(demandSolutionService.updateDemandSolutionWithAttachments(solution));
        } catch (Exception e) {
            logger.error("更新需求方案失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除需求方案
     */
    @DeleteMapping("/{solutionIds}")
    public AjaxResult remove(@PathVariable Long[] solutionIds) {
        // 验证是否是自己的方案
        for (Long solutionId : solutionIds) {
            DemandSolution solution = demandSolutionService.selectDemandSolutionById(solutionId);
            if (solution == null || !solution.getPortalUserId().equals(SecurityUtils.getUserId())) {
                return AjaxResult.error("无权删除方案ID为" + solutionId + "的方案");
            }
            // 删除方案的附件
            attachmentService.deleteAttachmentByBusiness(solutionId, "demand_solution");
        }
        
        // 删除方案
        return toAjax(demandSolutionService.deleteDemandSolutionByIds(solutionIds));
    }

    /**
     * 删除方案附件
     */
    @DeleteMapping("/attachment/{attachmentId}")
    public AjaxResult removeAttachment(@PathVariable Long attachmentId) {
        // 验证附件所属方案是否是自己的
        CommonAttachment attachment = attachmentService.selectCommonAttachmentById(attachmentId);
        if (attachment == null || !"demand_solution".equals(attachment.getBusinessType())) {
            return AjaxResult.error("附件不存在或类型错误");
        }
        
        DemandSolution solution = demandSolutionService.selectDemandSolutionById(attachment.getBusinessId());
        if (solution == null || !solution.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权删除该附件");
        }
        
        return toAjax(attachmentService.deleteCommonAttachmentById(attachmentId));
    }
} 