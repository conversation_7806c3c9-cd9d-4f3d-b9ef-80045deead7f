package com.bocloud.admin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.common.utils.poi.ExcelUtil;
import com.bocloud.common.core.page.TableDataInfo;

/**
 * 系统审核记录Controller
 */
@RestController
@RequestMapping("/system/audit")
public class SysAuditController extends BaseController {
    @Autowired
    private ISysAuditService sysAuditService;

    /**
     * 查询审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:audit:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAudit sysAudit) {
        startPage();
        List<SysAudit> list = sysAuditService.selectSysAuditList(sysAudit);
        return getDataTable(list);
    }

    /**
     * 导出审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:audit:export')")
    @Log(title = "审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAudit sysAudit) {
        List<SysAudit> list = sysAuditService.selectSysAuditList(sysAudit);
        ExcelUtil<SysAudit> util = new ExcelUtil<SysAudit>(SysAudit.class);
        util.exportExcel(response, list, "审核记录数据");
    }

    /**
     * 获取审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:audit:query')")
    @GetMapping(value = "/{auditId}")
    public AjaxResult getInfo(@PathVariable("auditId") Long auditId) {
        return success(sysAuditService.selectSysAuditById(auditId));
    }

} 