package com.bocloud.domain.web;

import java.util.Date;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 认证记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VerificationRecord extends BaseEntity {
    
    /** 认证ID */
    private Long id;
    
    /** 用户ID */
    private Long userId;

    /** 认证名称 */
    private String name;

    /** 证件号码 */
    private String idCardNumber;
    
    /** 认证类型（1-个人认证，2-企业认证） */
    private String verificationType;
    
    /** 认证状态（1-待认证，2-审核中，3-已通过，-1-已拒绝） */
    private String status;
    
    /** 拒绝原因 */
    private String rejectReason;

    /** 法人姓名（企业认证时） */
    private String legalPersonName;

    /** 统一社会信用代码（企业认证时） */
    private String creditCode;

    /** 申请时间 */
    private Date createTime;

    /** 完成时间 */
    private Date finishTime;
    
    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

}
