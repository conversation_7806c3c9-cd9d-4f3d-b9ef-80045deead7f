package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.achievement.Achievements;

import java.util.List;

public interface AchievementsMapper {
    /**
     * 查询成果发布
     *
     * @param achievementId 成果发布主键
     * @return 成果发布
     */
    public Achievements selectAchievementsByAchievementId(Long achievementId);

    /**
     * 查询成果发布列表
     *
     * @param Achievements 成果发布
     * @return 成果发布集合
     */
    public List<Achievements> selectAchievementsList(Achievements Achievements);

    /**
     * 更新成果浏览次数
     *
     * @param achievementId 成果ID
     * @return 结果
     */
    public int updateAchievementViews(Long achievementId);

    /**
     * 更新成果收藏次数+1
     *
     * @param achievementId 成果ID
     * @return 结果
     */
    public int updateAchievementAddCollections(Long achievementId);
    /**
     * 更新成果收藏次数-1
     *
     * @param achievementId 成果ID
     * @return 结果
     */
    public int updateAchievementMinusCollections(Long achievementId);
}
