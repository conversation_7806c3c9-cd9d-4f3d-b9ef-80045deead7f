<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.ExpertPaperMapper">
    <resultMap type="com.bocloud.domain.web.ExpertPaper" id="ExpertPaperResult">
        <id property="id" column="id"/>
        <result property="expertId" column="expert_id"/>
        <result property="title" column="title"/>
        <result property="journal" column="journal"/>
        <result property="publishDate" column="publish_date"/>
        <result property="authors" column="authors"/>
        <result property="abstractText" column="abstract_text"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectExpertPaperList" parameterType="long" resultMap="ExpertPaperResult">
        select * from expert_paper where expert_id = #{expertId} and del_flag = '0'
    </select>
</mapper> 