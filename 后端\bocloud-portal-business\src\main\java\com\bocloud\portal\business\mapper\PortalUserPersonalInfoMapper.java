package com.bocloud.portal.business.mapper;

import org.apache.ibatis.annotations.Param;

import com.bocloud.domain.web.PersonalInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户个人信息 数据层
 */
@Mapper
public interface PortalUserPersonalInfoMapper {
    
    /**
     * 查询用户最新的个人信息（优先返回已认证的记录，如果没有则返回待审核的记录）
     * 
     * @param userId 用户ID
     * @return 用户个人信息
     */
    PersonalInfo selectLatestPersonalInfoByUserId(Long userId);

    /**
     * 新增用户个人信息
     *
     * @param personalInfo 用户信息
     * @return 结果
     */
    int insertPortalUserPersonalInfo(PersonalInfo personalInfo);

    /**
     * 修改用户个人信息
     * 
     * @param personalInfo 用户信息
     * @return 结果
     */
    int updatePortalUserPersonalInfo(PersonalInfo personalInfo);

    /**
     * 将用户的所有个人信息记录设置为非活跃
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deactivateUserPersonalInfo(Long userId);

    
    /**
     * 根据用户ID查询最新的指定状态的个人信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 个人信息
     */
    PersonalInfo selectLatestByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据用户ID查询已激活的个人信息
     * 
     * @param userId 用户ID
     * @return 个人信息
     */
    PersonalInfo selectActiveByUserId(Long userId);

    /**
     * 检查用户是否有待审核的认证信息
     *
     * @param userId 用户ID
     * @return 待审核的认证信息数量
     */
    int checkPendingVerification(@Param("userId") Long userId);
} 