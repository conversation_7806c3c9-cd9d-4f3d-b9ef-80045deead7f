<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PortalUserMapper">
    
    <resultMap type="com.bocloud.domain.web.PortalUser" id="PortalUserResult">
        <id     property="id"           column="id"           />
        <result property="username"     column="username"     />
        <result property="password"     column="password"     />
        <result property="phone"        column="phone"        />

        <result property="wechatOpenid" column="wechat_openid"/>
        <result property="wechatUnionid" column="wechat_unionid"/>
        <result property="nickname"     column="nickname"     />
        <result property="avatar"       column="avatar"       />

        <result property="type"         column="type"         />
        <result property="status"       column="status"       />
        <result property="infoStatus"   column="info_status"  />
        <result property="verificationType" column="verification_type" />
        <result property="verificationStatus" column="verification_status" />
        <result property="auditComment" column="audit_comment"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        
        
    </resultMap>

    <sql id="selectPortalUserVo">
        select 
            pu.id, pu.username, pu.password, pu.phone, pu.wechat_openid, pu.wechat_unionid, 
            pu.nickname, pu.avatar, pu.type, pu.status, pu.info_status, pu.verification_type, pu.verification_status,
            pu.audit_comment, pu.last_login_time, pu.create_by, pu.create_time, pu.update_by, pu.update_time
        from portal_user pu
    </sql>

    <select id="selectPortalUserList" parameterType="PortalUser" resultMap="PortalUserResult">
        <include refid="selectPortalUserVo"/>
        <where>
            <if test="username != null and username != ''">
                AND pu.username like concat('%', #{username}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND pu.phone like concat('%', #{phone}, '%')
            </if>
            <if test="type != null and type != ''">
                AND pu.type = #{type}
            </if>
            <if test="status != null">
                AND pu.status = #{status}
            </if>
            <if test="infoStatus != null">
                AND pu.info_status = #{infoStatus}
            </if>
            <if test="verificationType != null and verificationType != ''">
                AND pu.verification_type = #{verificationType}
            </if>
            <if test="verificationStatus != null and verificationStatus != ''">
                AND pu.verification_status = #{verificationStatus}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND pu.create_time >= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND pu.create_time &lt;= #{params.endTime}
            </if>
            and pu.del_flag = '0'
        </where>
        order by pu.info_status = 1 desc, pu.create_time desc
    </select>

    <select id="selectPortalUserById" parameterType="Long" resultMap="PortalUserResult">
        <include refid="selectPortalUserVo"/>
        where pu.id = #{userId} and pu.del_flag = '0'
    </select>

    <update id="updatePortalUser" parameterType="PortalUser">
        update portal_user
        <set>
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="wechatOpenid != null and wechatOpenid != ''">wechat_openid = #{wechatOpenid},</if>
            <if test="wechatUnionid != null and wechatUnionid != ''">wechat_unionid = #{wechatUnionid},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="infoStatus != null">info_status = #{infoStatus},</if>
            <if test="verificationType != null and verificationType != ''">verification_type = #{verificationType},</if>
            <if test="verificationStatus != null and verificationStatus != ''">verification_status = #{verificationStatus},</if>
            <if test="auditComment != null and auditComment != ''">audit_comment = #{auditComment},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        where id = #{id} and del_flag = '0'
    </update>

</mapper> 