package com.bocloud.admin.business.service;

import com.bocloud.domain.system.demand.EnterpriseDemand;
import java.util.List;

public interface IEnterpriseDemandService {
    EnterpriseDemand selectEnterpriseDemandById(Long demandId);
    List<EnterpriseDemand> selectEnterpriseDemandList(EnterpriseDemand enterpriseDemand);
    int deleteEnterpriseDemandById(Long demandId);
    int deleteEnterpriseDemandByIds(Long[] demandIds);
    /**
     * 审核企业需求（同意/拒绝）
     * @param demand 企业需求对象，需包含demandId、status、reviewComment
     * @return 影响行数
     */
    int updateEnterpriseDemandAudit(EnterpriseDemand demand);

    /**
     * 审核企业需求
     * @param demandId 需求ID
     * @param approve 是否同意（true=同意，false=拒绝）
     * @param reviewComment 审核意见
     * @return 影响行数
     */
    int auditDemand(Long demandId, Boolean approve, String reviewComment);

        /**
     * 审核企业选择的中标方案
     * 
     * @param demandId 需求ID
     * @param approved 是否通过审核
     * @param reviewComment 审核意见
     * @return 结果
     */
    int reviewSolution(Long demandId, Boolean approved, String reviewComment);
} 