package com.bocloud.admin.business.mapper;

import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.ExpertInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 门户用户信息 数据层
 */
public interface PortalUserMapper {
    
    /**
     * 查询门户用户列表
     * 
     * @param portalUser 门户用户信息
     * @return 门户用户集合
     */
    List<PortalUser> selectPortalUserList(PortalUser portalUser);

    /**
     * 查询用户详细信息
     */
    PortalUser selectPortalUserById(Long userId);

    /**
     * 更新会员信息
     * 
     * @param portalUser 会员信息
     * @return 结果
     */
    int updatePortalUser(PortalUser portalUser);

    /**
     * 查询专家用户列表
     * 
     * @param portalUser 用户查询条件
     * @param expertInfo 专家信息查询条件
     * @return 专家用户列表
     */
    List<PortalUser> selectExpertList(@Param("portalUser") PortalUser portalUser, @Param("expertInfo") ExpertInfo expertInfo);
} 