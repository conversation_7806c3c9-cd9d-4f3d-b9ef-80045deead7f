package com.bocloud.admin.business.service;

import java.util.List;
import com.bocloud.domain.system.demand.DemandCharge;

/**
 * 需求收费Service接口
 * 
 * <AUTHOR>
 */
public interface IDemandChargeService {
    /**
     * 新增需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int insertDemandCharge(DemandCharge demandCharge);

    /**
     * 查询需求收费列表
     * 
     * @param demandCharge 需求收费信息
     * @return 需求收费集合
     */
    public List<DemandCharge> selectDemandChargeList(DemandCharge demandCharge);

    /**
     * 查询需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeById(Long chargeId);

    /**
     * 根据合同ID查询需求收费
     * 
     * @param contractId 合同ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeByContractId(Long contractId);

    /**
     * 修改需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int updateDemandCharge(DemandCharge demandCharge);

    /**
     * 批量确认收费
     * 
     * @param chargeIds 需要确认的收费ID数组
     * @return 结果
     */
    public int confirmCharges(Long[] chargeIds);

    /**
     * 批量确认开票
     * 
     * @param chargeIds 需要确认开票的收费ID数组
     * @return 结果
     */
    public int confirmInvoices(Long[] chargeIds);

    /**
     * 批量完成收费
     * 
     * @param chargeIds 需要完成的收费ID数组
     * @return 结果
     */
    public int completeCharges(Long[] chargeIds);

    /**
     * 批量取消收费
     * 
     * @param chargeIds 需要取消的收费ID数组
     * @return 结果
     */
    public int cancelCharges(Long[] chargeIds);
} 