<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.DemandSolutionMapper">
    
    <resultMap type="com.bocloud.domain.system.demand.DemandSolution" id="AdminDemandSolutionResult">
        <result property="solutionId"    column="solution_id"    />
        <result property="solutionNo"    column="solution_no"    />
        <result property="demandId"      column="demand_id"      />
        <result property="demandNo"      column="demand_no"      />
        <result property="inviteId"      column="invite_id"      />
        <result property="portalUserId"  column="portal_user_id" />
        <result property="solutionTitle" column="solution_title" />
        <result property="backgroundUnderstanding" column="background_understanding" />
        <result property="technicalSolutions" column="technical_solutions" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="technicalIndicators" column="technical_indicators" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="estimatedBudget" column="estimated_budget" />
        <result property="estimatedDuration" column="estimated_duration" />
        <result property="implementationPlan" column="implementation_plan" />
        <result property="additionalNotes" column="additional_notes" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="status"        column="status"         />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <result property="delFlag"       column="del_flag"       />
    </resultMap>

    <sql id="selectDemandSolutionVo">
        select solution_id, solution_no, demand_id, demand_no, invite_id, portal_user_id, solution_title, background_understanding, technical_solutions, technical_indicators, 
        estimated_budget, estimated_duration, implementation_plan, additional_notes, status,
        create_by, create_time, update_by, update_time, remark, del_flag
        from demand_solution
    </sql>

    <select id="selectDemandSolutionList" parameterType="com.bocloud.domain.system.demand.DemandSolution" resultMap="AdminDemandSolutionResult">
        <include refid="selectDemandSolutionVo"/>
        <where>
            AND del_flag = '0'
            <if test="demandId != null">
                AND demand_id = #{demandId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDemandSolutionById" parameterType="Long" resultMap="AdminDemandSolutionResult">
        <include refid="selectDemandSolutionVo"/>
        where solution_id = #{solutionId} and del_flag = '0'
    </select>

    <update id="updateDemandSolution" parameterType="com.bocloud.domain.system.demand.DemandSolution">
        update demand_solution
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            update_time = sysdate()
        </set>
        where solution_id = #{solutionId}
    </update>

    <update id="deleteDemandSolutionById" parameterType="Long">
        update demand_solution
        set del_flag = '1'
        where solution_id = #{solutionId}
    </update>

    <update id="deleteDemandSolutionByIds" parameterType="String">
        update demand_solution
        set del_flag = '1'
        where solution_id in
        <foreach item="solutionId" collection="array" open="(" separator="," close=")">
            #{solutionId}
        </foreach>
    </update>

    <select id="selectByDemandIdExceptSolutionId" resultMap="AdminDemandSolutionResult">
        <include refid="selectDemandSolutionVo"/>
        where demand_id = #{demandId} and solution_id != #{solutionId} and del_flag = '0'
    </select>

</mapper> 