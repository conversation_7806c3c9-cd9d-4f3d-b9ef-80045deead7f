package com.bocloud.portal.business.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序配置
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.miniapp")
public class WxMaConfiguration {

    /**
     * 微信小程序appId
     */
    private String appId;

    /**
     * 微信小程序secret
     */
    private String secret;

    @Bean
    public WxMaService wxMaService() {
        log.info("[WxMaConfiguration] 开始初始化微信小程序服务，appId: {}", appId);
        
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(appId);
        config.setSecret(secret);
        
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        
        log.info("[WxMaConfiguration] 微信小程序服务初始化完成");
        return wxMaService;
    }
} 