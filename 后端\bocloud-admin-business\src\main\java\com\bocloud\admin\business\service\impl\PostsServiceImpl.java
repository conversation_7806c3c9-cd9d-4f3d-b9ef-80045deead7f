package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.PostsMapper;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.admin.business.service.IPostsService;
import com.bocloud.domain.system.post.Posts;
import com.bocloud.domain.system.CommonAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 信息发布Service业务层处理
 *
 * @date 2025-03-03
 */
@Service
public class PostsServiceImpl implements IPostsService
{
    @Autowired
    private PostsMapper postsMapper;

    @Autowired
    private ICommonAttachmentService commonAttachmentServiceImpl;

    /**
     * 查询信息发布
     *
     * @param postId 信息发布主键
     * @return 信息发布
     */
    @Override
    public Posts selectPostsByPostId(Long postId)
    {
        Posts posts = postsMapper.selectPostsByPostId(postId);
        if (posts != null) {
            // 查询附件列表
            List<CommonAttachment> attachments = commonAttachmentServiceImpl.selectCommonAttachmentByBusiness(postId, "posts");
            posts.setAttachments(attachments);
        }
        return posts;
    }

    /**
     * 查询信息发布列表
     *
     * @param posts 信息发布
     * @return 信息发布
     */
    @Override
    public List<Posts> selectPostsList(Posts posts)
    {
        List<Posts> postsList = postsMapper.selectPostsList(posts);
        // 查询每个文章的附件列表
        for (Posts post : postsList) {
            List<CommonAttachment> attachments = commonAttachmentServiceImpl.selectCommonAttachmentByBusiness(post.getPostId(), "posts");
            post.setAttachments(attachments);
        }
        return postsList;
    }

    /**
     * 新增信息发布
     *
     * @param posts 信息发布
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPosts(Posts posts)
    {
        // 插入文章信息
        int rows = postsMapper.insertPosts(posts);
        
        // 处理附件
        if (rows > 0 && posts.getAttachments() != null && !posts.getAttachments().isEmpty()) {
            for (CommonAttachment attachment : posts.getAttachments()) {
                attachment.setBusinessId(posts.getPostId());
                attachment.setBusinessType("posts");
                commonAttachmentServiceImpl.insertCommonAttachment(attachment);
            }
        }
        
        return rows;
    }

    /**
     * 修改信息发布
     *
     * @param posts 信息发布
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePosts(Posts posts)
    {
        // 更新文章信息
        int rows = postsMapper.updatePosts(posts);
        
        // 处理附件
        if (rows > 0 && posts.getAttachments() != null) {
            // 先删除原有附件
            commonAttachmentServiceImpl.deleteAttachmentByBusiness(posts.getPostId(), "posts");
            
            // 插入新附件
            if (!posts.getAttachments().isEmpty()) {
                for (CommonAttachment attachment : posts.getAttachments()) {
                    attachment.setBusinessId(posts.getPostId());
                    attachment.setBusinessType("posts");
                    commonAttachmentServiceImpl.insertCommonAttachment(attachment);
                }
            }
        }
        
        return rows;
    }

    /**
     * 批量删除信息发布
     *
     * @param postIds 需要删除的信息发布主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePostsByPostIds(Long[] postIds)
    {
        // 删除文章信息
        int rows = postsMapper.deletePostsByPostIds(postIds);
        
        // 删除相关附件
        if (rows > 0) {
            for (Long postId : postIds) {
                commonAttachmentServiceImpl.deleteAttachmentByBusiness(postId, "posts");
            }
        }
        
        return rows;
    }

    /**
     * 删除信息发布信息
     *
     * @param postId 信息发布主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePostsByPostId(Long postId)
    {
        // 删除文章信息
        int rows = postsMapper.deletePostsByPostId(postId);
        
        // 删除相关附件
        if (rows > 0) {
            commonAttachmentServiceImpl.deleteAttachmentByBusiness(postId, "posts");
        }
        
        return rows;
    }
}