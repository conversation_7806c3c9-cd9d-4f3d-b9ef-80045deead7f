package com.bocloud.domain.web;

import java.util.Date;

import lombok.Data;

/**
 * 消息查询对象
 */
@Data
public class PortalMessageQuery {
    /** 用户ID */
    private Long userId;

    private String title;
    
    /** 消息类型 */
    private String type;
    
    /** 消息状态 */
    private String status;
    
    /** 开始时间 */
    private Date beginTime;
    
    /** 结束时间 */
    private Date endTime;

    /** 接收者ID */
    private Long receiverId;

}