package com.bocloud.domain.system.online_education;

import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 课程视频对象 course_video
 *
 * @date 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CourseVideo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 视频ID */
    private Integer videoId;

    /** 标题 */
    private String title;

    /** 时长（分钟） */
    private Integer duration;

    /** 视频URL */
    private String videoUrl;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 章节 */
    private String chapter;

    /** 课程ID */
    private Integer courseId;
} 