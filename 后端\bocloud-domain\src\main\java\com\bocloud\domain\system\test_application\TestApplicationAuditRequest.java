package com.bocloud.domain.system.test_application;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 测试申请审核请求对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestApplicationAuditRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long testApplicationId;

    /** 审核结果（状态） */
    @NotNull(message = "审核结果不能为空")
    private String auditResult;

    /** 审核意见 */
    private String reviewComment;

    /** 管理员设置的价格 */
    private BigDecimal adminPrice;

    /** 管理员价格备注 */
    private String adminPriceRemark;
} 