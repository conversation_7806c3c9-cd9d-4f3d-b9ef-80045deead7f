package com.bocloud.portal.controller.api;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.portal.business.service.IExpertInfoService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 信息发布接口
 */
@Slf4j
@RestController
@RequestMapping("/api/experts")
@Api(tags = "专家接口")
public class ApiExpertsController extends BaseController{

    @Autowired
    private IExpertInfoService expertInfoService;

    /**
     * 获取专家列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ExpertInfo expertInfo) {
        startPage();
        List<ExpertInfo> list = expertInfoService.selectExpertInfoList(expertInfo);
        return getDataTable(list);
    }

    /**
     * 获取专家详情
     */
    @GetMapping("/{expertId}")
    public AjaxResult getInfo(@PathVariable("expertId") Long expertId) {
        ExpertInfo expertInfo = expertInfoService.getExpertDetailByExpertId(expertId);
        return AjaxResult.success(expertInfo);
    }
}
