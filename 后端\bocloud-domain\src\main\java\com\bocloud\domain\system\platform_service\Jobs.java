package com.bocloud.domain.system.platform_service;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 职位发布对象 jobs
 *
 * @date 2025-03-11
 */
public class Jobs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 职位唯一标识 */
    private Long jobId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 职位名称 */
    @Excel(name = "职位名称")
    private String title;

    /** 工作地点 */
    @Excel(name = "工作地点")
    private String location;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private BigDecimal salaryMax;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String description;

    /** 任职要求 */
    @Excel(name = "任职要求")
    private String requirements;

    /** 职位状态（审核中1、开放2、关闭3） */
    @Excel(name = "职位状态", readConverterExp = "审=核中1、开放2、关闭3")
    private String status;

    /** 职位申请截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "职位申请截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    public void setJobId(Long jobId)
    {
        this.jobId = jobId;
    }

    public Long getJobId()
    {
        return jobId;
    }
    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }

    public String getCompanyName()
    {
        return companyName;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setLocation(String location)
    {
        this.location = location;
    }

    public String getLocation()
    {
        return location;
    }
    public void setSalaryMin(BigDecimal salaryMin)
    {
        this.salaryMin = salaryMin;
    }

    public BigDecimal getSalaryMin()
    {
        return salaryMin;
    }
    public void setSalaryMax(BigDecimal salaryMax)
    {
        this.salaryMax = salaryMax;
    }

    public BigDecimal getSalaryMax()
    {
        return salaryMax;
    }
    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setRequirements(String requirements)
    {
        this.requirements = requirements;
    }

    public String getRequirements()
    {
        return requirements;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setDeadline(Date deadline)
    {
        this.deadline = deadline;
    }

    public Date getDeadline()
    {
        return deadline;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("jobId", getJobId())
                .append("companyName", getCompanyName())
                .append("title", getTitle())
                .append("location", getLocation())
                .append("salaryMin", getSalaryMin())
                .append("salaryMax", getSalaryMax())
                .append("description", getDescription())
                .append("requirements", getRequirements())
                .append("status", getStatus())
                .append("deadline", getDeadline())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}