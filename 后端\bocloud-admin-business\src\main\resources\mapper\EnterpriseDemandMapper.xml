<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.EnterpriseDemandMapper">

    <resultMap id="EnterpriseDemandResult" type="com.bocloud.domain.system.demand.EnterpriseDemand">
        <id property="demandId" column="demand_id"/>
        <result property="demandNo" column="demand_no"/>
        <result property="portalUserId" column="portal_user_id"/>
        <result property="demandTitle" column="demand_title"/>
        <result property="demandDescription" column="demand_description"/>
        <result property="demandCategory" column="demand_category"/>
        <result property="specificRequirements" column="specific_requirements" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="technicalIndicators" column="technical_indicators" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="otherRequirements" column="other_requirements" typeHandler="com.bocloud.common.core.handler.JsonStringListTypeHandler"/>
        <result property="industry" column="industry"/>
        <result property="technologyField" column="technology_field"/>
        <result property="minBudget" column="min_budget"/>
        <result property="maxBudget" column="max_budget"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="status" column="status"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="reviewComment" column="review_comment"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="selectEnterpriseDemandById" parameterType="Long" resultMap="EnterpriseDemandResult">
        select * from enterprise_demand where demand_id = #{demandId} and del_flag = '0'
    </select>

    <select id="selectEnterpriseDemandList" parameterType="EnterpriseDemand" resultMap="EnterpriseDemandResult">
        select * from enterprise_demand
        <where>
            del_flag = '0'
            <if test="demandNo != null and demandNo != ''">and demand_no like concat(#{demandNo}, '%')</if>
            <if test="demandTitle != null and demandTitle != ''">and demand_title like concat('%', #{demandTitle}, '%')</if>
            <if test="statusList != null and statusList.size() > 0">
              and status in
              <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
              </foreach>
            </if>
            <if test="technologyField != null and technologyField != ''">and technology_field = #{technologyField}</if>
        </where>
        order by create_time desc
    </select>

    <update id="deleteEnterpriseDemandById" parameterType="Long">
        update enterprise_demand set del_flag = '1', update_time = SYSDATE() where demand_id = #{demandId}
    </update>

    <update id="deleteEnterpriseDemandByIds" parameterType="Long[]">
        update enterprise_demand set del_flag = '1', update_time = SYSDATE() where demand_id in
        <foreach item="demandId" collection="array" open="(" separator="," close=")">
            #{demandId}
        </foreach>
    </update>

    <update id="updateEnterpriseDemandAudit" parameterType="EnterpriseDemand">
        update enterprise_demand
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = SYSDATE()
        </set>
        where demand_id = #{demandId}
    </update>

    <!-- 更新需求的状态 -->
    <update id="updateEnterpriseDemandStatusByDemandId" parameterType="EnterpriseDemand">
        update enterprise_demand
        set status = #{status}
        where demand_id = #{demandId}
    </update>
</mapper> 