package com.bocloud.domain.system;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

public class CommonCollection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 收藏ID */
    private Long collectionId;

    /** 业务ID */
    @Excel(name = "业务ID")
    private Long businessId;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 收藏人ID */
    @Excel(name = "收藏人ID")
    private Long collectBy;

    /** 收藏时间 */
    @Excel(name = "收藏时间")
    private Date collectTime;

    public Long getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(Long collectionId) {
        this.collectionId = collectionId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Long getCollectBy() {
        return collectBy;
    }

    public void setCollectBy(Long collectBy) {
        this.collectBy = collectBy;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("collectionId", getCollectionId())
                .append("businessId", getBusinessId())
                .append("businessType", getBusinessType())
                .append("collectBy", getCollectBy())
                .append("collectTime", getCollectTime())
                .toString();
    }
}
