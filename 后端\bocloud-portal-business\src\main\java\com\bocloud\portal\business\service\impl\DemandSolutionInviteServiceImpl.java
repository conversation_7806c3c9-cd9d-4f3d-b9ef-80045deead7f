package com.bocloud.portal.business.service.impl;

import com.bocloud.common.exception.ServiceException;
import com.bocloud.portal.business.mapper.DemandSolutionInviteMapper;
import com.bocloud.portal.business.service.IDemandSolutionInviteService;
import com.bocloud.domain.web.DemandSolutionInvite;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 需求解决方案邀请Service实现
 */
@Service
public class DemandSolutionInviteServiceImpl implements IDemandSolutionInviteService {

    @Autowired
    private DemandSolutionInviteMapper demandSolutionInviteMapper;

    @Override
    public DemandSolutionInvite selectDemandSolutionInviteById(Long id) {
        return demandSolutionInviteMapper.selectDemandSolutionInviteById(id);
    }

    @Override
    public List<DemandSolutionInvite> selectDemandSolutionInviteList(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.selectDemandSolutionInviteList(invite);
    }

    @Override
    public int insertDemandSolutionInvite(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.insertDemandSolutionInvite(invite);
    }

    @Override
    public int updateDemandSolutionInvite(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.updateDemandSolutionInvite(invite);
    }

    @Override
    public int deleteDemandSolutionInviteById(Long id) {
        return demandSolutionInviteMapper.deleteDemandSolutionInviteById(id);
    }

    @Override
    public int deleteDemandSolutionInviteByIds(Long[] ids) {
        return demandSolutionInviteMapper.deleteDemandSolutionInviteByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int acceptDemandSolutionInvite(Long inviteId) {
        // 查询邀请记录
        DemandSolutionInvite invite = demandSolutionInviteMapper.selectDemandSolutionInviteById(inviteId);
        if (invite == null) {
            throw new ServiceException("邀请记录不存在");
        }
        
        // 检查邀请状态
        if (!DemandSolutionInvite.STATUS_PENDING.equals(invite.getInviteStatus())) {
            throw new ServiceException("该邀请已处理，不能重复操作");
        }
        
        // 更新邀请状态为已接受
        invite.setInviteStatus(DemandSolutionInvite.STATUS_ACCEPTED);
        return demandSolutionInviteMapper.updateDemandSolutionInvite(invite);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int rejectDemandSolutionInvite(Long inviteId) {
        // 查询邀请记录
        DemandSolutionInvite invite = demandSolutionInviteMapper.selectDemandSolutionInviteById(inviteId);
        if (invite == null) {
            throw new ServiceException("邀请记录不存在");
        }
        
        // 检查邀请状态
        if (!DemandSolutionInvite.STATUS_PENDING.equals(invite.getInviteStatus())) {
            throw new ServiceException("该邀请已处理，不能重复操作");
        }
        
        // 更新邀请状态为已拒绝
        invite.setInviteStatus(DemandSolutionInvite.STATUS_REJECTED);
        return demandSolutionInviteMapper.updateDemandSolutionInvite(invite);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submitDemandSolutionInvite(Long inviteId) {
        // 查询邀请记录
        DemandSolutionInvite invite = demandSolutionInviteMapper.selectDemandSolutionInviteById(inviteId);
        if (invite == null) {
            throw new ServiceException("邀请记录不存在");
        }
        
        // 检查邀请状态，只有待响应或已接受的邀请才能提交方案
        if (!DemandSolutionInvite.STATUS_PENDING.equals(invite.getInviteStatus()) 
            && !DemandSolutionInvite.STATUS_ACCEPTED.equals(invite.getInviteStatus())) {
            throw new ServiceException("该邀请状态不允许提交方案");
        }
        
        // 更新邀请状态为已提交
        invite.setInviteStatus(DemandSolutionInvite.STATUS_SUBMITTED);
        return demandSolutionInviteMapper.updateDemandSolutionInvite(invite);
    }
} 