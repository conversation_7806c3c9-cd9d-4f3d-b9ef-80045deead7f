package com.bocloud.portal.controller;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.VerifyCodeType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.portal.business.service.IVerifyCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 验证码控制器
 */
@Api(tags = "验证码接口")
@RestController
@RequestMapping("/smscode")
public class VerifyCodeController {

    @Autowired
    private IVerifyCodeService verifyCodeService;

    /**
     * 发送验证码
     */
    @ApiOperation("发送验证码")
    @PostMapping("/send")
    public AjaxResult sendVerifyCode(
            @ApiParam("账号（手机号/邮箱）") @RequestParam String account,
            @ApiParam("验证码类型") @RequestParam VerifyCodeType type) {
        try {
            verifyCodeService.sendVerifyCode(account, type);
            return AjaxResult.success("验证码发送成功");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 验证验证码
     */
    @ApiOperation("验证验证码")
    @PostMapping("/check")
    public AjaxResult verifyCode(
            @ApiParam("账号（手机号/邮箱）") @RequestParam String account,
            @ApiParam("验证码") @RequestParam String code,
            @ApiParam("验证码类型") @RequestParam VerifyCodeType type) {
        try {
            verifyCodeService.verifyCode(account, code, type);
            return AjaxResult.success("验证码验证成功");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
} 