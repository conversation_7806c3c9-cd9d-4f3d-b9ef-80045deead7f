package com.bocloud.admin.business.mapper;

import com.bocloud.domain.system.demand.DemandSolution;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 需求方案Mapper接口
 */
public interface DemandSolutionMapper {
    /**
     * 查询方案列表
     * 
     * @param solution 查询条件
     * @return 方案列表
     */
    List<DemandSolution> selectDemandSolutionList(DemandSolution solution);

    /**
     * 查询方案详细信息
     * 
     * @param solutionId 方案ID
     * @return 方案信息
     */
    DemandSolution selectDemandSolutionById(Long solutionId);

    /**
     * 更新方案
     * 
     * @param solution 方案信息
     * @return 结果
     */
    int updateDemandSolution(DemandSolution solution);

    /**
     * 查询同一需求下除指定方案外的所有方案
     */
    List<DemandSolution> selectByDemandIdExceptSolutionId(@Param("demandId") Long demandId, @Param("solutionId") Long solutionId);
} 