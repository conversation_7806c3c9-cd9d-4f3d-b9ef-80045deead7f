<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.OrderMapper">

    <resultMap id="OrderResult" type="com.bocloud.domain.system.Order">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="businessType" column="business_type"/>
        <result property="businessId" column="business_id"/>
        <result property="userId" column="user_id"/>
        <result property="amount" column="amount"/>
        <result property="status" column="status"/>
        <result property="payTime" column="pay_time"/>
        <result property="payChannel" column="pay_channel"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="refundTime" column="refund_time"/>
        <result property="remark" column="remark"/>
        <result property="paymentProofUrl" column="payment_proof_url"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_id, order_no, business_type, business_id, user_id, amount, status, pay_time, pay_channel, refund_amount, refund_time, remark, payment_proof_url, del_flag, create_by, create_time, update_by, update_time
    </sql>

    <insert id="insertOrder" parameterType="com.bocloud.domain.system.Order">
        insert into orders
        (order_no, business_type, business_id, user_id, amount, status, pay_time, pay_channel, refund_amount, refund_time, remark, payment_proof_url, del_flag, create_by, create_time, update_by, update_time)
        values
        (#{orderNo}, #{businessType}, #{businessId}, #{userId}, #{amount}, #{status}, #{payTime}, #{payChannel}, #{refundAmount}, #{refundTime}, #{remark}, #{paymentProofUrl}, #{delFlag}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <select id="selectOrderById" parameterType="long" resultMap="OrderResult">
        select <include refid="Base_Column_List"/>
        from orders
        where order_id = #{orderId} and del_flag = '0'
    </select>

    <select id="selectOrderList" parameterType="com.bocloud.domain.system.Order" resultMap="OrderResult">
        select <include refid="Base_Column_List"/>
        from orders
        <where>
            <if test="orderNo != null and orderNo != ''">and order_no = #{orderNo}</if>
            <if test="businessType != null and businessType != ''">and business_type = #{businessType}</if>
            <if test="businessId != null">and business_id = #{businessId}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="status != null and status != ''">and status = #{status}</if>
            <if test="payChannel != null and payChannel != ''">and pay_channel = #{payChannel}</if>
            <if test="delFlag != null and delFlag != ''">and del_flag = #{delFlag}</if>
        </where>
        order by create_time desc
    </select>

    <update id="updateOrder" parameterType="com.bocloud.domain.system.Order">
        update orders
        <set>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="paymentProofUrl != null">payment_proof_url = #{paymentProofUrl},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where order_id = #{orderId}
    </update>

    <update id="deleteOrderById" parameterType="long">
        update orders set del_flag = '1' where order_id = #{orderId}
    </update>

    <update id="deleteOrderByIds" parameterType="long">
        update orders set del_flag = '1' where order_id in
        <foreach collection="array" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

</mapper> 