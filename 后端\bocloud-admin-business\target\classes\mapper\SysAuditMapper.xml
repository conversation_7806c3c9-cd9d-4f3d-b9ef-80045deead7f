<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.SysAuditMapper">
    
    <resultMap type="com.bocloud.domain.system.SysAudit" id="SysAuditResult">
        <result property="auditId"       column="audit_id"       />
        <result property="businessType"  column="business_type"  />
        <result property="businessId"    column="business_id"    />
        <result property="auditType"     column="audit_type"     />
        <result property="fromStatus"    column="from_status"    />
        <result property="toStatus"      column="to_status"      />
        <result property="auditResult"   column="audit_result"   />
        <result property="reviewComment" column="review_comment" />
        <result property="auditUserId"   column="audit_user_id"  />
        <result property="auditUserName" column="audit_user_name"/>
        <result property="auditTime"     column="audit_time"     />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
    </resultMap>

    <sql id="selectSysAuditVo">
        select audit_id, business_type, business_id, audit_type, from_status, to_status, 
        audit_result, review_comment, audit_user_id, audit_user_name, audit_time, create_time, update_time, remark
        from sys_audit
    </sql>

    <select id="selectSysAuditList" parameterType="com.bocloud.domain.system.SysAudit" resultMap="SysAuditResult">
        <include refid="selectSysAuditVo"/>
        <where>
            <if test="businessType != null and businessType != ''">
                AND business_type = #{businessType}
            </if>
            <if test="businessId != null">
                AND business_id = #{businessId}
            </if>
            <if test="auditType != null and auditType != ''">
                AND audit_type = #{auditType}
            </if>
            <if test="auditResult != null and auditResult != ''">
                AND audit_result = #{auditResult}
            </if>
            <if test="auditUserId != null">
                AND audit_user_id = #{auditUserId}
            </if>
            <if test="auditTime != null">
                AND audit_time = #{auditTime}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysAuditById" parameterType="Long" resultMap="SysAuditResult">
        <include refid="selectSysAuditVo"/>
        where audit_id = #{auditId}
    </select>
        
    <insert id="insertSysAudit" parameterType="com.bocloud.domain.system.SysAudit" useGeneratedKeys="true" keyProperty="auditId">
        insert into sys_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="auditType != null">audit_type,</if>
            <if test="fromStatus != null">from_status,</if>
            <if test="toStatus != null">to_status,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="remark != null">remark,</if>
            audit_time,
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="auditType != null">#{auditType},</if>
            <if test="fromStatus != null">#{fromStatus},</if>
            <if test="toStatus != null">#{toStatus},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="remark != null">#{remark},</if>
            sysdate(),
            sysdate(),
            sysdate()
         </trim>
    </insert>

    <update id="updateSysAudit" parameterType="com.bocloud.domain.system.SysAudit">
        update sys_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="auditType != null">audit_type = #{auditType},</if>
            <if test="fromStatus != null">from_status = #{fromStatus},</if>
            <if test="toStatus != null">to_status = #{toStatus},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where audit_id = #{auditId}
    </update>

    <delete id="deleteSysAuditById" parameterType="Long">
        delete from sys_audit where audit_id = #{auditId}
    </delete>

    <delete id="deleteSysAuditByIds" parameterType="String">
        delete from sys_audit where audit_id in 
        <foreach item="auditId" collection="array" open="(" separator="," close=")">
            #{auditId}
        </foreach>
    </delete>
</mapper> 