package com.bocloud.domain.system.pilot_application;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.system.CommonAttachment;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 中试申请对象 pilot_application
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PilotApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 申请编号 */
    private String applicationNo;

    /** 用户ID */
    private Long portalUserId;

    /** 申请人姓名 */
    private String applicantName;

    /** 联系电话 */
    private String phone;

    /** 单位名称 */
    private String enterpriseName;

    /** 中试类型：chemical-化工中试/material-材料中试/environment-环保中试 */
    private String pilotType;

    /** 项目名称 */
    private String projectName;

    /** 项目背景与目标 */
    private String projectBackground;

    /** 技术需求 */
    private String technicalRequirements;

    /** 补充说明 */
    private String otherInfo;

    /** 申请状态：0-待审核，1-待付款，2-已付款，3-进行中，4-已完成，-1-已拒绝，-2-已取消 */
    private String status;

    /** 付款状态：0-待付款，1-已上传凭证，2-已确认收款，3-付款凭证被拒绝 */
    private String paymentStatus;

    /** 管理员报价 */
    private BigDecimal adminPrice;

    /** 实际确认收款金额 */
    private BigDecimal confirmedAmount;

    /** 审核凭证时间 */
    private Date auditPaymentDate;

    /** 付款凭证 */
    private String paymentVoucher;

    /** 管理员报价备注 */
    private String adminPriceRemark;

    /** 确认收款备注 */
    private String confirmPaymentRemark;

    /** 审核意见 */
    private String reviewComment;

    /** 是否删除：0-否/1-是 */
    private String delFlag;

    /** 附件列表 */
    private List<CommonAttachment> attachments;

    /** 报告列表 */
    private List<CommonAttachment> reports;

    /** 关键字搜索 */
    private String keyword;

    /** 付款拒绝原因 */
    private String paymentRejectReason;

    // 状态常量定义
    public static final String STATUS_PENDING_REVIEW = "0";      // 待审核
    public static final String STATUS_PENDING_PAYMENT = "1";     // 待付款
    public static final String STATUS_PAID = "2";                // 已付款
    public static final String STATUS_IN_PROGRESS = "3";         // 进行中
    public static final String STATUS_COMPLETED = "4";           // 已完成
    public static final String STATUS_REJECTED = "-1";           // 已拒绝
    public static final String STATUS_CANCELLED = "-2";          // 已取消

    // 付款状态常量定义
    public static final String PAYMENT_STATUS_PENDING = "0";         // 待付款
    public static final String PAYMENT_STATUS_UPLOADED = "1";        // 已上传凭证(待审核)
    public static final String PAYMENT_STATUS_CONFIRMED = "2";       // 已确认收款
    public static final String PAYMENT_STATUS_REJECTED = "3";        // 付款凭证被拒绝

    @Override
    public String toString() {
        return "PilotApplication [id=" + id + ", applicationNo=" + applicationNo + ", portalUserId=" + portalUserId
                + ", applicantName=" + applicantName + ", phone=" + phone + ", enterpriseName=" + enterpriseName
                + ", pilotType=" + pilotType + ", projectName=" + projectName + ", projectBackground=" + projectBackground
                + ", technicalRequirements=" + technicalRequirements + ", otherInfo=" + otherInfo + ", status=" + status
                + ", paymentStatus=" + paymentStatus + ", adminPrice=" + adminPrice + ", confirmedAmount=" + confirmedAmount
                + ", paymentVoucher=" + paymentVoucher + ", adminPriceRemark=" + adminPriceRemark
                + ", confirmPaymentRemark=" + confirmPaymentRemark + ", reviewComment=" + reviewComment
                + ", delFlag=" + delFlag + ", attachments=" + attachments + "]";
    }

} 