package com.bocloud.portal.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.portal.business.mapper.ExpertInfoMapper;
import com.bocloud.portal.business.mapper.ExpertPaperMapper;
import com.bocloud.portal.business.mapper.ExpertWorkExperienceMapper;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.portal.business.service.IExpertInfoService;
import com.bocloud.domain.web.ExpertWorkExperience;
import com.bocloud.domain.web.enums.ExpertStatusEnum;
import com.bocloud.domain.web.ExpertPaper;

/**
 * 专家信息Service业务层处理
 */
@Service
public class ExpertInfoServiceImpl implements IExpertInfoService {

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertPaperMapper expertPaperMapper;

    @Autowired
    private ExpertWorkExperienceMapper expertWorkExperienceMapper;

    /**
     * 查询专家信息
     * 
     * @param userId 用户ID
     * @return 专家信息
     */
    @Override
    public ExpertInfo getExpertDetailByExpertId(Long expertId) {
        ExpertInfo expertInfo = expertInfoMapper.selectExpertInfoById(expertId);
        if (expertInfo != null) {
            // 查询工作履历
            expertInfo.setExpertWorkExperiences(expertWorkExperienceMapper.selectExpertWorkExperiencesByExpertId(expertId));
            // 查询论文列表
            expertInfo.setExpertPapers(expertPaperMapper.selectExpertPapersByExpertId(expertId));
        }
        return expertInfo;
    }

    /**
     * 查询专家信息列表
     * 
     * @param expertInfo 专家信息
     * @return 专家信息
     */
    @Override
    public List<ExpertInfo> selectExpertInfoList(ExpertInfo expertInfo) {
        return expertInfoMapper.selectExpertInfoList(expertInfo);
    }

    /**
     * 新增专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int insertExpertInfo(ExpertInfo expertInfo) {
        expertInfo.setStatus(ExpertStatusEnum.PENDING.getCode());
        int rows = expertInfoMapper.insertExpertInfo(expertInfo);
        // 插入工作履历
        if (rows > 0 && expertInfo.getExpertWorkExperiences() != null && !expertInfo.getExpertWorkExperiences().isEmpty()) {
            // 设置expert_id和user_id
            for (ExpertWorkExperience experience : expertInfo.getExpertWorkExperiences()) {
                experience.setExpertId(expertInfo.getExpertId());
                experience.setUserId(expertInfo.getUserId());
            }
            expertWorkExperienceMapper.insertExpertWorkExperiences(expertInfo.getExpertWorkExperiences());
        }
        // 插入论文列表
        if (rows > 0 && expertInfo.getExpertPapers() != null && !expertInfo.getExpertPapers().isEmpty()) {
            // 设置expert_id和user_id
            for (ExpertPaper paper : expertInfo.getExpertPapers()) {
                paper.setExpertId(expertInfo.getExpertId());
                paper.setUserId(expertInfo.getUserId());
            }
            expertPaperMapper.insertExpertPapers(expertInfo.getExpertPapers());
        }
        return rows;
    }

    /**
     * 修改专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    @Override
    public int updateExpertInfo(ExpertInfo expertInfo) {
        expertInfo.setStatus(ExpertStatusEnum.PENDING.getCode());
        int rows = expertInfoMapper.updateExpertInfo(expertInfo);
        // 更新工作履历
        if (rows > 0 && expertInfo.getExpertWorkExperiences() != null) {
            // 先删除原有履历
            expertWorkExperienceMapper.deleteExpertWorkExperiencesByExpertId(expertInfo.getExpertId());
            // 插入新的履历
            if (!expertInfo.getExpertWorkExperiences().isEmpty()) {
                // 设置expert_id和user_id
                for (ExpertWorkExperience experience : expertInfo.getExpertWorkExperiences()) {
                    experience.setExpertId(expertInfo.getExpertId());
                    experience.setUserId(expertInfo.getUserId());
                }
                expertWorkExperienceMapper.insertExpertWorkExperiences(expertInfo.getExpertWorkExperiences());
            }
        }
        // 更新论文列表
        if (rows > 0 && expertInfo.getExpertPapers() != null) {
            // 先删除原有论文
            expertPaperMapper.deleteExpertPapersByExpertId(expertInfo.getExpertId());
            // 插入新的论文
            if (!expertInfo.getExpertPapers().isEmpty()) {
                // 设置expert_id和user_id
                for (ExpertPaper paper : expertInfo.getExpertPapers()) {
                    paper.setExpertId(expertInfo.getExpertId());
                    paper.setUserId(expertInfo.getUserId());
                }
                expertPaperMapper.insertExpertPapers(expertInfo.getExpertPapers());
            }
        }
        return rows;
    }

    @Override
    public ExpertInfo getExpertDetailByUserId(Long userId) {
        ExpertInfo expertInfo = expertInfoMapper.selectExpertInfoByUserId(userId);
        if (expertInfo != null) {
            // 查询工作履历
            expertInfo.setExpertWorkExperiences(expertWorkExperienceMapper.selectExpertWorkExperiencesByExpertId(expertInfo.getExpertId()));
            // 查询论文列表
            expertInfo.setExpertPapers(expertPaperMapper.selectExpertPapersByExpertId(expertInfo.getExpertId()));
        }
        return expertInfo;
    }
} 