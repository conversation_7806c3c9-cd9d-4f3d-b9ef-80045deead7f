package com.bocloud.admin.controller;

import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import  com.bocloud.domain.system.platform_service.Jobs;
import com.bocloud.admin.business.service.IJobsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 职位发布Controller
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@RestController
@RequestMapping("/system/jobs")
public class JobsController extends BaseController
{
    @Autowired
    private IJobsService jobsService;

    /**
     * 查询职位发布列表
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:list')")
    @GetMapping("/list")
    public TableDataInfo list(Jobs jobs)
    {
        startPage();
        List<Jobs> list = jobsService.selectJobsList(jobs);
        return getDataTable(list);
    }

    /**
     * 导出职位发布列表
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:export')")
    @Log(title = "职位发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Jobs jobs)
    {
        List<Jobs> list = jobsService.selectJobsList(jobs);
        ExcelUtil<Jobs> util = new ExcelUtil<Jobs>(Jobs.class);
        util.exportExcel(response, list, "职位发布数据");
    }

    /**
     * 获取职位发布详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:query')")
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId)
    {
        return success(jobsService.selectJobsByJobId(jobId));
    }

    /**
     * 新增职位发布
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:add')")
    @Log(title = "职位发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Jobs jobs)
    {
        return toAjax(jobsService.insertJobs(jobs));
    }

    /**
     * 修改职位发布
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:edit')")
    @Log(title = "职位发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Jobs jobs)
    {
        return toAjax(jobsService.updateJobs(jobs));
    }

    /**
     * 删除职位发布
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:remove')")
    @Log(title = "职位发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable Long[] jobIds)
    {
        return toAjax(jobsService.deleteJobsByJobIds(jobIds));
    }
}