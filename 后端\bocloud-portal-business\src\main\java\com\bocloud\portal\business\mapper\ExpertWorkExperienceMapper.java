package com.bocloud.portal.business.mapper;

import java.util.List;
import com.bocloud.domain.web.ExpertWorkExperience;

/**
 * 专家工作履历Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExpertWorkExperienceMapper {
    /**
     * 批量新增专家工作履历
     * 
     * @param expertWorkExperienceList 专家工作履历列表
     * @return 结果
     */
    public int insertExpertWorkExperiences(List<ExpertWorkExperience> expertWorkExperienceList);

    /**
     * 修改专家工作履历
     * 
     * @param expertWorkExperience 专家工作履历
     * @return 结果
     */
    public int updateExpertWorkExperience(ExpertWorkExperience expertWorkExperience);

    /**
     * 批量删除专家工作履历
     * 
     * @param experienceIds 需要删除的履历ID
     * @return 结果
     */
    public int deleteExpertWorkExperienceByIds(Long[] experienceIds);

    /**
     * 根据专家ID删除工作履历
     * 
     * @param expertId 专家ID
     * @return 结果
     */
    public int deleteExpertWorkExperiencesByExpertId(Long expertId);

    /**
     * 根据专家ID查询工作履历列表
     * 
     * @param expertId 专家ID
     * @return 工作履历列表
     */
    public List<ExpertWorkExperience> selectExpertWorkExperiencesByExpertId(Long expertId);
} 