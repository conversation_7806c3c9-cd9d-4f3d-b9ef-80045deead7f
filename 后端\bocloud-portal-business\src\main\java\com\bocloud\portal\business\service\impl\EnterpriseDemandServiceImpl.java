package com.bocloud.portal.business.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.DemandContractStatus;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.portal.business.service.ICommonAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.portal.business.mapper.EnterpriseDemandMapper;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.portal.business.service.IEnterpriseDemandService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.portal.business.service.IDemandSolutionService;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.portal.business.mapper.DemandContractMapper;
import com.bocloud.portal.business.service.IDemandContractService;
import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.portal.business.mapper.DemandSolutionInviteMapper;

/**
 * 企业需求Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class EnterpriseDemandServiceImpl implements IEnterpriseDemandService 
{
    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    @Autowired
    private IDemandSolutionService demandSolutionService;

    @Autowired
    private DemandContractMapper demandContractMapper;

    @Autowired
    private IDemandContractService demandContractService;

    @Autowired
    private DemandSolutionInviteMapper demandSolutionInviteMapper;

    /**
     * 查询企业需求
     * 
     * @param demandId 企业需求主键
     * @return 企业需求
     */
    @Override
    public EnterpriseDemand selectEnterpriseDemandById(Long demandId)
    {
        //查询对应的附件
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand != null) {
            //查询对应的附件
            CommonAttachment attachmentQuery = new CommonAttachment();
            attachmentQuery.setBusinessType(CommonAttachmentBusinessType.ENTERPRISE_DEMAND);
            attachmentQuery.setBusinessId(demandId);
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(attachmentQuery);
            demand.setAttachments(attachments);
        }
        return demand;
    }

    /**
     * 查询企业需求列表
     * 
     * @param enterpriseDemand 企业需求
     * @return 企业需求
     */
    @Override
    public List<EnterpriseDemand> selectEnterpriseDemandList(EnterpriseDemand enterpriseDemand)
    {
        // 查询所有需求
        List<EnterpriseDemand> demandList = enterpriseDemandMapper.selectEnterpriseDemandList(enterpriseDemand);
        
        // 计算每个需求收到的方案数量
        if (!CollectionUtils.isEmpty(demandList)) {
            // 收集所有需求ID
            List<Long> demandIds = demandList.stream()
                .map(EnterpriseDemand::getDemandId)
                .collect(Collectors.toList());
            
            // 批量查询方案数量
            List<Map<String, Object>> solutionCounts = enterpriseDemandMapper.selectSolutionCountByDemandIds(demandIds);
            
            // 将方案数量设置到对应的需求对象中
            Map<Long, Integer> solutionCountMap = solutionCounts.stream()
                .collect(Collectors.toMap(
                    map -> Long.valueOf(map.get("demandId").toString()),
                    map -> Integer.valueOf(map.get("solutionCount").toString())
                ));
            
            // 设置方案数量
            for (EnterpriseDemand demand : demandList) {
                demand.setSolutionCount(solutionCountMap.getOrDefault(demand.getDemandId(), 0));
            }
        }
        
        return demandList;
    }

    /**
     * 获取下一个需求编号
     * 
     * @param prefix 前缀 (例如 "DEMAND-yyyyMMdd-")
     * @return 当天最大序号 + 1
     */
    private synchronized int getNextDemandSeq(String prefix) {
        // 查询当天最大的序号
        EnterpriseDemand query = new EnterpriseDemand();
        query.setDemandNo(prefix + "%");
        // 注意: 需要确保 EnterpriseDemandMapper.xml 中 selectEnterpriseDemandList 支持通过 demandNo 前缀查询
        // 如果不支持，需要调整查询逻辑或添加新的查询方法
        List<EnterpriseDemand> list = enterpriseDemandMapper.selectEnterpriseDemandList(query); 
        int maxSeq = 0;
        if (!CollectionUtils.isEmpty(list)) {
            for (EnterpriseDemand demand : list) {
                String demandNo = demand.getDemandNo();
                if (demandNo != null && demandNo.startsWith(prefix)) {
                    try {
                        // 提取序号部分并转换为整数
                        int seq = Integer.parseInt(demandNo.substring(prefix.length()));
                        maxSeq = Math.max(maxSeq, seq);
                    } catch (NumberFormatException e) {
                        // 忽略无法解析为数字的序号
                    }
                }
            }
        }
        return maxSeq + 1; // 返回下一个可用序号
    }

    /**
     * 新增企业需求
     * 
     * @param enterpriseDemand 企业需求
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEnterpriseDemand(EnterpriseDemand enterpriseDemand)
    {
        // 0. 生成需求编号
        String datePrefix = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prefix = "DEMAND-" + datePrefix + "-";
        int seq = getNextDemandSeq(prefix);
        enterpriseDemand.setDemandNo(prefix + String.format("%04d", seq)); // 格式化为4位，例如 DEMAND-20240520-0001
        
        // 1. 设置删除标志为0
        enterpriseDemand.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        
        // 2. 插入需求基本信息
        int rows = enterpriseDemandMapper.insertEnterpriseDemand(enterpriseDemand);
        if (rows <= 0) {
            return rows;
        }

        // 3. 保存附件信息
        List<CommonAttachment> attachments = enterpriseDemand.getAttachments();
        if (!CollectionUtils.isEmpty(attachments)) {
            Long demandId = enterpriseDemand.getDemandId();
            Long userId = enterpriseDemand.getPortalUserId(); // 使用需求对象中的用户ID作为上传者ID
            for (CommonAttachment attachment : attachments) {
                try {
                    attachment.setBusinessId(demandId);
                    attachment.setBusinessType(CommonAttachmentBusinessType.ENTERPRISE_DEMAND); // 企业需求附件的业务类型
                    attachment.setUploadBy(userId); // 设置上传者ID
                    attachment.setUploadDate(new Date()); // 设置上传时间
                    
                    attachmentService.insertCommonAttachment(attachment);
                } catch (Exception e) {
                    // 可以选择记录日志或抛出更具体的异常
                    throw new RuntimeException("保存需求附件失败: " + e.getMessage(), e);
                }
            }
        }

        return rows;
    }

    /**
     * 修改需求
     * 
     * @param enterpriseDemand 需求
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEnterpriseDemand(EnterpriseDemand enterpriseDemand)
    {
        // 1. 更新需求基本信息

        //如果当前状态是审核失败，则需要重新审核，需要到数据库中查询当前需求的状态
        EnterpriseDemand oldDemand = enterpriseDemandMapper.selectEnterpriseDemandById(enterpriseDemand.getDemandId());
        if (EnterpriseDemandStatus.FAILED.getCode().equals(oldDemand.getStatus())) {
            enterpriseDemand.setStatus(EnterpriseDemandStatus.PENDING.getCode());
        }
        int rows = enterpriseDemandMapper.updateEnterpriseDemand(enterpriseDemand);
        if (rows <= 0) {
            return rows;
        }

        Long demandId = enterpriseDemand.getDemandId();
        Long userId = enterpriseDemand.getPortalUserId();
        
        // 2. 先删除原有附件关联
        try {
            attachmentService.deleteAttachmentByBusiness(demandId, CommonAttachmentBusinessType.ENTERPRISE_DEMAND);
        } catch (Exception e) {
            throw new RuntimeException("删除原有附件失败: " + e.getMessage(), e);
        }
        
        // 3. 重新添加附件信息
        List<CommonAttachment> attachments = enterpriseDemand.getAttachments();
        if (!CollectionUtils.isEmpty(attachments)) {
            for (CommonAttachment attachment : attachments) {
                try {
                    attachment.setBusinessId(demandId);
                    attachment.setBusinessType(CommonAttachmentBusinessType.ENTERPRISE_DEMAND); // 企业需求附件的业务类型
                    attachment.setUploadBy(userId); // 设置上传者ID
                    attachment.setUploadDate(new Date()); // 设置上传时间
                    
                    attachmentService.insertCommonAttachment(attachment);
                } catch (Exception e) {
                    throw new RuntimeException("更新需求附件失败: " + e.getMessage(), e);
                }
            }
        }
        
        return rows;
    }

    /**
     * 删除需求
     * 
     * @param demandId 需要删除的需求主键
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteEnterpriseDemandById(Long demandId, Long userId)
    {
        EnterpriseDemand demand = selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权删除其他用户的需求");
        }
        if (!EnterpriseDemandStatus.PENDING.getCode().equals(demand.getStatus())) {
            throw new ServiceException("只有待审核的需求可以删除");
        }
        return enterpriseDemandMapper.deleteEnterpriseDemandById(demandId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> getDemandDetail(Long demandId, Long userId) {
        // 获取需求信息
        EnterpriseDemand demand = selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }
        
        // 验证权限：需求创建者或被邀请的用户都可以访问
        boolean hasPermission = false;
        
        // 检查是否是需求创建者
        if (demand.getPortalUserId().equals(userId)) {
            hasPermission = true;
        } else {
            // 检查是否是被邀请的用户
            DemandSolutionInvite inviteQuery = new DemandSolutionInvite();
            inviteQuery.setDemandId(demandId);
            inviteQuery.setInvitedUserId(userId);
            List<DemandSolutionInvite> invites = demandSolutionInviteMapper.selectDemandSolutionInviteList(inviteQuery);
            
            // 如果存在邀请记录，则允许访问
            if (!invites.isEmpty()) {
                hasPermission = true;
            }
        }
        
        if (!hasPermission) {
            throw new ServiceException("无权访问该需求");
        }

        // 获取需求的附件列表
        CommonAttachment attachmentQuery = new CommonAttachment();
        attachmentQuery.setBusinessType(CommonAttachmentBusinessType.ENTERPRISE_DEMAND);
        attachmentQuery.setBusinessId(demandId);
        List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(attachmentQuery);
        demand.setAttachments(attachments);

        // 获取关联的解决方案列表
        DemandSolution solutionQuery = new DemandSolution();
        solutionQuery.setDemandId(demandId);
        List<DemandSolution> allSolutions = demandSolutionService.selectDemandSolutionList(solutionQuery);
        // 只保留状态为1及以上的方案
        List<DemandSolution> solutions = allSolutions.stream()
            .filter(s -> {
                try {
                    return !s.getStatus().equals(DemandSolutionStatus.PENDING.getCode());
                } catch (Exception e) {
                    return false;
                }
            })
            .collect(Collectors.toList());
        demand.setSolutionCount(solutions.size());
        
        // 为每个解决方案获取其附件列表
        for (DemandSolution solution : solutions) {
            CommonAttachment solutionAttachmentQuery = new CommonAttachment();
            solutionAttachmentQuery.setBusinessType(CommonAttachmentBusinessType.DEMAND_SOLUTION);
            solutionAttachmentQuery.setBusinessId(solution.getSolutionId());
            List<CommonAttachment> solutionAttachments = attachmentService.selectCommonAttachmentList(solutionAttachmentQuery);
            solution.setAttachments(solutionAttachments);
        }

        // 获取当前需求的合同（如有）
        DemandContract contract = demandContractMapper.selectDemandContractByDemandId(demandId);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("demand", demand);
        result.put("solutions", solutions);
        result.put("contract", contract);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EnterpriseDemand createEnterpriseDemand(EnterpriseDemand enterpriseDemand) {
        // 校验截止日期是否合法（不能早于当前日期）
        if (DateUtils.compareDate(enterpriseDemand.getDeliveryTime(), DateUtils.getNowDate()) < 0) {
            throw new ServiceException("期望交付时间不能早于当前日期");
        }

        // 校验动态属性最大长度
        if (enterpriseDemand.getSpecificRequirements() != null && enterpriseDemand.getSpecificRequirements().size() > 5) {
            throw new ServiceException("具体需求最多只能填写5条");
        }
        if (enterpriseDemand.getTechnicalIndicators() != null && enterpriseDemand.getTechnicalIndicators().size() > 5) {
            throw new ServiceException("技术指标最多只能填写5条");
        }
        if (enterpriseDemand.getOtherRequirements() != null && enterpriseDemand.getOtherRequirements().size() > 5) {
            throw new ServiceException("其他要求最多只能填写5条");
        }

        // 设置基础信息
        enterpriseDemand.setStatus(EnterpriseDemandStatus.PENDING.getCode());  // 待审核状态
        enterpriseDemand.setCreateBy(SecurityUtils.getUsername());

        // 插入需求
        int rows = insertEnterpriseDemand(enterpriseDemand);
        if (rows <= 0) {
            throw new ServiceException("需求发布失败，请稍后重试");
        }

        // 查询并返回创建的需求信息
        return selectEnterpriseDemandById(enterpriseDemand.getDemandId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int completeEnterpriseDemand(Long demandId, Long userId) {
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权限操作该需求");
        }
        // Add logic for which statuses can be completed
        if (!EnterpriseDemandStatus.IN_PROGRESS.getCode().equals(demand.getStatus())) {
             throw new ServiceException("只有进行中的需求才能完成");
        }
        demand.setStatus(EnterpriseDemandStatus.COMPLETED.getCode());
        demand.setUpdateTime(new Date());
        demand.setUpdateBy(SecurityUtils.getUsername()); // Or however updateBy is usually set
        enterpriseDemandMapper.updateEnterpriseDemand(demand);

        //同步更新方案状态
        DemandSolution solution = new DemandSolution();
        solution.setDemandId(demandId);
        solution.setStatus(DemandSolutionStatus.COMPLETED.getCode());
        demandSolutionService.updateDemandSolution(solution);

        //同步更新合同状态
        DemandContract contract = demandContractMapper.selectDemandContractByDemandId(demandId);
        if (contract != null) {
            contract.setStatus(DemandContractStatus.COMPLETED.getCode());
            demandContractMapper.updateDemandContract(contract);
        }

        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int publishDemandAndStartBidding(Long demandId, Long userId) throws ServiceException {
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);

        if (demand == null) {
            throw new ServiceException("需求不存在 (ID: " + demandId + ")");
        }

        // Permission Check: Only the demand owner can publish it
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("您没有权限发布此需求");
        }

        // Status Check: Demand must be in "STATUS_WAIT_PUBLIC" state
        if (!EnterpriseDemandStatus.WAIT_PUBLIC.getCode().equals(demand.getStatus())) {
            String errorMessage = "需求状态不正确，只有 \"待公开\" 的需求才能发布并开始投标。当前状态：" + demand.getStatus();
            throw new ServiceException(errorMessage);
        }

        // Update status to "STATUS_BIDDING"
        demand.setStatus(EnterpriseDemandStatus.BIDDING.getCode());
        demand.setUpdateBy(SecurityUtils.getUsername()); // Set updater, ensure SecurityUtils.getUsername() is appropriate

        int rows = enterpriseDemandMapper.updateEnterpriseDemand(demand);
        if (rows == 0) {
            throw new ServiceException("发布需求失败，数据可能已被修改或不存在");
        }
        return rows;
    }

    /**
     * 选择中标方案
     * 
     * @param demandId 需求ID
     * @param solutionId 方案ID
     * @param userId 操作用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int selectSolution(Long demandId, Long solutionId, Long userId) {
        // 查询需求信息
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }

        // 验证需求状态
        if (!EnterpriseDemandStatus.SELECTING.getCode().equals(demand.getStatus())) {
            throw new ServiceException("当前需求状态不允许选择方案");
        }

        // 验证操作权限
        if (!userId.equals(demand.getPortalUserId())) {
            throw new ServiceException("只有需求发布者可以选择中标方案");
        }

        // 查询方案信息
        DemandSolution solution = demandSolutionService.selectDemandSolutionById(solutionId);
        if (solution == null) {
            throw new ServiceException("方案不存在");
        }

        // 验证方案是否属于该需求
        if (!demandId.equals(solution.getDemandId())) {
            throw new ServiceException("方案不属于该需求");
        }

        // 更新需求状态为待平台审核
        EnterpriseDemand updateDemand = new EnterpriseDemand();
        updateDemand.setDemandId(demandId);
        updateDemand.setStatus(EnterpriseDemandStatus.PLATFORM_REVIEW.getCode());
        int rows = enterpriseDemandMapper.updateEnterpriseDemand(updateDemand);

        if (rows > 0) {
            // 更新方案状态为已中标
            DemandSolution updateSolution = new DemandSolution();
            updateSolution.setSolutionId(solutionId);
            updateSolution.setStatus(DemandSolutionStatus.SELECTED.getCode());
            demandSolutionService.updateDemandSolution(updateSolution);

            // 生成合同记录（如未存在）
            demandContractService.createContractForBid(demandId, solutionId, userId);
        }

        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int endBidding(Long demandId, Long userId) {
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }
        
        // 权限检查：只有需求所有者可以结束投标
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("您没有权限结束此需求的投标");
        }
        
        // 状态检查：需求必须处于"投标中"状态
        if (!EnterpriseDemandStatus.BIDDING.getCode().equals(demand.getStatus())) {
            throw new ServiceException("只有处于投标中状态的需求才能结束投标");
        }
        
        // 更新状态为"待签约"
        demand.setStatus(EnterpriseDemandStatus.SELECTING.getCode());
        demand.setUpdateBy(SecurityUtils.getUsername());
        
        return enterpriseDemandMapper.updateEnterpriseDemand(demand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int sign(Long demandId, DemandContract contract, Long userId) {
        // 获取需求信息
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }

        // 权限检查：只有需求所有者可以上传合同
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("您没有权限上传此需求的合同");
        }

        // 状态检查：需求必须处于"待签约"状态
        if (!EnterpriseDemandStatus.WAIT_SIGN.getCode().equals(demand.getStatus())) {
            throw new ServiceException("只有处于待签约状态的需求才能上传合同");
        }

        // 检查并删除旧合同
        DemandContract existingContract = demandContractMapper.selectDemandContractByDemandId(demandId);
        if (existingContract != null) {
            demandContractMapper.deleteDemandContractById(existingContract.getContractId());
        }

        // 设置合同基础信息
        contract.setStatus(DemandContractStatus.PENDING.getCode());  // 设置合同状态为待审核
        contract.setCreateBy(SecurityUtils.getUsername());
        contract.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());

        // 保存合同信息
        int contractRows = demandContractMapper.insertDemandContract(contract);
        if (contractRows <= 0) {
            throw new ServiceException("合同保存失败");
        }

        // 更新需求状态为"合同已上传"
        demand.setStatus(EnterpriseDemandStatus.PLATFORM_REVIEW.getCode());
        demand.setUpdateTime(new Date());
        demand.setUpdateBy(SecurityUtils.getUsername());

        // 更新需求状态
        return enterpriseDemandMapper.updateEnterpriseDemand(demand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int closeEnterpriseDemand(Long demandId, Long userId) {
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }
        if (!demand.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权限关闭该需求");
        }   
        // 只有 进行中，已完成的需求不可以关闭，其他状态的需求可以关闭
        if (EnterpriseDemandStatus.IN_PROGRESS.getCode().equals(demand.getStatus()) || EnterpriseDemandStatus.COMPLETED.getCode().equals(demand.getStatus())) { 
            throw new ServiceException("进行中或已完成的需求不可以关闭");
        }
        demand.setStatus(EnterpriseDemandStatus.CLOSED.getCode());
        demand.setUpdateTime(new Date());
        demand.setUpdateBy(SecurityUtils.getUsername());
        enterpriseDemandMapper.updateEnterpriseDemand(demand);

        //同步更新方案状态
        DemandSolution solution = new DemandSolution();
        solution.setDemandId(demandId);
        solution.setStatus(DemandSolutionStatus.BID_LOSE.getCode());
        demandSolutionService.updateDemandSolution(solution);

        return 1;
    }
} 