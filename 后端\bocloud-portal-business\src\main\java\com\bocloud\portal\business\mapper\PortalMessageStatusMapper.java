package com.bocloud.portal.business.mapper;

import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 消息状态管理Mapper接口
 */
public interface PortalMessageStatusMapper {
    /**
     * 插入或更新消息状态
     */
    int insertOrUpdateMessageStatus(@Param("messageId") Long messageId, @Param("userId") Long userId, @Param("status") String status);
    
    /**
     * 批量更新消息状态
     */
    int batchUpdateMessageStatus(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId, @Param("status") String status);

    /**
     * 批量插入或更新消息状态
     */
    int batchInsertOrUpdateMessageStatus(@Param("list") List<com.bocloud.domain.web.PortalMessageStatus> statusList);
} 