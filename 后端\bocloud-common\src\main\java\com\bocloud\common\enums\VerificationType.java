package com.bocloud.common.enums;

/**
 * 实名认证类型枚举
 * 
 * <AUTHOR>
 */
public enum VerificationType {
    
    PERSONAL("1", "个人认证"),
    ENTERPRISE("2", "企业认证");
    
    private final String code;
    private final String desc;
    
    VerificationType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static VerificationType getByCode(String code) {
        for (VerificationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        VerificationType type = getByCode(code);
        return type != null ? type.getDesc() : "";
    }
    
    /**
     * 判断是否为个人认证
     */
    public static boolean isPersonal(String code) {
        return PERSONAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为企业认证
     */
    public static boolean isEnterprise(String code) {
        return ENTERPRISE.getCode().equals(code);
    }
} 