package com.bocloud.admin.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.admin.business.mapper.SysAuditMapper;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.admin.business.service.ISysAuditService;

/**
 * 系统审核记录Service业务层处理
 */
@Service
public class SysAuditServiceImpl implements ISysAuditService {
    @Autowired
    private SysAuditMapper sysAuditMapper;

    /**
     * 查询审核记录
     */
    @Override
    public SysAudit selectSysAuditById(Long auditId) {
        return sysAuditMapper.selectSysAuditById(auditId);
    }

    /**
     * 查询审核记录列表
     */
    @Override
    public List<SysAudit> selectSysAuditList(SysAudit sysAudit) {
        return sysAuditMapper.selectSysAuditList(sysAudit);
    }

    /**
     * 新增审核记录
     */
    @Override
    public int insertSysAudit(SysAudit sysAudit) {
        return sysAuditMapper.insertSysAudit(sysAudit);
    }

    /**
     * 修改审核记录
     */
    @Override
    public int updateSysAudit(SysAudit sysAudit) {
        return sysAuditMapper.updateSysAudit(sysAudit);
    }

    /**
     * 批量删除审核记录
     */
    @Override
    public int deleteSysAuditByIds(Long[] auditIds) {
        return sysAuditMapper.deleteSysAuditByIds(auditIds);
    }

    /**
     * 删除审核记录信息
     */
    @Override
    public int deleteSysAuditById(Long auditId) {
        return sysAuditMapper.deleteSysAuditById(auditId);
    }
} 