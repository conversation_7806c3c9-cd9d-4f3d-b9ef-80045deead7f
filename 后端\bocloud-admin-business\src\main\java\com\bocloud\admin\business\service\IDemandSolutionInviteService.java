package com.bocloud.admin.business.service;

import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.domain.web.dto.SingleInviteRequest;
import com.bocloud.domain.web.BatchInviteRequest;
import java.util.List;

/**
 * 需求解决方案邀请Service接口
 */
public interface IDemandSolutionInviteService {
    /**
     * 查询邀请记录
     */
    DemandSolutionInvite selectDemandSolutionInviteById(Long id);

    /**
     * 查询邀请记录列表
     */
    List<DemandSolutionInvite> selectDemandSolutionInviteList(DemandSolutionInvite invite);

    /**
     * 新增邀请记录
     */
    int insertDemandSolutionInvite(DemandSolutionInvite invite);

    /**
     * 修改邀请记录
     */
    int updateDemandSolutionInvite(DemandSolutionInvite invite);

    /**
     * 批量新增邀请记录
     */
    int batchInsertDemandSolutionInvite(List<DemandSolutionInvite> inviteList);

    /**
     * 验证需求是否存在
     * 
     * @param demandId 需求ID
     * @return 是否存在
     */
    boolean validateDemandExists(Long demandId);

    /**
     * 验证专家是否存在且状态正常
     * 
     * @param userId 用户ID
     * @return 是否存在且状态正常
     */
    boolean validateExpertExists(Long userId);

    /**
     * 检查是否已经邀请过该专家
     * 
     * @param demandId 需求ID
     * @param userId 用户ID
     * @return 是否已邀请
     */
    boolean checkInviteExists(Long demandId, Long userId);

    /**
     * 获取已经被邀请的用户ID列表
     * 
     * @param demandId 需求ID
     * @param userIds 用户ID列表
     * @return 已被邀请的用户ID列表
     */
    List<Long> getAlreadyInvitedUsers(Long demandId, List<Long> userIds);

    /**
     * 邀请单个专家
     * 
     * @param request 邀请请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 邀请结果
     */
    DemandSolutionInvite inviteSingleExpert(SingleInviteRequest request, Long operatorId, String operatorName);

    /**
     * 批量邀请专家
     * 
     * @param request 批量邀请请求
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 成功邀请数量
     */
    int batchInviteExperts(BatchInviteRequest request, Long operatorId, String operatorName);
} 