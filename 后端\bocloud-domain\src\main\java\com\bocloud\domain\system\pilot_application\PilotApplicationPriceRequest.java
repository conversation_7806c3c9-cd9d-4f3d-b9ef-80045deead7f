package com.bocloud.domain.system.pilot_application;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中试申请报价请求对象
 * 
 * <AUTHOR>
 */
@Data
public class PilotApplicationPriceRequest
{
    /** 中试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long pilotApplicationId;

    /** 管理员报价 */
    @NotNull(message = "报价金额不能为空")
    private BigDecimal adminPrice;

    /** 报价备注 */
    private String adminPriceRemark;
} 