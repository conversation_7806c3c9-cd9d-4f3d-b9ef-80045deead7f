package com.bocloud.framework.filters;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bocloud.framework.util.JsonUtil;
import com.bocloud.framework.util.RSAUtil;
import com.bocloud.framework.util.SignUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@RestControllerAdvice
@Component
@Slf4j
public class ReqBodyAdvice implements RequestBodyAdvice {

    @Value("${encrypt}")
    private boolean encrypt;
    @Value("${signkey}")
    private String secret;
    @Value("${privatekey}")
    private String privatekey;

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        if(!encrypt || (methodParameter.getMethodAnnotation(PostMapping.class)!=null && Arrays.stream(methodParameter.getMethodAnnotation(PostMapping.class).path()).collect(Collectors.toList()).contains("/check"))){
            return false;
        }
        return true;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {

        HttpHeaders headers = httpInputMessage.getHeaders();
//       String[] url =  parameter.getContainingClass().getAnnotation(RequestMapping.class).value();
//       log.info("url:{}",url);
        //源请求参数
        String bodyStr = StreamUtils.copyToString(httpInputMessage.getBody(), Charset.forName("utf-8"));
        log.info("请求参数:{}",bodyStr);

       bodyStr =  JSONUtil.toJsonPrettyStr(bodyStr) ;
       JSONObject paramsMap = JSONUtil.parseObj(bodyStr);
       // TreeMap<String, Object> paramsMap = JsonUtil.parse(bodyStr, new TypeReference<TreeMap<String, Object>>() {});

        if(!ObjectUtils.isEmpty(paramsMap) && !ObjectUtils.isEmpty(paramsMap.get("data"))){
            String params = paramsMap.get("data").toString();
            try {
                bodyStr = RSAUtil.decrypt(params,privatekey);
                log.info("解密后的数据:{}",bodyStr);
            }catch (Exception e){
                throw new IOException("解密失败");
            }
            //转换成TreeMap结构
            TreeMap<String, String> map = JsonUtil.parse(bodyStr, new TypeReference<TreeMap<String, String>>() {});
            //校验签名
            SignUtil.verify(map, secret);
            Map<String, Object> out = new HashMap<>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                out.put(entry.getKey(), JsonUtil.read(entry.getValue()));
            }
            String outStr = JsonUtil.toStr(out);
            return new MyHttpInputMessage(headers, outStr.getBytes(Charset.forName("utf-8")));
        }
        return new MyHttpInputMessage(httpInputMessage.getHeaders(),bodyStr.getBytes());

    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    /**
     * 自定义消息体，因为org.springframework.http.HttpInputMessage#getBody()只能调一次，所以要重新封装一个可重复读的消息体
     */
    @AllArgsConstructor
    public static class MyHttpInputMessage implements HttpInputMessage {

        private HttpHeaders headers;

        private byte[] body;

        @Override
        public InputStream getBody() throws IOException {
            return new ByteArrayInputStream(body);
        }

        @Override
        public HttpHeaders getHeaders() {
            return headers;
        }
    }
}
