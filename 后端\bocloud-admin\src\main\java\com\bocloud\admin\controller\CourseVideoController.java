package com.bocloud.admin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bocloud.admin.business.service.ICourseVideoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import  com.bocloud.domain.system.online_education.CourseVideo;

/**
 * 课程视频Controller
 *
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/system/video")
public class CourseVideoController extends BaseController
{
    @Autowired
    private ICourseVideoService courseVideoService;

    /**
     * 查询课程视频列表
     */
    @PreAuthorize("@ss.hasPermi('system:video:list')")
    @GetMapping("/list")
    public TableDataInfo list(CourseVideo courseVideo)
    {
        startPage();
        List<CourseVideo> list = courseVideoService.selectCourseVideoList(courseVideo);
        return getDataTable(list);
    }

    /**
     * 导出课程视频列表
     */
    @PreAuthorize("@ss.hasPermi('system:video:export')")
    @Log(title = "课程视频", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseVideo courseVideo)
    {
        List<CourseVideo> list = courseVideoService.selectCourseVideoList(courseVideo);
        ExcelUtil<CourseVideo> util = new ExcelUtil<CourseVideo>(CourseVideo.class);
        util.exportExcel(response, list, "课程视频数据");
    }

    /**
     * 获取课程视频详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:video:query')")
    @GetMapping(value = "/{videoId}")
    public AjaxResult getInfo(@PathVariable("videoId") Integer videoId)
    {
        return success(courseVideoService.selectCourseVideoByVideoId(videoId));
    }

    /**
     * 新增课程视频
     */
    @PreAuthorize("@ss.hasPermi('system:video:add')")
    @Log(title = "课程视频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseVideo courseVideo)
    {
        return toAjax(courseVideoService.insertCourseVideo(courseVideo));
    }

    /**
     * 修改课程视频
     */
    @PreAuthorize("@ss.hasPermi('system:video:edit')")
    @Log(title = "课程视频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseVideo courseVideo)
    {
        return toAjax(courseVideoService.updateCourseVideo(courseVideo));
    }

    /**
     * 删除课程视频
     */
    @PreAuthorize("@ss.hasPermi('system:video:remove')")
    @Log(title = "课程视频", businessType = BusinessType.DELETE)
    @DeleteMapping("/{videoIds}")
    public AjaxResult remove(@PathVariable Integer[] videoIds)
    {
        return toAjax(courseVideoService.deleteCourseVideoByVideoIds(videoIds));
    }
} 