package com.bocloud.common.enums;

public enum AuditStatus {
    //审核状态，通过是1 不通过是-1
    NOT_PASS("-1", "不通过"), PASSED("1", "通过");

    private final String code;
    private final String info;

    AuditStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
