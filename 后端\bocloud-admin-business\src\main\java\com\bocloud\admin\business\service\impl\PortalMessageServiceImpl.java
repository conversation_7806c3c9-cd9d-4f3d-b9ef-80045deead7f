package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.PortalMessageMapper;
import com.bocloud.admin.business.mapper.PortalMessageStatusMapper;
import com.bocloud.admin.business.service.IPortalMessageService;
import com.bocloud.common.core.domain.model.LoginUser;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PortalMessageServiceImpl implements IPortalMessageService {
    @Autowired
    private PortalMessageMapper portalMessageMapper;

    @Autowired
    private PortalMessageStatusMapper portalMessageStatusMapper;

    @Override
    public List<PortalMessage> selectPortalMessageList(PortalMessageQuery query) {
        return portalMessageMapper.selectPortalMessageList(query);
    }

    @Override
    public PortalMessage selectPortalMessageById(Long messageId) {
        // 获取消息详情
        PortalMessage message = portalMessageMapper.selectPortalMessageById(messageId);
        if (message != null) {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();
            // 查询消息状态
            String status = portalMessageStatusMapper.selectMessageStatus(messageId, userId);
            if (status == null) {
                // 如果状态不存在，创建未读状态
                portalMessageStatusMapper.insertOrUpdateMessageStatus(messageId, userId, "0");
            }
        }
        return message;
    }

    @Override
    @Transactional
    public int insertPortalMessage(PortalMessage message) {
        // 设置发送者ID
        LoginUser loginUser = SecurityUtils.getLoginUser();
        message.setSenderId(loginUser.getUserId());
        message.setCreateBy(loginUser.getUsername());
        
        // 插入消息
        return portalMessageMapper.insertPortalMessage(message);
    }

    @Override
    public int updatePortalMessage(PortalMessage message) {
        message.setUpdateBy(SecurityUtils.getUsername());
        return portalMessageMapper.updatePortalMessage(message);
    }

    @Override
    public int deletePortalMessageById(Long messageId) {
        return portalMessageMapper.deletePortalMessageById(messageId);
    }
} 