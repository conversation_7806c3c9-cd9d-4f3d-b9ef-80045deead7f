package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.AdPositionsMapper;
import com.bocloud.common.utils.DateUtils;
import  com.bocloud.domain.system.platform_service.AdPositions;
import com.bocloud.admin.business.service.IAdPositionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告位置信息Service业务层处理
 *
 * @date 2025-03-11
 */
@Service
public class AdPositionsServiceImpl implements IAdPositionsService
{
    @Autowired
    private AdPositionsMapper adPositionsMapper;

    /**
     * 查询广告位置信息
     *
     * @param positionId 广告位置信息主键
     * @return 广告位置信息
     */
    @Override
    public AdPositions selectAdPositionsByPositionId(Long positionId)
    {
        return adPositionsMapper.selectAdPositionsByPositionId(positionId);
    }

    /**
     * 查询广告位置信息列表
     *
     * @param adPositions 广告位置信息
     * @return 广告位置信息
     */
    @Override
    public List<AdPositions> selectAdPositionsList(AdPositions adPositions)
    {
        return adPositionsMapper.selectAdPositionsList(adPositions);
    }

    /**
     * 新增广告位置信息
     *
     * @param adPositions 广告位置信息
     * @return 结果
     */
    @Override
    public int insertAdPositions(AdPositions adPositions)
    {
        adPositions.setCreateTime(DateUtils.getNowDate());
        return adPositionsMapper.insertAdPositions(adPositions);
    }

    /**
     * 修改广告位置信息
     *
     * @param adPositions 广告位置信息
     * @return 结果
     */
    @Override
    public int updateAdPositions(AdPositions adPositions)
    {
        adPositions.setUpdateTime(DateUtils.getNowDate());
        return adPositionsMapper.updateAdPositions(adPositions);
    }

    /**
     * 批量删除广告位置信息
     *
     * @param positionIds 需要删除的广告位置信息主键
     * @return 结果
     */
    @Override
    public int deleteAdPositionsByPositionIds(Long[] positionIds)
    {
        return adPositionsMapper.deleteAdPositionsByPositionIds(positionIds);
    }

    /**
     * 删除广告位置信息信息
     *
     * @param positionId 广告位置信息主键
     * @return 结果
     */
    @Override
    public int deleteAdPositionsByPositionId(Long positionId)
    {
        return adPositionsMapper.deleteAdPositionsByPositionId(positionId);
    }
}