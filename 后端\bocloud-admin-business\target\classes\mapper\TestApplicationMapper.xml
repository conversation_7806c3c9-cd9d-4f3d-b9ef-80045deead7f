<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.TestApplicationMapper">

    <resultMap type="TestApplication" id="TestApplicationResult">
        <result property="id"    column="id"    />
        <result property="enterpriseName"    column="enterprise_name"    />
        <result property="testProjectName"    column="test_project_name"    />
        <result property="testType"    column="test_type"    />
        <result property="expectedCompletionTime"    column="expected_completion_time"    />
        <result property="sampleDesc"    column="sample_desc"    />
        <result property="testPurpose"    column="test_purpose"    />
        <result property="minBudget" column="min_budget" />
        <result property="maxBudget" column="max_budget" />
        <result property="timeRequirement"    column="time_requirement"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="status"    column="status"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="adminPrice" column="admin_price" />
        <result property="confirmedAmount" column="confirmed_amount" />
        <result property="auditPaymentDate" column="audit_payment_date" />
        <result property="paymentVoucher" column="payment_voucher" />
        <result property="adminPriceRemark" column="admin_price_remark" />
        <result property="confirmPaymentRemark" column="confirm_payment_remark" />
        <result property="paymentRejectReason" column="payment_reject_reason" />
        <result property="reviewComment"    column="review_comment"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTestApplicationVo">
        select id, enterprise_name, test_project_name, test_type, expected_completion_time, 
        sample_desc, test_purpose, min_budget, max_budget, time_requirement, contact_name, contact_phone, 
        contact_email, status, payment_status, admin_price, confirmed_amount, audit_payment_date, payment_voucher, admin_price_remark, confirm_payment_remark, payment_reject_reason, review_comment, remark, del_flag, create_by, create_time, update_by, update_time 
        from test_application
    </sql>

    <select id="selectTestApplicationList" parameterType="TestApplication" resultMap="TestApplicationResult">
        <include refid="selectTestApplicationVo"/>
        <where>
            <if test="enterpriseName != null and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="testProjectName != null and testProjectName != ''"> and test_project_name like concat('%', #{testProjectName}, '%')</if>
            <if test="testType != null and testType != ''"> and test_type = #{testType}</if>
            <if test="expectedCompletionTime != null"> and expected_completion_time = #{expectedCompletionTime}</if>
            <if test="sampleDesc != null and sampleDesc != ''"> and sample_desc = #{sampleDesc}</if>
            <if test="testPurpose != null and testPurpose != ''"> and test_purpose = #{testPurpose}</if>
            <if test="minBudget != null and minBudget != ''"> and min_budget = #{minBudget}</if>
            <if test="maxBudget != null and maxBudget != ''"> and max_budget = #{maxBudget}</if>
            <if test="timeRequirement != null and timeRequirement != ''"> and time_requirement = #{timeRequirement}</if>
            <if test="contactName != null and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="contactEmail != null and contactEmail != ''"> and contact_email = #{contactEmail}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="adminPrice != null"> and admin_price = #{adminPrice}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectTestApplicationById" parameterType="Long" resultMap="TestApplicationResult">
        <include refid="selectTestApplicationVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <update id="updateTestApplication" parameterType="TestApplication">
        update test_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="enterpriseName != null and enterpriseName != ''">enterprise_name = #{enterpriseName},</if>
            <if test="testProjectName != null and testProjectName != ''">test_project_name = #{testProjectName},</if>
            <if test="testType != null and testType != ''">test_type = #{testType},</if>
            <if test="expectedCompletionTime != null">expected_completion_time = #{expectedCompletionTime},</if>
            <if test="sampleDesc != null and sampleDesc != ''">sample_desc = #{sampleDesc},</if>
            <if test="testPurpose != null and testPurpose != ''">test_purpose = #{testPurpose},</if>
            <if test="minBudget != null and minBudget != ''">min_budget = #{minBudget},</if>
            <if test="maxBudget != null and maxBudget != ''">max_budget = #{maxBudget},</if>
            <if test="timeRequirement != null and timeRequirement != ''">time_requirement = #{timeRequirement},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="paymentStatus != null and paymentStatus != ''">payment_status = #{paymentStatus},</if>
            <if test="adminPrice != null">admin_price = #{adminPrice},</if>
            <if test="confirmedAmount != null">confirmed_amount = #{confirmedAmount},</if>
            <if test="auditPaymentDate != null">audit_payment_date = #{auditPaymentDate},</if>
            <if test="paymentVoucher != null and paymentVoucher != ''">payment_voucher = #{paymentVoucher},</if>
            <if test="adminPriceRemark != null and adminPriceRemark != ''">admin_price_remark = #{adminPriceRemark},</if>
            <if test="confirmPaymentRemark != null and confirmPaymentRemark != ''">confirm_payment_remark = #{confirmPaymentRemark},</if>
            <if test="paymentRejectReason != null">payment_reject_reason = #{paymentRejectReason},</if>
            <if test="reviewComment != null and reviewComment != ''">review_comment = #{reviewComment},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

</mapper>