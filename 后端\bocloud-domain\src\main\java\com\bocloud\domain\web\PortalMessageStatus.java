package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PortalMessageStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 状态ID */
    private Long statusId;

    /** 消息ID */
    private Long messageId;

    /** 用户ID */
    private Long userId;

    /** 消息状态（0=未读 1=已读） */
    private String status;

    /** 消息ID列表（用于批量操作） */
    private List<Long> messageIds;
} 