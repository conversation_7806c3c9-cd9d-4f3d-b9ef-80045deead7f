package com.bocloud.common.enums;

/**
 * 验证码类型枚举
 */
public enum VerifyCodeType {
    /**
     * 修改手机号
     */
    CHANGE_PHONE("change_phone", "修改手机号"),

    /**
     * 修改密码
     */
    CHANGE_PASSWORD("change_password", "修改密码"),

    /**
     * 修改邮箱
     */
    CHANGE_EMAIL("change_email", "修改邮箱"),

    /**
     * 修改企业信息
     */
    CHANGE_ENTERPRISE("change_enterprise", "修改企业信息"),

    /**
     * 修改专家信息
     */
    CHANGE_EXPERT("change_expert", "修改专家信息");

    private final String code;
    private final String desc;

    VerifyCodeType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static VerifyCodeType getByCode(String code) {
        for (VerifyCodeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 