package com.bocloud.portal.business.mapper;

import com.bocloud.domain.web.DemandSolutionInvite;
import java.util.List;

/**
 * 需求解决方案邀请Mapper接口
 */
public interface DemandSolutionInviteMapper {
    /**
     * 查询邀请记录
     */
    DemandSolutionInvite selectDemandSolutionInviteById(Long id);

    /**
     * 查询邀请记录列表
     */
    List<DemandSolutionInvite> selectDemandSolutionInviteList(DemandSolutionInvite invite);

    /**
     * 新增邀请记录
     */
    int insertDemandSolutionInvite(DemandSolutionInvite invite);

    /**
     * 修改邀请记录
     */
    int updateDemandSolutionInvite(DemandSolutionInvite invite);

    /**
     * 删除邀请记录
     */
    int deleteDemandSolutionInviteById(Long id);

    /**
     * 批量删除邀请记录
     */
    int deleteDemandSolutionInviteByIds(Long[] ids);
} 