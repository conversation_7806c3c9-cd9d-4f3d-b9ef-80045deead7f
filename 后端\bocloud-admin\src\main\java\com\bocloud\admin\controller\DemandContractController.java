package com.bocloud.admin.controller;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.domain.system.demand.dto.ContractAuditDTO;
import com.bocloud.admin.business.service.IDemandContractService;

/**
 * 需求合同Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/demand/contract")
public class DemandContractController extends BaseController {
    @Autowired
    private IDemandContractService demandContractService;

    /**
     * 查询需求合同列表
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:list')")
    @GetMapping("/list")
    public AjaxResult list(DemandContract demandContract) {
        List<DemandContract> list = demandContractService.selectDemandContractList(demandContract);
        return success(list);
    }

    /**
     * 获取需求合同详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:query')")
    @GetMapping(value = "/{contractId}")
    public AjaxResult getInfo(@PathVariable("contractId") Long contractId) {
        return success(demandContractService.selectDemandContractById(contractId));
    }

    /**
     * 修改需求合同
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:edit')")
    @Log(title = "需求合同", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DemandContract demandContract) {
        return toAjax(demandContractService.updateDemandContract(demandContract));
    }

    /**
     * 批量审核通过
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:approve')")
    @Log(title = "需求合同", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody Map<String, Object> params) {
        Long[] contractIds = (Long[]) params.get("contractIds");
        String chargeType = (String) params.get("chargeType");
        if ("1".equals(chargeType)) {
            // 固定收费
            return toAjax(demandContractService.approveContracts(contractIds, chargeType, (BigDecimal) params.get("fixedAmount")));
        } else {
            // 比例收费
            return toAjax(demandContractService.approveContracts(contractIds, chargeType, null));
        }
    }

    /**
     * 合同审核（通过/不通过，单个审核）
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:audit')")
    @Log(title = "需求合同", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody ContractAuditDTO auditDTO) {
        return toAjax(demandContractService.auditContract(auditDTO));
    }

    /**
     * 根据需求ID获取合同信息
     */
    @PreAuthorize("@ss.hasPermi('admin:business:demand:contract:query')")
    @GetMapping("/by-demand/{demandId}")
    public AjaxResult getByDemandId(@PathVariable("demandId") Long demandId) {
        return success(demandContractService.selectDemandContractByDemandId(demandId));
    }
} 