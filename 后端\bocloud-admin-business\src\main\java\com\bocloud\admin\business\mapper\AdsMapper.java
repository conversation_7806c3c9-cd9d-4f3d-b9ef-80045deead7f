package com.bocloud.admin.business.mapper;

import com.bocloud.domain.system.platform_service.Ads;

import java.util.List;

/**
 * 广告申请Mapper接口
 *
 * @date 2025-03-11
 */
public interface AdsMapper
{
    /**
     * 查询广告申请
     *
     * @param adId 广告申请主键
     * @return 广告申请
     */
    public Ads selectAdsByAdId(Long adId);

    /**
     * 查询广告申请列表
     *
     * @param ads 广告申请
     * @return 广告申请集合
     */
    public List<Ads> selectAdsList(Ads ads);

    /**
     * 新增广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    public int insertAds(Ads ads);

    /**
     * 修改广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    public int updateAds(Ads ads);

    /**
     * 删除广告申请
     *
     * @param adId 广告申请主键
     * @return 结果
     */
    public int deleteAdsByAdId(Long adId);

    /**
     * 批量删除广告申请
     *
     * @param adIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdsByAdIds(Long[] adIds);
}