<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.EnterpriseInfoMapper">
    
    <resultMap type="com.bocloud.domain.web.EnterpriseInfo" id="EnterpriseInfoResult">
        <id     property="enterpriseId"        column="enterprise_id"       />
        <result property="userId"              column="user_id"             />
        <result property="companyName"         column="company_name"        />
        <result property="creditCode"          column="credit_code"         />
        <result property="creditImageUrl"      column="credit_image_url"    />
        <result property="companyType"         column="company_type"        />
        <result property="province"            column="province"            />
        <result property="city"                column="city"                />
        <result property="district"            column="district"            />
        <result property="detailAddress"       column="detail_address"      />
        <result property="registeredCapital"   column="registered_capital"  />
        <result property="establishDate"       column="establish_date"      />
        <result property="companySize"         column="company_size"        />
        <result property="industry"            column="industry"            />
        <result property="website"             column="website"             />
        <result property="legalPersonName"         column="legal_person_name"        />
        <result property="legalPersonIdType"       column="legal_person_id_type"      />
        <result property="legalPersonImageUrl"     column="legal_person_image_url"    />
        <result property="legalPersonIdNumber"     column="legal_person_id_number"    />
        <result property="legalPersonIdCardPortrait" column="legal_person_id_card_portrait"/>
        <result property="legalPersonIdCardEmblem" column="legal_person_id_card_emblem"/>
        <result property="legalPersonPhone"        column="legal_person_phone"         />
        <result property="contactName"         column="contact_name"        />
        <result property="contactPosition"     column="contact_position"    />
        <result property="contactPhone"        column="contact_phone"       />
        <result property="contactEmail"        column="contact_email"       />
        <result property="businessLicense"     column="business_license"    />
        <result property="status"              column="status"              />
        <result property="delFlag"             column="del_flag"            />
        <result property="active"              column="active"              />
        <result property="businessScope" column="business_scope"/>
        <result property="introduction" column="introduction"/>
        <result property="createBy"            column="create_by"           />
        <result property="createTime"          column="create_time"         />
        <result property="updateBy"            column="update_by"           />
        <result property="updateTime"          column="update_time"         />
    </resultMap>

    <sql id="selectEnterpriseInfoVo">
        select enterprise_id, user_id, company_name, credit_code, credit_image_url, company_type, province, city, district, detail_address, registered_capital,
        establish_date, company_size, industry, website, legal_person_name, legal_person_id_type, legal_person_image_url, legal_person_id_number,
        legal_person_id_card_portrait, legal_person_id_card_emblem, legal_person_phone, contact_name, contact_position, contact_phone,
        contact_email, business_license, status, del_flag, active, business_scope, introduction, create_by, create_time, update_by, update_time
        from enterprise_info
    </sql>

    <select id="selectActiveByUserId" parameterType="Long" resultMap="EnterpriseInfoResult">
        <include refid="selectEnterpriseInfoVo"/>
        where user_id = #{userId} 
        and status = '1'
        and active = '1'
        and del_flag = '0'
        order by create_time desc
        limit 1
    </select>

    <select id="selectEnterpriseInfoByUserId" parameterType="Long" resultMap="EnterpriseInfoResult">
        <include refid="selectEnterpriseInfoVo"/>
        where user_id = #{userId} 
        and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectLatestEnterpriseInfoByUserId" parameterType="Long" resultMap="EnterpriseInfoResult">
        select e.*
        from enterprise_info e
        left join sys_verification_status v on e.user_id = v.user_id and v.verification_type = '2'
        where e.user_id = #{userId} and e.del_flag = '0'
        order by 
            case when v.status = '3' then 1  -- 已认证的记录优先
                 when v.status = '2' then 2  -- 其次是待审核的记录
                 else 3                      -- 最后是其他状态的记录
            end,
            e.create_time desc               -- 同状态的记录按创建时间倒序
        limit 1
    </select>

    <select id="selectLatestByUserIdAndStatus" resultMap="EnterpriseInfoResult">
        <include refid="selectEnterpriseInfoVo"/>
        where user_id = #{userId} 
        and status = #{status}
        and del_flag = '0'
        order by create_time desc
        limit 1
    </select>

    <update id="updateEnterpriseInfo" parameterType="EnterpriseInfo">
        update enterprise_info
        <set>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="companyType != null and companyType != ''">company_type = #{companyType},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address = #{detailAddress},</if>
            <if test="registeredCapital != null and registeredCapital != ''">registered_capital = #{registeredCapital},</if>
            <if test="establishDate != null">establish_date = #{establishDate},</if>
            <if test="companySize != null and companySize != ''">company_size = #{companySize},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="website != null and website != ''">website = #{website},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPosition != null and contactPosition != ''">contact_position = #{contactPosition},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="active != null and active != ''">active = #{active},</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="introduction != null">introduction,</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where enterprise_id = #{enterpriseId} and del_flag = '0'
    </update>

    

</mapper> 