package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.domain.model.LoginUser;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.dto.PersonalVerificationDTO;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会员中心个人信息控制器
 */
@RestController
@RequestMapping("/membercenter/personal")
public class MemberCenterPersonalInfoController extends BaseController {

    @Autowired
    private IPortalUserPersonalInfoService personalInfoService;

    /**
     * 获取个人信息
     */
    @GetMapping("/info")
    public AjaxResult getPersonalInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        PersonalInfo personalInfo = personalInfoService.selectBestPersonalInfoByUserId(userId);
        return AjaxResult.success(personalInfo);
    }

    /**
     * 更新个人信息
     */
    @PutMapping("/info")
    public AjaxResult updatePersonalInfo(@RequestBody PersonalInfo personalInfo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        
        // 设置用户ID，确保只能更新自己的信息
        personalInfo.setUserId(userId);
        
        // 调用Service方法进行插入或更新
        int rows = personalInfoService.insertOrUpdatePersonalInfo(personalInfo, loginUser.getUsername());
        
        return rows > 0 ? AjaxResult.success() : AjaxResult.error("更新个人信息失败");
    }

    /**
     * 提交个人认证
     */
    @PostMapping("/verify")
    public AjaxResult submitPersonalVerification(@Validated @RequestBody PersonalVerificationDTO verificationDTO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        
        int result = personalInfoService.submitPersonalVerification(userId, verificationDTO, loginUser.getUsername());
        
        if (result > 0) {
            return AjaxResult.success();
        } else if (result == -1) {
            return AjaxResult.error("您已有待审核的认证信息，请勿重复提交");
        } else {
            return AjaxResult.error("提交认证信息失败");
        }
    }
}
