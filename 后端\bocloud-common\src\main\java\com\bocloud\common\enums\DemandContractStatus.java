package com.bocloud.common.enums;

/**
 * 需求合同状态枚举
 * 
 * <AUTHOR>
 */
public enum DemandContractStatus {
    
    // 合同状态
    TO_SIGN("0", "待签约"),
    PENDING("1", "待审核"),
    EFFECTIVE("2", "进行中"),
    COMPLETED("3", "已完成"),
    REJECTED("-1", "审核不通过");
    
    private final String code;
    private final String desc;
    
    DemandContractStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static DemandContractStatus getByCode(String code) {
        for (DemandContractStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        DemandContractStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断是否为待签约
     */
    public static boolean isToSign(String code) {
        return TO_SIGN.getCode().equals(code);
    }
    
    /**
     * 判断是否为待审核
     */
    public static boolean isPending(String code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为进行中
     */
    public static boolean isEffective(String code) {
        return EFFECTIVE.getCode().equals(code);
    }

    /**
     * 判断是否为已完成
     */
    public static boolean isCompleted(String code) {
        return COMPLETED.getCode().equals(code);
    }
    
    /**
     * 判断是否为审核不通过
     */
    public static boolean isRejected(String code) {
        return REJECTED.getCode().equals(code);
    }
    
    /**
     * 判断是否为可编辑状态（待签约、待审核）
     */
    public static boolean isEditableStatus(String code) {
        return isToSign(code) || isPending(code);
    }
    
    /**
     * 判断是否为最终状态（已完成、审核不通过）
     */
    public static boolean isFinalStatus(String code) {
        return isCompleted(code) || isRejected(code);
    }
    
    /**
     * 判断是否为活跃状态（进行中）
     */
    public static boolean isActiveStatus(String code) {
        return isEffective(code);
    }
} 