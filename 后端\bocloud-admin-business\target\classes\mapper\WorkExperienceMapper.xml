<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.WorkExperienceMapper">
    
    <resultMap type="com.bocloud.domain.web.ExpertWorkExperience" id="WorkExperienceResult">
        <id     property="experienceId" column="experience_id" />
        <result property="userId"       column="user_id"       />
        <result property="companyName"  column="company_name"  />
        <result property="position"     column="position"      />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"      column="end_date"      />
        <result property="description"  column="description"   />
        <result property="delFlag"      column="del_flag"      />
        <result property="createBy"     column="create_by"     />
        <result property="createTime"   column="create_time"   />
        <result property="updateBy"     column="update_by"     />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <select id="selectWorkExperienceList" parameterType="com.bocloud.domain.web.ExpertWorkExperience" resultMap="WorkExperienceResult">
        select experience_id, user_id, company_name, position, start_date, end_date, description,
               del_flag, create_by, create_time, update_by, update_time
        from work_experience
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            AND del_flag = '0'
        </where>
        order by start_date desc
    </select>

</mapper> 