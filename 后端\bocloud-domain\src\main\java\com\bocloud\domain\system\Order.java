package com.bocloud.domain.system;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 order
 * 
 * <AUTHOR>
 */
@Data
public class Order extends BaseEntity {
    private static final long serialVersionUID = 1L;

    // 订单状态常量
    public static final String STATUS_PENDING = "0";      // 待支付
    public static final String STATUS_PAID = "1";         // 已支付
    public static final String STATUS_CANCELLED = "2";    // 已取消
    public static final String STATUS_REFUNDED = "3";     // 已退款

    // 支付渠道常量
    public static final String PAY_CHANNEL_WECHAT = "0";      // 微信支付
    public static final String PAY_CHANNEL_ALIPAY = "1";      // 支付宝
    public static final String PAY_CHANNEL_OFFLINE = "2";    // 线下支付
    public static final String PAY_CHANNEL_BANK_TRANSFER = "3"; // 公对公汇款

    /** 订单主键ID */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 业务类型（如DEMAND_CONTRACT） */
    private String businessType;

    /** 关联业务ID（如需求ID、合同ID等） */
    private Long businessId;

    /** 下单用户ID */
    private Long userId;

    /** 订单金额 */
    private BigDecimal amount;

    /** 订单状态（0-待支付，1-已支付，2-已取消，3-已退款） */
    private String status;

    /** 支付时间 */
    private Date payTime;

    /** 支付渠道（0-微信，1-支付宝，2-线下，3-公对公汇款） */
    private String payChannel;

    /** 退款金额 */
    private BigDecimal refundAmount;

    /** 退款时间 */
    private Date refundTime;

    /** 备注 */
    private String remark;

    /** 支付凭证附件URL（线下支付时上传的凭证图片或文件） */
    private String paymentProofUrl;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

    // ... 其他字段 ...

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("businessType", getBusinessType())
            .append("businessId", getBusinessId())
            .append("userId", getUserId())
            .append("amount", getAmount())
            .append("status", getStatus())
            .append("payTime", getPayTime())
            .append("payChannel", getPayChannel())
            .append("refundAmount", getRefundAmount())
            .append("refundTime", getRefundTime())
            .append("remark", getRemark())
            .append("paymentProofUrl", getPaymentProofUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 