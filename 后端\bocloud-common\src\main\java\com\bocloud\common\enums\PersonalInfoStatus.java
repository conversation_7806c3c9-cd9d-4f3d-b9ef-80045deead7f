package com.bocloud.common.enums;

/**
 * 个人信息状态枚举
 * 
 * <AUTHOR>
 */
public enum PersonalInfoStatus {
    
    // 个人信息状态
    STATUS_PENDING("0", "待审核"),
    STATUS_NORMAL("1", "正常"),
    STATUS_REJECTED("-1", "未通过"),
    
    // 是否当前有效
    ACTIVE_NO("0", "否"),
    ACTIVE_YES("1", "是");
    
    private final String code;
    private final String desc;
    
    PersonalInfoStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static PersonalInfoStatus getByCode(String code) {
        for (PersonalInfoStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        PersonalInfoStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断是否为待审核状态
     */
    public static boolean isPending(String code) {
        return STATUS_PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为正常状态
     */
    public static boolean isNormal(String code) {
        return STATUS_NORMAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为拒绝状态
     */
    public static boolean isRejected(String code) {
        return STATUS_REJECTED.getCode().equals(code);
    }
    
    /**
     * 判断是否为当前有效
     */
    public static boolean isActive(String code) {
        return ACTIVE_YES.getCode().equals(code);
    }
} 