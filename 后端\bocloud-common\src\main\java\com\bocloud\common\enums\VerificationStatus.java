package com.bocloud.common.enums;

/**
 * 实名认证状态枚举
 * 
 * <AUTHOR>
 */
public enum VerificationStatus {
    
    PENDING("1", "待认证"),
    REVIEWING("2", "审核中"),
    APPROVED("3", "已通过"),
    REJECTED("-1", "已拒绝");
    
    private final String code;
    private final String desc;
    
    VerificationStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static VerificationStatus getByCode(String code) {
        for (VerificationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        VerificationStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断认证是否通过
     */
    public static boolean isApproved(String code) {
        return APPROVED.getCode().equals(code);
    }
    
    /**
     * 判断是否为待认证状态
     */
    public static boolean isPending(String code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为审核中状态
     */
    public static boolean isReviewing(String code) {
        return REVIEWING.getCode().equals(code);
    }
    
    /**
     * 判断认证是否被拒绝
     */
    public static boolean isRejected(String code) {
        return REJECTED.getCode().equals(code);
    }
} 