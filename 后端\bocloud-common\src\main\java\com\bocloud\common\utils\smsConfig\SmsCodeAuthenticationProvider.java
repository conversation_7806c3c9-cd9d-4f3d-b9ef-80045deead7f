package com.bocloud.common.utils.smsConfig;

import com.bocloud.common.constant.Constants;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.utils.SpringContextUtil;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.Map;

/**
 * 短信登陆鉴权 Provider，要求实现 AuthenticationProvider 接口
 *
 */

public class SmsCodeAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SmsCodeAuthenticationToken authenticationToken = (SmsCodeAuthenticationToken) authentication;

        String mobile = (String) authenticationToken.getPrincipal();
        String smsCode = authenticationToken.getSmsCode();
        String smsUuid = authenticationToken.getSmsUuid();

        checkSmsCode(mobile, smsCode, smsUuid);

        UserDetails userDetails = userDetailsService.loadUserByUsername(mobile);

        // 此时鉴权成功后，应当重新 new 一个拥有鉴权的 authenticationResult 返回
        SmsCodeAuthenticationToken authenticationResult = new SmsCodeAuthenticationToken(userDetails, userDetails.getAuthorities());

        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }

    private void checkSmsCode(String mobile, String inputCode, String uuid) {
        RedisCache redisCache = (RedisCache) SpringContextUtil.getBean("redisCache");

        String verifyKey = Constants.SMS_CAPTCHA_CODE_KEY + mobile + ":" + uuid;

        Map<String, Object> smsCode = redisCache.getCacheObject(verifyKey);

        if(smsCode == null) {
            throw new BadCredentialsException("验证码失效");
        }

        String applyMobile = (String) smsCode.get("mobile");
        String code = String.valueOf(smsCode.get("code"));

        if(!applyMobile.equals(mobile)) {
            throw new BadCredentialsException("手机号码不一致");
        }
        if(!code.equals(inputCode)) {
            throw new BadCredentialsException("验证码错误");
        }
        //成功后清空验证码
        redisCache.deleteObject(verifyKey);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        // 判断 authentication 是不是 SmsCodeAuthenticationToken 的子类或子接口
        return SmsCodeAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}
