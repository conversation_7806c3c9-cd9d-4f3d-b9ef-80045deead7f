package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.AchievementsMapper;
import com.bocloud.admin.business.service.IAchievementsService;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.achievement.Achievements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description 成果发布业务处理层
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class AchievementsServiceImpl implements IAchievementsService {
    @Autowired
    private AchievementsMapper achievementsMapper;
    @Autowired
    private ICommonAttachmentService commonAttachmentServiceImpl;

    /**
     * 查询成果发布
     * @param achievementId 成果发布主键
     * @return
     */
    @Override
    public Achievements selectAchievementsByAchievementId(Long achievementId) {
        Achievements achievements = achievementsMapper.selectAchievementsByAchievementId(achievementId);
        if (achievements != null) {
            // 查询附件列表
            List<CommonAttachment> attachments = commonAttachmentServiceImpl.selectCommonAttachmentByBusiness(achievementId, "achievements");
            achievements.setAttachments(attachments);
        }
        return achievements;
    }

    /**
     * 查询成果发布列表
     * @param achievements 成果发布
     * @return
     */
    @Override
    public List<Achievements> selectAchievementsList(Achievements achievements) {
        List<Achievements> achievementsList = achievementsMapper.selectAchievementsList(achievements);
        // 查询每个文章的附件列表
        for (Achievements post : achievementsList) {
            List<CommonAttachment> attachments = commonAttachmentServiceImpl.selectCommonAttachmentByBusiness(achievements.getAchievementId(), "achievements");
            post.setAttachments(attachments);
        }
        return achievementsList;
    }

    /**
     * 新增成果发布
     * @param achievements 成果发布
     * @return
     */
    @Override
    @Transactional
    public int insertAchievements(Achievements achievements) {
        // 插入文章信息
        int rows = achievementsMapper.insertAchievements(achievements);

        // 处理附件
        if (rows > 0 && achievements.getAttachments() != null && !achievements.getAttachments().isEmpty()) {
            for (CommonAttachment attachment : achievements.getAttachments()) {
                attachment.setBusinessId(achievements.getAchievementId());
                attachment.setBusinessType("achievements");
                String fileName = attachment.getOriginalFilename();
                if (fileName!=null && !"".equals(fileName)){
                    int lastDotIndex = fileName.lastIndexOf('.');
                    if (lastDotIndex == -1 || lastDotIndex == 0) {
                        // 无扩展名 或 隐藏文件（如 ".config"）
                        fileName = attachment.getOriginalFilename();
                    } else {
                        fileName = fileName.substring(0, lastDotIndex);
                    }
                }
                attachment.setOriginalFilename(fileName);
                commonAttachmentServiceImpl.insertCommonAttachment(attachment);
            }
        }

        return rows;
    }

    /**
     * 修改成果发布
     * @param achievements 成果发布
     * @return
     */
    @Override
    @Transactional
    public int updateAchievements(Achievements achievements) {
        // 更新文章信息
        int rows = achievementsMapper.updateAchievements(achievements);

        // 处理附件
        if (rows > 0 && achievements.getAttachments() != null) {
            // 先删除原有附件
            commonAttachmentServiceImpl.deleteAttachmentByBusiness(achievements.getAchievementId(), "achievements");

            // 插入新附件
            if (!achievements.getAttachments().isEmpty()) {
                for (CommonAttachment attachment : achievements.getAttachments()) {
                    attachment.setBusinessId(achievements.getAchievementId());
                    attachment.setBusinessType("achievements");
                    String fileName = attachment.getOriginalFilename();
                    if (fileName!=null && !"".equals(fileName)){
                        int lastDotIndex = fileName.lastIndexOf('.');
                        if (lastDotIndex == -1 || lastDotIndex == 0) {
                            // 无扩展名 或 隐藏文件（如 ".config"）
                            fileName = attachment.getOriginalFilename();
                        } else {
                            fileName = fileName.substring(0, lastDotIndex);
                        }
                    }
                    attachment.setOriginalFilename(fileName);
                    commonAttachmentServiceImpl.insertCommonAttachment(attachment);
                }
            }
        }

        return rows;
    }

    /**
     * 删除成果发布
     * @param achievementIds 需要删除的成果发布主键集合
     * @return
     */
    @Override
    @Transactional
    public int deleteAchievementsByAchievementIds(Long[] achievementIds) {
        // 删除文章信息
        int rows = achievementsMapper.deleteAchievementsByAchievementIds(achievementIds);

        // 删除相关附件
        if (rows > 0) {
            for (Long achievementId : achievementIds) {
                commonAttachmentServiceImpl.deleteAttachmentByBusiness(achievementId, "achievements");
            }
        }

        return rows;
    }


    /**
     * 删除成果发布成果
     * @param achievementId 成果发布主键
     * @return
     */
    @Override
    public int deleteAchievementsByAchievementId(Long achievementId) {
        // 删除文章信息
        int rows = achievementsMapper.deleteAchievementsByAchievementId(achievementId);

        // 删除相关附件
        if (rows > 0) {
            commonAttachmentServiceImpl.deleteAttachmentByBusiness(achievementId, "achievements");
        }
        return rows;
    }
}
