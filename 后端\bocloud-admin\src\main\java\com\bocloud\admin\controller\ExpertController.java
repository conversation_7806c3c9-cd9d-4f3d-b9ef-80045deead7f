package com.bocloud.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bocloud.admin.business.service.IDemandSolutionInviteService;
import com.bocloud.admin.business.service.IExpertService;
import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.dto.SingleInviteRequest;
import com.bocloud.domain.web.BatchInviteRequest;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 专家管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/expert")
@Api(tags = "专家管理")
public class ExpertController extends BaseController {

    @Autowired
    private IExpertService expertService;

    @Autowired
    private IDemandSolutionInviteService demandSolutionInviteService;

    @Autowired
    private IMessageNotificationService messageNotificationService;

    /**
     * 获取专家列表
     */
    @ApiOperation("获取专家列表")
    @PreAuthorize("@ss.hasPermi('admin:expert:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExpertInfo expertInfo) {
        startPage();
        List<ExpertInfo> list = expertService.list(expertInfo);
        return getDataTable(list);
    }
    
    /**
     * 获取专家详情
     */
    @ApiOperation("获取专家详情")
    @PreAuthorize("@ss.hasPermi('admin:expert:detail')")
    @GetMapping("/detail/{expertId}")
    public AjaxResult detail(@PathVariable Long expertId) {
        ExpertInfo expertInfo = expertService.getExpertDetailByExpertId(expertId);
        return AjaxResult.success(expertInfo);
    }

    /**
     * 审核专家
     */
    @ApiOperation("审核专家")
    @PreAuthorize("@ss.hasPermi('admin:expert:audit')")
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody ExpertInfo expertInfo) { 
        expertService.auditExpert(expertInfo);
        return AjaxResult.success();
    }

    /**
     * 获取邀请列表
     */
    @ApiOperation("获取邀请列表")
    @PreAuthorize("@ss.hasPermi('admin:expert:inviteList')")
    @GetMapping("/inviteList")
    public TableDataInfo inviteList(DemandSolutionInvite invite) {
        startPage();
        List<DemandSolutionInvite> list = demandSolutionInviteService.selectDemandSolutionInviteList(invite);
        return getDataTable(list);
    }

    /**
     * 获取邀请详情
     */
    @ApiOperation("获取邀请详情")
    @PreAuthorize("@ss.hasPermi('admin:expert:inviteDetail')")
    @GetMapping("/inviteDetail")
    public AjaxResult inviteDetail(Long inviteId) {
        if (inviteId == null) {
            return AjaxResult.error("邀请ID不能为空");
        }
        DemandSolutionInvite invite = demandSolutionInviteService.selectDemandSolutionInviteById(inviteId);
        if (invite == null) {
            return AjaxResult.error("邀请记录不存在");
        }
        return AjaxResult.success(invite);
    }

    /**
     * 邀请单个专家
     * 
     * @param request 单个邀请请求
     * @return 操作结果
     */
    @ApiOperation("邀请单个专家")
    @PreAuthorize("@ss.hasPermi('admin:expert:invite')")
    @PostMapping("/invite")
    public AjaxResult inviteExpert(@Validated @RequestBody SingleInviteRequest request) {
        try {
            log.info("[inviteExpert] 开始邀请专家，需求ID: {}, 专家用户ID: {}", 
                    request.getDemandId(), request.getInvitedUserId());
            
            // 调用Service层处理业务逻辑
            DemandSolutionInvite invite = demandSolutionInviteService.inviteSingleExpert(
                    request, getUserId(), getLoginUser().getUsername());
            
            log.info("[inviteExpert] 专家邀请成功，邀请ID: {}", invite.getId());
            return AjaxResult.success("邀请发送成功");
            
        } catch (ServiceException e) {
            log.error("[inviteExpert] 邀请专家业务异常: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("[inviteExpert] 邀请专家系统异常", e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 批量邀请
     */
    @ApiOperation("批量邀请")
    @PreAuthorize("@ss.hasPermi('admin:expert:batchInvite')")
    @PostMapping("/batchInvite")
    public AjaxResult batchInvite(@RequestBody BatchInviteRequest request) {
        try {
            if (request == null || request.getUserIds() == null || request.getUserIds().isEmpty() || request.getDemandId() == null) {
                return AjaxResult.error("参数不完整");
            }
            int successCount = demandSolutionInviteService.batchInviteExperts(request, getUserId(), getLoginUser().getUsername());
            return AjaxResult.success("批量邀请成功，成功邀请人数：" + successCount);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            return AjaxResult.error("批量邀请失败: " + e.getMessage());
        }
    }

}
