package com.bocloud.domain.system.post;

import java.util.List;
import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.system.CommonAttachment;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 信息发布对象 posts
 * 
 * <AUTHOR>
 */
public class Posts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 状态常量 */
    public static final String STATUS_DRAFT = "1";      // 草稿
    public static final String STATUS_PUBLISH = "2";    // 发布
    public static final String STATUS_CANCEL = "3";     // 取消
    public static final String STATUS_WAIT_AUDIT = "4"; // 待审核

    /** 置顶类型常量 */
    public static final String TOP_TYPE_NONE = "0";     // 不置顶
    public static final String TOP_TYPE_GLOBAL = "1";   // 全局置顶
    public static final String TOP_TYPE_CATEGORY = "2"; // 分类置顶

    /** 信息ID */
    private Long postId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 分类ID */
    @Excel(name = "分类ID")
    private String categoryId;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 关键字，多个用英文逗号分隔 */
    @Excel(name = "关键字")
    private String keywords;

    /** 主图 */
    @Excel(name = "主图")
    private String mainImage;

    /** 概述 */
    private String overview;

    /** 新闻来源 */
    private String source;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer views;

    /** 是否热门 */
    @Excel(name = "是否热门")
    private String hot;

    /** 置顶类型：0-不置顶，1-全局置顶，2-分类置顶 */
    @Excel(name = "置顶类型")
    private String topType;

    /** 置顶排序号（数字越大越靠前） */
    @Excel(name = "置顶排序号")
    private Integer topOrder;

    /** 删除标志（0代表存在 1代表删除） */
    @Excel(name = "删除标志")
    private String delFlag;

    /** 附件列表 */
    private List<CommonAttachment> attachments;

    public void setPostId(Long postId)
    {
        this.postId = postId;
    }

    public Long getPostId()
    {
        return postId;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setCategoryId(String categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryId()
    {
        return categoryId;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setKeywords(String keywords)
    {
        this.keywords = keywords;
    }

    public String getKeywords()
    {
        return keywords;
    }

    public void setMainImage(String mainImage)
    {
        this.mainImage = mainImage;
    }

    public String getMainImage()
    {
        return mainImage;
    }

    public String getOverview() {
        return overview;
    }

    public void setOverview(String overview) {
        this.overview = overview;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setViews(Integer views)
    {
        this.views = views;
    }

    public Integer getViews()
    {
        return views;
    }

    public List<CommonAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<CommonAttachment> attachments) {
        this.attachments = attachments;
    }

    public String getHot() {
        return hot;
    }

    public void setHot(String hot) {
        this.hot = hot;
    }

    public String getTopType() {
        return topType;
    }

    public void setTopType(String topType) {
        this.topType = topType;
    }

    public Integer getTopOrder() {
        return topOrder;
    }

    public void setTopOrder(Integer topOrder) {
        this.topOrder = topOrder;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("postId", getPostId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("categoryId", getCategoryId())
                .append("status", getStatus())
                .append("keywords", getKeywords())
                .append("mainImage", getMainImage())
                .append("views", getViews())
                .append("hot", getHot())
                .append("topType", getTopType())
                .append("topOrder", getTopOrder())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("attachments", getAttachments())
                .toString();
    }
}