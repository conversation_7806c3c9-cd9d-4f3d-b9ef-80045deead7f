package com.bocloud.domain.web.enums;

/**
 * 专家状态枚举
 */
public enum ExpertStatusEnum {
    
    /** 待审核 */
    PENDING("0", "待审核"),
    
    /** 正常 */
    NORMAL("1", "正常"),
    
    /** 未通过 */
    REJECTED("-1", "未通过");
    
    private final String code;
    private final String desc;
    
    ExpertStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 状态代码
     * @return 枚举值
     */
    public static ExpertStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ExpertStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为正常状态
     * 
     * @param code 状态代码
     * @return 是否为正常状态
     */
    public static boolean isNormal(String code) {
        return NORMAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为待审核状态
     * 
     * @param code 状态代码
     * @return 是否为待审核状态
     */
    public static boolean isPending(String code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为未通过状态
     * 
     * @param code 状态代码
     * @return 是否为未通过状态
     */
    public static boolean isRejected(String code) {
        return REJECTED.getCode().equals(code);
    }
    
    /**
     * 获取状态描述
     * 
     * @param code 状态代码
     * @return 状态描述
     */
    public static String getDesc(String code) {
        ExpertStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
} 