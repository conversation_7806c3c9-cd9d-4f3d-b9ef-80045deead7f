package com.bocloud.domain.web;

import com.bocloud.common.annotation.Excel;
import lombok.Data;

@Data
public class BusinessLicense {
    /** 营业执照ID */
    private Long licenseId;

    /** 用户ID */
    private Long userId;

    /** 执照类型 */
    @Excel(name = "执照类型")
    private String licenseType;

    /** 执照编号 */
    @Excel(name = "执照编号")
    private String licenseNumber;

    /** 发证日期 */
    @Excel(name = "发证日期")
    private String issueDate;

    /** 有效期 */
    @Excel(name = "有效期")
    private String expiryDate;

    /** 发证机关 */
    @Excel(name = "发证机关")
    private String issuingAuthority;

    /** 附件URL */
    @Excel(name = "附件URL")
    private String attachmentUrl;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private String createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    private String updateTime;
} 