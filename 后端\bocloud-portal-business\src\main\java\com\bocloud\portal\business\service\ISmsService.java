package com.bocloud.portal.business.service;

public interface ISmsService {
    /**
     * 发送短信验证码
     * @param phone 手机号
     * @param code  验证码
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone, String code);

    /**
     * 校验短信验证码
     * @param mobile 手机号
     * @param inputCode 用户输入的验证码
     * @param uuid 验证码唯一标识
     * @return 校验结果
     */
    boolean checkSmsCode(String mobile, String inputCode, String uuid);
} 