package com.bocloud.admin.business.service;

import com.bocloud.domain.system.Order;
import java.util.List;

public interface IOrderService {
    /** 新增订单 */
    int insertOrder(Order order);

    /** 根据主键查询订单 */
    Order selectOrderById(Long orderId);

    /** 查询订单列表 */
    List<Order> selectOrderList(Order order);

    /** 更新订单 */
    int updateOrder(Order order);

    /** 逻辑删除订单 */
    int deleteOrderById(Long orderId);

    /** 批量逻辑删除订单 */
    int deleteOrderByIds(Long[] orderIds);
} 