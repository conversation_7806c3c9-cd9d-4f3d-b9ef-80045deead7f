package com.bocloud.portal.controller.api;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.system.online_education.Courses;
import com.bocloud.portal.business.service.ICoursesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/course")
@Api(tags = "在线课程接口")
public class ApiCoursesController extends BaseController {

    @Autowired
    private ICoursesService coursesService;

    /**
     * 获取推荐课程列表
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "获取推荐课程列表", notes = "获取被标记为推荐的课程列表")
    public AjaxResult getRecommendCourses() {
        try {
            Courses query = new Courses();
            query.setRecommend("1"); // 设置推荐标志
            query.setStatus("1");    // 只查询已批准的课程
            
            List<Courses> recommendCourses = coursesService.selectCoursesList(query);
            return AjaxResult.success(recommendCourses);
        } catch (Exception e) {
            log.error("获取推荐课程列表失败", e);
            return AjaxResult.error("获取推荐课程列表失败");
        }
    }

    /**
     * 获取热门讲师列表
     */
    @GetMapping("/hot-teachers")
    @ApiOperation(value = "获取热门讲师列表", notes = "获取平台热门讲师列表")
    public AjaxResult getHotTeachers() {
        return AjaxResult.success();
    }
}
