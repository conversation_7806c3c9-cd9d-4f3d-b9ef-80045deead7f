package com.bocloud.admin.business.mapper;

import com.bocloud.domain.web.PortalMessageStatus;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface PortalMessageStatusMapper {
    /**
     * 批量新增消息状态
     */
    int batchInsertPortalMessageStatus(List<PortalMessageStatus> statusList);

    /**
     * 查询用户的消息状态
     */
    String selectMessageStatus(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 更新消息状态
     */
    int updateMessageStatus(@Param("messageId") Long messageId, @Param("userId") Long userId, @Param("status") String status);

    /**
     * 插入或更新消息状态
     */
    int insertOrUpdateMessageStatus(@Param("messageId") Long messageId, @Param("userId") Long userId, @Param("status") String status);
} 