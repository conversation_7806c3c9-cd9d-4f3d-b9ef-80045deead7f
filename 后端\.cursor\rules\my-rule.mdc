---
description: 
globs: 
alwaysApply: false
---
# <PERSON><PERSON><PERSON> AI Assistant Guidelines for bocloud project

## Module Structure (Strict Adherence Required)

Refer to README.md for the definitive module structure and responsibilities. Key points:

*   **bocloud-portal:** Controllers ONLY for the front-end portal API. No Service, Mapper, or XML files.
*   **bocloud-portal-business:** Service, Mapper, Mapper XML for the portal.
*   **bocloud-admin:** Controllers ONLY for the admin backend API. No Service, Mapper, or XML files.
*   **bocloiud-admin-business:** Service, Mapper, Mapper XML for the admin backend.
*   **bocloud-domain:** Entity classes ONLY.
*   **bocloud-common:** Common utilities and configurations.
*   **bocloud-framework:** Framework configurations.
*   **bocloud-generator:** Code generation logic.
*   **bocloud-quartz:** Scheduled task logic.

## File Creation/Modification Rules

*   **DO NOT** create Service, Mapper, or Mapper XML files in `bocloud-portal` or `bocloud-admin`.
*   Place new Service, Mapper, Mapper XML files in the corresponding `-business` module (`bocloud-portal-business` or `bocloiud-admin-business`).
*   Place new Controllers in `bocloud-portal` or `bocloud-admin`.
*   Place new Entities in `bocloud-domain`.
*   Always verify the target module against `README.md` before creating or modifying files.
*   If unsure, re-read `README.md` or ask for clarification.
*   **STRICTLY** follow the folder structure when creating new files. Each type of file must be placed in its designated folder:
    *   Controllers: `com/bocloud/portal/controller` or `com/bocloud/admin/controller`
    *   Services: `com/bocloud/portal/business/service` or `com/bocloud/admin/business/service`
    *   Service Impls: `com/bocloud/portal/business/service/impl` or `com/bocloud/admin/business/service/impl`
    *   Mappers: `com/bocloud/portal/business/mapper` or `com/bocloud/admin/business/mapper`
    *   Mapper XMLs: `resources/mapper/portal/business` or `resources/mapper/admin/business`
    *   Entities: `com/bocloud/domain/system/[module]`

## 代码注释
*   所有代码注释必须使用中文。

## Communication

*   Always respond in 中文. 

## SQL/Mapper 规范

- 所有 insert/update 语句的 create_time、update_time（或 upload_time 等）字段，必须无条件写在动态字段的最后，值始终为 sysdate()，不得用 if 判断。
- 禁止在这些时间字段上使用 <if test="..."> 判断。

## delFlag 字段规范

### Select 查询规范
- 所有 select 查询方法的 delFlag 参数都不需要使用动态参数 `<if test="delFlag != null and delFlag != ''">`
- 直接使用固定值 `del_flag = '0'`，默认只查询未删除的记录
- 示例：
```xml
<!-- ✅ 正确 -->
<select id="selectList" resultType="Entity">
    SELECT * FROM table_name WHERE del_flag = '0'
    <if test="otherField != null">
        AND other_field = #{otherField}
    </if>
</select>

<!-- ❌ 错误 -->
<select id="selectList" resultType="Entity">
    SELECT * FROM table_name 
    <where>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}
        </if>
        <if test="otherField != null">
            AND other_field = #{otherField}
        </if>
    </where>
</select>
```

### Insert 插入规范
- 所有 insert 语句的 delFlag 字段，必须判断不为空就插入固定值 `'0'`，而不是使用参数值
- 防止 Java 代码忘记传值导致为空
- 示例：
```xml
<!-- ✅ 正确 -->
<insert id="insert" parameterType="Entity">
    INSERT INTO table_name (
        field1, field2, del_flag, create_time
    ) VALUES (
        #{field1}, #{field2}, 
        <if test="delFlag != null and delFlag != ''">#{delFlag}</if>
        <if test="delFlag == null or delFlag == ''">'0'</if>,
        sysdate()
    )
</insert>

<!-- 或者更简洁的写法 -->
<insert id="insert" parameterType="Entity">
    INSERT INTO table_name (
        field1, field2, del_flag, create_time
    ) VALUES (
        #{field1}, #{field2}, 
        COALESCE(#{delFlag}, '0'),
        sysdate()
    )
</insert>
```

### 原因说明
1. **查询规范**：业务上默认只查询未删除的记录，使用固定值 `'0'` 更安全
2. **插入规范**：防止 Java 代码忘记设置 delFlag 导致插入空值，影响后续查询
3. **一致性**：确保所有记录的 delFlag 字段都有正确的默认值

- 其它已有规则保持不变。


