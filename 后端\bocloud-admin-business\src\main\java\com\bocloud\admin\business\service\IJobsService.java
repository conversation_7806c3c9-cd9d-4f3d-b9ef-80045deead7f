package com.bocloud.admin.business.service;

import  com.bocloud.domain.system.platform_service.Jobs;

import java.util.List;

/**
 * 职位发布Service接口
 *
 * @date 2025-03-11
 */
public interface IJobsService
{
    /**
     * 查询职位发布
     *
     * @param jobId 职位发布主键
     * @return 职位发布
     */
    public Jobs selectJobsByJobId(Long jobId);

    /**
     * 查询职位发布列表
     *
     * @param jobs 职位发布
     * @return 职位发布集合
     */
    public List<Jobs> selectJobsList(Jobs jobs);

    /**
     * 新增职位发布
     *
     * @param jobs 职位发布
     * @return 结果
     */
    public int insertJobs(Jobs jobs);

    /**
     * 修改职位发布
     *
     * @param jobs 职位发布
     * @return 结果
     */
    public int updateJobs(Jobs jobs);

    /**
     * 批量删除职位发布
     *
     * @param jobIds 需要删除的职位发布主键集合
     * @return 结果
     */
    public int deleteJobsByJobIds(Long[] jobIds);

    /**
     * 删除职位发布信息
     *
     * @param jobId 职位发布主键
     * @return 结果
     */
    public int deleteJobsByJobId(Long jobId);
}