package com.bocloud.portal.business.service.impl;

import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.CommonCollection;
import com.bocloud.domain.system.achievement.Achievements;
import com.bocloud.portal.business.mapper.AchievementsMapper;
import com.bocloud.portal.business.mapper.CommonAttachmentMapper;
import com.bocloud.portal.business.mapper.CommonCollectionMapper;
import com.bocloud.portal.business.service.IAchievementsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DuplicateKeyException;


import java.util.List;
import java.util.Map;

@Service
public class AchievementsServiceImpl implements IAchievementsService {

    @Autowired
    private AchievementsMapper achievementsMapper;

    @Autowired
    private CommonCollectionMapper commonCollectionMapper;

    @Autowired
    private CommonAttachmentMapper commonAttachmentMapper;

    @Override
    public Achievements selectAchievementsByAchievementId(Long achievementId,Long userId) {
        Achievements achievement = achievementsMapper.selectAchievementsByAchievementId(achievementId);
        if (achievement == null) {
            return null;
        }
        if (achievement != null) {
            // 查询附件列表
            List<CommonAttachment> attachments = commonAttachmentMapper.selectCommonAttachmentByBusiness(achievementId, "achievements");
            achievement.setAttachments(attachments);
        }
        if (userId != null) {
            CommonCollection commonCollection = new CommonCollection();
            commonCollection.setBusinessId(achievementId);
            commonCollection.setBusinessType("achievements");
            commonCollection.setCollectBy(userId);
            List<CommonCollection> list = commonCollectionMapper.selectCollectionList(commonCollection);
            if (list != null && list.size() > 0) {
                achievement.setCollectFlag("1");//已收藏
            } else {
                achievement.setCollectFlag("0");// 为收藏
            }
            achievement.setCollections(commonCollectionMapper.countByBusiness(achievementId, "achievements"));
        }
        return achievement;

    }

    /**
     * 查询成果列表
     * @param achievements 成果发布
     * @return
     */
    @Override
    public List<Achievements> selectAchievementsList(Achievements achievements) {
        List<Achievements> list = achievementsMapper.selectAchievementsList(achievements);
        for (Achievements ach : list) {
            if (ach.getCollectFlag() == null) {
                ach.setCollectFlag("0");
            }
        }
        return list;
    }

    /**
     * 更新浏览次数
     * @param achievementId 成果ID
     * @return
     */
    @Override
    public int updateAchievementViews(Long achievementId) {
        return achievementsMapper.updateAchievementViews(achievementId);
    }

    /**
     *
     * @param achievementId 成果ID
     * @param userId 用户ID
     * @param collectFlag 1-收藏/0-取消收藏
     * @return
     */
    @Override
    @Transactional
    public Map<String, Object> updateAchievementCollections(Long achievementId, Long userId, String collectFlag) {
        if ("1".equals(collectFlag)) {
            // 收藏，唯一索引保证不会重复
            try {
                CommonCollection commonCollection = new CommonCollection();
                commonCollection.setBusinessId(achievementId);
                commonCollection.setBusinessType("achievements");
                commonCollection.setCollectBy(userId);
                commonCollectionMapper.insertCommonCollection(commonCollection);
            } catch (DuplicateKeyException ignore) {
                // 已收藏，无需处理
            }
        } else {
            // 取消收藏
            CommonCollection commonCollection = new CommonCollection();
            commonCollection.setBusinessId(achievementId);
            commonCollection.setBusinessType("achievements");
            commonCollection.setCollectBy(userId);
            commonCollectionMapper.deleteCommonCollectionByBussiness(commonCollection);
        }
        // 实时查询收藏数
        int collections = commonCollectionMapper.countByBusiness(achievementId, "achievements");
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("collectFlag", collectFlag);
        result.put("collections", collections);
        return result;
    }

}
