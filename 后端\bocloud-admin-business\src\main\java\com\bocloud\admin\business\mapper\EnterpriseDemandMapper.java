package com.bocloud.admin.business.mapper;

import com.bocloud.domain.system.demand.EnterpriseDemand;
import java.util.List;

/**
 * 企业需求Mapper接口
 * 对应企业需求表的数据库操作
 */
public interface EnterpriseDemandMapper {
    /**
     * 根据ID查询企业需求详情
     * @param demandId 需求ID
     * @return 企业需求详情
     */
    EnterpriseDemand selectEnterpriseDemandById(Long demandId);

    /**
     * 查询企业需求列表
     * @param enterpriseDemand 查询条件
     * @return 企业需求列表
     */
    List<EnterpriseDemand> selectEnterpriseDemandList(EnterpriseDemand enterpriseDemand);

    /**
     * 根据ID删除企业需求
     * @param demandId 需求ID
     * @return 删除结果
     */
    int deleteEnterpriseDemandById(Long demandId);

    /**
     * 批量删除企业需求
     * @param demandIds 需求ID数组
     * @return 删除结果
     */
    int deleteEnterpriseDemandByIds(Long[] demandIds);

    /**
     * 审核企业需求（同意/拒绝）
     * @param demand 企业需求对象，需包含demandId、status、reviewComment
     * @return 影响行数
     */
    int updateEnterpriseDemandAudit(EnterpriseDemand demand);

    /**
     * 更新需求的状态
     * @param updateDemand
     */
    void updateEnterpriseDemandStatusByDemandId(EnterpriseDemand updateDemand);
} 