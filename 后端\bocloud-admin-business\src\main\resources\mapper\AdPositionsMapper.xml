<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.AdPositionsMapper">

    <resultMap type="AdPositions" id="AdPositionsResult">
        <result property="positionId"    column="position_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="exampleImage"    column="example_image"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAdPositionsVo">
        select position_id, name, description, example_image, create_by, create_time, update_by, update_time from ad_positions
    </sql>

    <select id="selectAdPositionsList" parameterType="AdPositions" resultMap="AdPositionsResult">
        <include refid="selectAdPositionsVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="exampleImage != null  and exampleImage != ''"> and example_image = #{exampleImage}</if>
        </where>
    </select>

    <select id="selectAdPositionsByPositionId" parameterType="Long" resultMap="AdPositionsResult">
        <include refid="selectAdPositionsVo"/>
        where position_id = #{positionId}
    </select>

    <insert id="insertAdPositions" parameterType="AdPositions" useGeneratedKeys="true" keyProperty="positionId">
        insert into ad_positions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="exampleImage != null">example_image,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="exampleImage != null">#{exampleImage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAdPositions" parameterType="AdPositions">
        update ad_positions
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="exampleImage != null">example_image = #{exampleImage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where position_id = #{positionId}
    </update>

    <delete id="deleteAdPositionsByPositionId" parameterType="Long">
        delete from ad_positions where position_id = #{positionId}
    </delete>

    <delete id="deleteAdPositionsByPositionIds" parameterType="String">
        delete from ad_positions where position_id in
        <foreach item="positionId" collection="array" open="(" separator="," close=")">
            #{positionId}
        </foreach>
    </delete>
</mapper>