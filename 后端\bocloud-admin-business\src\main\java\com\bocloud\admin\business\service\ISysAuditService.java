package com.bocloud.admin.business.service;

import com.bocloud.domain.system.SysAudit;
import java.util.List;

/**
 * 系统审核记录Service接口
 */
public interface ISysAuditService {
    /**
     * 查询审核记录
     */
    public SysAudit selectSysAuditById(Long auditId);

    /**
     * 查询审核记录列表
     */
    public List<SysAudit> selectSysAuditList(SysAudit sysAudit);

    /**
     * 新增审核记录
     */
    public int insertSysAudit(SysAudit sysAudit);

    /**
     * 修改审核记录
     */
    public int updateSysAudit(SysAudit sysAudit);

    /**
     * 批量删除审核记录
     */
    public int deleteSysAuditByIds(Long[] auditIds);

    /**
     * 删除审核记录信息
     */
    public int deleteSysAuditById(Long auditId);
} 