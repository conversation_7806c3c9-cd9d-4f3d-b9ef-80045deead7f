package com.bocloud.admin.business.mapper;

import com.bocloud.domain.web.EnterpriseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信息Mapper接口
 */
public interface EnterpriseInfoMapper {
    /**
     * 更新企业信息
     * 
     * @param enterpriseInfo 企业信息
     * @return 结果
     */
    int updateEnterpriseInfo(EnterpriseInfo enterpriseInfo);

    /**
     * 根据用户ID查询企业信息
     * 
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectEnterpriseInfoByUserId(Long userId);

    /**
     * 查询用户当前有效的企业信息
     * 
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectActiveByUserId(Long userId);

    /**
     * 查询用户最新的企业信息（考虑认证状态）
     * 
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectLatestEnterpriseInfoByUserId(Long userId);

    /**
     * 根据用户ID和状态查询最新的企业信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 企业信息
     */
    EnterpriseInfo selectLatestByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据状态查询企业信息列表
     * 
     * @param status 状态
     * @return 企业信息列表
     */
    List<EnterpriseInfo> selectListByStatus(@Param("status") String status);
} 