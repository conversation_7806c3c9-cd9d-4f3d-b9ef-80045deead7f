package com.bocloud.domain.web;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.common.enums.PersonalInfoStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class PersonalInfo extends BaseEntity{
    /** 个人信息ID */
    private Long id;
    /** 用户ID（关联系统用户表） */
    private Long userId;
    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;
    /** 头像 */
    @Excel(name = "头像")
    private String avatar;
    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;
    /** 性别（0=男, 1=女, 2=未知） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String gender;
    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String idCardNumber;
    /** 职业 */
    @Excel(name = "职业")
    private String occupation;
    /** 毕业院校 */
    @Excel(name = "毕业院校")
    private String graduationSchool;
    /** 毕业年份 */
    @Excel(name = "毕业年份")
    private String graduationYear;
    /** 入学年份 */
    @Excel(name = "入学年份")
    private String enrollmentYear;
    /** 专业 */
    @Excel(name = "专业")
    private String major;
    /** 省份 */
    @Excel(name = "省份")
    private String province;
    /** 城市 */
    @Excel(name = "城市")
    private String city;
    /** 区县 */
    @Excel(name = "区县")
    private String district;
    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detailAddress;
    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phone;
    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;
    /** 是否专家（Y=是, N=否） */
    @Excel(name = "是否专家", readConverterExp = "Y=是,N=否")
    private String isExpert;
    /** 身份证人像面图片路径 */
    @Excel(name = "身份证人像面")
    private String idCardPortrait;
    /** 身份证国徽面图片路径 */
    @Excel(name = "身份证国徽面")
    private String idCardEmblem;

    /** 状态，0-待审核， 1-正常，-1-未通过 */
    private String status;

    /** 删除标志 0-未删除，1-已删除 */
    private String delFlag;

    /** 是否当前有效：1-是 0-否 */
    private String active;
    
    // ==================== 状态转换方法 ====================
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return PersonalInfoStatus.getDescByCode(this.status);
    }
    
    /**
     * 获取激活状态描述
     */
    public String getActiveDesc() {
        return PersonalInfoStatus.getDescByCode(this.active);
    }
    
    /**
     * 判断是否为待审核状态
     */
    public boolean isPending() {
        return PersonalInfoStatus.isPending(this.status);
    }
    
    /**
     * 判断是否为正常状态
     */
    public boolean isNormal() {
        return PersonalInfoStatus.isNormal(this.status);
    }
    
    /**
     * 判断是否为拒绝状态
     */
    public boolean isRejected() {
        return PersonalInfoStatus.isRejected(this.status);
    }

    /**
     * 获取显示地址（省市区）
     */
    public String getRegion() {
        return buildAddressString(province, city, district);
    }

    /**
     * 构建地址字符串
     */
    private String buildAddressString(String province, String city, String district) {
        StringBuilder sb = new StringBuilder();
        if (province != null && !province.trim().isEmpty()) {
            sb.append(province);
        }
        if (city != null && !city.trim().isEmpty()) {
            sb.append(city);
        }
        if (district != null && !district.trim().isEmpty()) {
            sb.append(district);
        }
        return sb.toString();
    }
    
}