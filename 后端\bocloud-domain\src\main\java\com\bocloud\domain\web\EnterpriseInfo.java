package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 企业信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseInfo extends BaseEntity {

    /** 企业ID */
    private Long enterpriseId;

    /** 用户ID */
    private Long userId;

    /** 公司名称 */
    private String companyName;

    /** 统一社会信用代码 */
    private String creditCode;

    /** 营业执照图片地址 */
    private String creditImageUrl;

    /** 企业类型：state-国有企业，private-民营企业，foreign-外资企业，joint-合资企业 */
    private String companyType;

    /** 省份 */
    private String province;

    /** 城市 */
    private String city;

    /** 区县 */
    private String district;

    /** 详细地址 */
    private String detailAddress;

    /** 注册资本 */
    private String registeredCapital;

    /** 成立日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishDate;

    /** 企业规模：small-小型企业，medium-中型企业，large-大型企业 */
    private String companySize;

    /** 所属行业 */
    private String industry;

    /** 公司网站 */
    private String website;

    /** 法人姓名 */
    private String legalPersonName;

    /** 法人证件类型：1-身份证，2-护照，3-其他 */
    private String legalPersonIdType;

    /** 法人证件图片地址 */
    private String legalPersonImageUrl;

    /** 法人证件号码 */
    private String legalPersonIdNumber;

    /** 法人身份证人像面图片路径 */
    private String legalPersonIdCardPortrait;

    /** 法人身份证国徽面图片路径 */
    private String legalPersonIdCardEmblem;

    /** 法人电话 */
    private String legalPersonPhone;

    /** 联系人姓名 */
    private String contactName;

    /** 联系人职位 */
    private String contactPosition;

    /** 联系人电话 */
    private String contactPhone;

    /** 联系人邮箱 */
    private String contactEmail;

    /** 营业执照照片路径 */
    private String businessLicense;

    /** 状态，0-待审核， 1-正常，2-未通过 */
    private String status;

    /** 删除标志 */
    private String delFlag;

    /** 是否当前有效：1-是 0-否 */
    private String active;

    /** 经营范围 */
    private String businessScope;

    /** 企业简介 */
    private String introduction;
}


