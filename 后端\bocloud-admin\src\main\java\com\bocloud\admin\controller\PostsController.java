package com.bocloud.admin.controller;

import com.bocloud.admin.business.service.IPostsService;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import com.bocloud.domain.system.post.Posts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 信息发布Controller
 *
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/posts")
public class PostsController extends BaseController
{
    @Autowired
    private IPostsService postsService;

    /**
     * 查询信息发布列表
     */
    //@PreAuthorize("@ss.hasPermi('system:posts:list')")
    @GetMapping("/list")
    public TableDataInfo list(Posts posts)
    {
        startPage();
        List<Posts> list = postsService.selectPostsList(posts);
        return getDataTable(list);
    }

    /**
     * 导出信息发布列表
     */
    @PreAuthorize("@ss.hasPermi('system:posts:export')")
    @Log(title = "信息发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Posts posts)
    {
        List<Posts> list = postsService.selectPostsList(posts);
        ExcelUtil<Posts> util = new ExcelUtil<Posts>(Posts.class);
        util.exportExcel(response, list, "信息发布数据");
    }

    /**
     * 获取信息发布详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:posts:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Long postId)
    {
        return success(postsService.selectPostsByPostId(postId));
    }

    /**
     * 新增信息发布
     */
    @PreAuthorize("@ss.hasPermi('system:posts:add')")
    @Log(title = "信息发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Posts posts)
    {
        posts.setCreateBy(getUsername());
        return toAjax(postsService.insertPosts(posts));
    }

    /**
     * 修改信息发布
     */
    @PreAuthorize("@ss.hasPermi('system:posts:edit')")
    @Log(title = "信息发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Posts posts)
    {
        posts.setUpdateBy(getUsername());
        return toAjax(postsService.updatePosts(posts));
    }

    /**
     * 删除信息发布
     */
    @PreAuthorize("@ss.hasPermi('system:posts:remove')")
    @Log(title = "信息发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        return toAjax(postsService.deletePostsByPostIds(postIds));
    }

    /**
     * 审核信息发布
     */
    //@PreAuthorize("@ss.hasPermi('system:posts:audit')")
    @Log(title = "审核信息发布", businessType = BusinessType.UPDATE)
    @PostMapping("/audit/{postId}")
    public AjaxResult audit(@PathVariable Long postId)
    {
        Posts posts = postsService.selectPostsByPostId(postId);
        posts.setUpdateBy(getUsername());
        posts.setStatus(Posts.STATUS_PUBLISH);
        return toAjax(postsService.updatePosts(posts));
    }
}