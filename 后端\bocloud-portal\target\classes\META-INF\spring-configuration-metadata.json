{"groups": [{"name": "spring.datasource.druid.master", "type": "javax.sql.DataSource", "sourceType": "com.bocloud.framework.config.DruidConfig", "sourceMethod": "public javax.sql.DataSource masterDataSource(com.bocloud.framework.config.properties.DruidProperties) "}, {"name": "spring.datasource.druid.slave", "type": "javax.sql.DataSource", "sourceType": "com.bocloud.framework.config.DruidConfig", "sourceMethod": "public javax.sql.DataSource slaveDataSource(com.bocloud.framework.config.properties.DruidProperties) "}], "properties": [], "hints": []}