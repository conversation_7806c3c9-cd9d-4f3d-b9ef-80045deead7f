package com.bocloud.domain.web.enums;

/**
 * 消息类型枚举
 */
public enum PortalMessageType {
    SYSTEM("1", "系统通知"),
    ACTIVITY("2", "活动通知"),
    AUDIT("3", "审核通知"),
    TASK("4", "任务通知"),
    INVITE("5", "邀请通知"),
    ORDER("6", "订单通知"),
    SECURITY("7", "安全提醒"),
    OTHER("8", "其他");

    private final String code;
    private final String description;

    PortalMessageType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static PortalMessageType fromCode(String code) {
        for (PortalMessageType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return OTHER;
    }
} 