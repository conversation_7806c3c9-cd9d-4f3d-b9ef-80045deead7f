package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.admin.business.service.IPortalMessageService;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.domain.web.PortalMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 消息通知服务实现类
 * 用于后台管理员发送需求相关的消息通知
 */
@Service
public class MessageNotificationServiceImpl implements IMessageNotificationService {
    
    @Autowired
    private IPortalMessageService portalMessageService;
    
    // 消息类型常量
    private static final String MESSAGE_TYPE_SYSTEM = "1";    // 系统通知
    private static final String MESSAGE_TYPE_AUDIT = "3";     // 审核通知
    
    @Override
    public void sendDemandAuditNotification(Long demandId, String demandTitle, Long receiverId, boolean approved, String reviewComment) {
        PortalMessage message = new PortalMessage();
        message.setTitle(approved ? "需求审核通过" : "需求审核未通过");
        
        String content;
        if (approved) {
            content = "恭喜！您的需求\"" + demandTitle + "\"已通过审核，请及时公开，公开后可以开始接收方案投标。";
        } else {
            content = "很抱歉，您的需求\"" + demandTitle + "\"审核未通过。\n审核意见：" + 
                (reviewComment != null ? reviewComment : "无") + "\n请根据审核意见修改后重新提交。";
        }
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_AUDIT);
        message.setReceiverId(receiverId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendDemandStatusChangeNotification(Long demandId, String demandTitle, Long receiverId, String oldStatus, String newStatus) {
        PortalMessage message = new PortalMessage();
        message.setTitle("需求状态变更通知");
        
        String statusDesc = EnterpriseDemandStatus.getDescByCode(newStatus);
        String content = "您的需求\"" + demandTitle + "\"状态已变更为：" + statusDesc;
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_SYSTEM);
        message.setReceiverId(receiverId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendSolutionAuditNotification(Long solutionId, String solutionTitle, Long userId, boolean approved, String reviewComment) {
        PortalMessage message = new PortalMessage();
        message.setTitle(approved ? "方案审核通过" : "方案审核未通过");
        
        String content;
        if (approved) {
            content = "恭喜！您的方案\"" + solutionTitle + "\"已通过审核，企业可以查看和选择您的方案。";
        } else {
            content = "很抱歉，您的方案\"" + solutionTitle + "\"审核未通过。\n审核意见：" + 
                (reviewComment != null ? reviewComment : "无") + "\n请根据审核意见修改后重新提交。";
        }
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_AUDIT);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendSolutionBidWinNotification(Long solutionId, String solutionTitle, Long demandId, String demandTitle, Long userId) {
        PortalMessage message = new PortalMessage();
        message.setTitle("方案中标通知");
        
        String content = "恭喜！您的方案\"" + solutionTitle + "\"已中标需求\"" + demandTitle + "\"。\n请及时与企业联系，上传合同文件。";
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_SYSTEM);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendContractAuditNotification(Long contractId, String demandTitle, Long userId, boolean approved, String reviewComment) {
        PortalMessage message = new PortalMessage();
        message.setTitle(approved ? "合同审核通过" : "合同审核未通过");
        
        String content;
        if (approved) {
            content = "您的需求\"" + demandTitle + "\"合同已通过审核，项目正式开始。\n请及时支付平台费用。";
        } else {
            content = "您的需求\"" + demandTitle + "\"合同审核未通过。\n审核意见：" + 
                (reviewComment != null ? reviewComment : "无") + "\n请根据审核意见修改后重新上传合同。";
        }
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_AUDIT);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendDemandCompletedNotification(Long demandId, String demandTitle, Long userId) {
        PortalMessage message = new PortalMessage();
        message.setTitle("需求完成通知");
        
        String content = "恭喜！您的需求\"" + demandTitle + "\"已完成。\n感谢您使用我们的服务，如有新的技术需求，欢迎继续发布。";
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_SYSTEM);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }
    
    @Override
    public void sendPlatformChargeNotification(Long chargeId, String demandTitle, Long userId, String amount) {
        PortalMessage message = new PortalMessage();
        message.setTitle("平台收费通知");
        
        String content = "您的需求\"" + demandTitle + "\"合同已审核通过，需支付平台费用：" + amount + "元。\n请及时处理，以免影响项目进度。";
        
        message.setContent(content);
        message.setType(MESSAGE_TYPE_SYSTEM);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        
        portalMessageService.insertPortalMessage(message);
    }

    @Override
    public void sendExpertAuditNotification(Long userId, boolean approved) {
        PortalMessage message = new PortalMessage();
        message.setTitle(approved ? "专家审核通过" : "专家审核不通过");
        String content;
        if (approved) {
            content = "恭喜！您的专家信息已通过审核。";
        } else {
            content = "很抱歉，您的专家信息审核未通过。";
        }
        message.setContent(content);
        message.setType(MESSAGE_TYPE_AUDIT);
        message.setReceiverId(userId);
        message.setCreateTime(new Date());
        portalMessageService.insertPortalMessage(message);
    }
} 