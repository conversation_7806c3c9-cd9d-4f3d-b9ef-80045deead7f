package com.bocloud.portal.business.service.impl;

import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.PersonalInfoStatus;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.domain.web.dto.PersonalVerificationDTO;
import com.bocloud.portal.business.mapper.PortalUserPersonalInfoMapper;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.IVerificationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.beans.BeanUtils;

/**
 * 用户个人信息 服务实现
 */
@Service
public class PortalUserPersonalInfoServiceImpl implements IPortalUserPersonalInfoService {
    
    @Autowired
    private PortalUserPersonalInfoMapper portalUserPersonalInfoMapper;
    
    @Autowired
    private IVerificationRecordService verificationRecordService;

    @Autowired
    private IPortalUserService portalUserService;

    /**
     * 查询用户个人信息
     * 
     * @param userId 用户ID
     * @return 用户个人信息
     */
    @Override
    public PersonalInfo selectPendingPersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_PENDING.getCode());
    }

    @Override
    public PersonalInfo selectActivePersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectActiveByUserId(userId);
    }

    @Override
    public PersonalInfo selectLatestRejectedPersonalInfoByUserId(Long userId) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_REJECTED.getCode());
    }

    /**
     * 聚合优先级查询个人信息：
     * 1. 优先返回“待审核”状态的个人信息（status=0）
     * 2. 如果没有待审核，则返回“已通过且有效”状态的个人信息
     * 3. 如果没有已通过，则返回“最新未通过”状态的个人信息（status=2）
     * 4. 如果都没有，返回null
     *
     * @param userId 用户ID
     * @return 个人信息对象，优先级：待审核 > 已通过 > 最新未通过 > null
     */
    @Override
    public PersonalInfo selectBestPersonalInfoByUserId(Long userId) {
        PersonalInfo pending = selectPendingPersonalInfoByUserId(userId);
        if (pending != null) return pending;
        PersonalInfo active = selectActivePersonalInfoByUserId(userId);
        if (active != null) return active;
        PersonalInfo rejected = selectLatestRejectedPersonalInfoByUserId(userId);
        if (rejected != null) return rejected;
        return null;
    }

    /**
     * 根据用户ID和状态查询最新的个人信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 个人信息
     */
    @Override
    public PersonalInfo selectLatestByUserIdAndStatus(Long userId, String status) {
        return portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, status);
    }

    /**
     * 新增用户个人信息
     * 
     * @param personalInfo 用户个人信息
     * @return 结果
     */
    @Override
    public int insertPortalUserPersonalInfo(PersonalInfo personalInfo) {
        return portalUserPersonalInfoMapper.insertPortalUserPersonalInfo(personalInfo);
    }

    /**
     * 修改用户个人信息
     * 
     * @param personalInfo 用户个人信息
     * @return 结果
     */
    @Override
    public int updatePortalUserPersonalInfo(PersonalInfo personalInfo) {
        return portalUserPersonalInfoMapper.updatePortalUserPersonalInfo(personalInfo);
    }

    /**
     * 插入或更新个人信息（自动判断插入或更新）
     * 
     * @param personalInfo 个人信息
     * @param operator 操作人
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrUpdatePersonalInfo(PersonalInfo personalInfo, String operator) {
        // 检查是否已存在个人信息
        PersonalInfo existingInfo = selectBestPersonalInfoByUserId(personalInfo.getUserId());
        
        if (existingInfo != null) {
            // 如果存在，设置ID进行更新
            personalInfo.setId(existingInfo.getId());
            personalInfo.setUpdateBy(operator);
            return updatePortalUserPersonalInfo(personalInfo);
        } else {
            // 如果不存在，设置创建人信息和默认值并插入
            personalInfo.setCreateBy(operator);
            personalInfo.setUpdateBy(operator);
            // 设置默认状态为正常
            personalInfo.setStatus(PersonalInfoStatus.STATUS_NORMAL.getCode());
            // 设置删除标志为未删除
            personalInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
            // 设置当前有效
            personalInfo.setActive(PersonalInfoStatus.ACTIVE_YES.getCode());
            return insertPortalUserPersonalInfo(personalInfo);
        }
    }

    /**
     * 提交实名认证申请
     * 
     * @param personalInfo 用户个人信息
     * @return 结果
     */
    @Override
    @Transactional
    public int submitVerification(PersonalInfo personalInfo) {
        // 1. 保存个人信息
        int rows = portalUserPersonalInfoMapper.insertPortalUserPersonalInfo(personalInfo);
        if (rows <= 0) {
            return 0;
        }
        
        // 2. 创建认证状态记录
        VerificationRecord verificationRecord = new VerificationRecord();
        verificationRecord.setUserId(personalInfo.getUserId());
        verificationRecord.setVerificationType(VerificationType.PERSONAL.getCode());
        verificationRecord.setStatus(VerificationStatus.REVIEWING.getCode());
        
        return verificationRecordService.insertVerificationRecord(verificationRecord);
    }

    /**
     * 停用个人信息
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deactivateUserPersonalInfo(Long userId) {
        return portalUserPersonalInfoMapper.deactivateUserPersonalInfo(userId);
    }

    /**
     * 提交个人认证
     * 
     * @param userId 用户ID
     * @param verificationDTO 认证信息
     * @param operator 操作人
     * @return 结果（1成功，0失败，-1已有待审核）
     */
    @Override
    @Transactional
    public int submitPersonalVerification(Long userId, PersonalVerificationDTO verificationDTO, String operator) {
        // 1. 检查是否已有待审核的认证信息
        PersonalInfo pending = portalUserPersonalInfoMapper.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_PENDING.getCode());
        if (pending != null) {
            return -1; // 已有待审核的认证信息
        }

        // 1. 更新portal user表的状态
        PortalUser portalUser = portalUserService.selectPortalUserById(userId);;
        portalUser.setVerificationStatus(VerificationStatus.REVIEWING.getCode());
        // 认证类型为 个人
        portalUser.setVerificationType(VerificationType.PERSONAL.getCode());
        portalUserService.updatePortalUser(portalUser);

        // 2. 转换为PersonalInfo对象
        PersonalInfo personalInfo = portalUserPersonalInfoMapper.selectActiveByUserId(userId);
        if (personalInfo == null) {
            personalInfo = new PersonalInfo();
            personalInfo.setUserId(userId);
            personalInfo.setCreateBy(operator);
            personalInfo.setUpdateBy(operator);
            personalInfo.setStatus(PersonalInfoStatus.STATUS_PENDING.getCode());
            personalInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
            personalInfo.setActive(PersonalInfoStatus.ACTIVE_NO.getCode());
        }
        BeanUtils.copyProperties(verificationDTO, personalInfo);
        
        // 3. 设置用户ID和创建人信息
        personalInfo.setUserId(userId);
        personalInfo.setCreateBy(operator);
        
        // 4. 设置认证状态为待审核
        personalInfo.setStatus(PersonalInfoStatus.STATUS_PENDING.getCode());
        // 5. 设置删除标志为未删除
        personalInfo.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        // 6. 设置是否当前有效为否
        personalInfo.setActive(PersonalInfoStatus.ACTIVE_NO.getCode());

        // 7. 保存认证信息
        int rows = portalUserPersonalInfoMapper.insertPortalUserPersonalInfo(personalInfo);
        if (rows <= 0) {
            return 0;
        }
        
        // 7. 创建认证状态记录
        VerificationRecord verificationRecord = new VerificationRecord();
        verificationRecord.setUserId(userId);
        verificationRecord.setVerificationType(VerificationType.PERSONAL.getCode());
        verificationRecord.setStatus(VerificationStatus.REVIEWING.getCode());
        verificationRecord.setName(verificationDTO.getRealName());
        verificationRecord.setIdCardNumber(personalInfo.getIdCardNumber());
        verificationRecord.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        verificationRecord.setCreateBy(operator);
        
        return verificationRecordService.insertVerificationRecord(verificationRecord);
    }
}