<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.AchievementsMapper">

    <resultMap type="Achievements" id="AchievementsResult">
        <result property="achievementId"    column="achievement_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="categoryId"    column="category_id"    />
        <result property="status"    column="status"    />
        <result property="keywords"    column="keywords"    />
        <result property="overview"    column="overview"    />
        <result property="views"    column="views"    />
        <result property="collections"    column="collections"    />
        <result property="orderNo"    column="orderno"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAchievementsVo">
        select p.achievement_id, p.title, p.keywords, p.category_id, p.status,
               p.views, p.collections, p.overview, p.content,p.orderno,p.del_flag,
               p.create_by, p.create_time, p.update_by, p.update_time
        from achievements p
    </sql>

    <select id="selectAchievementsList" parameterType="Achievements" resultMap="AchievementsResult">
        <include refid="selectAchievementsVo"/>
        <where>
            <if test="title != null  and title != ''"> and p.title = #{title}</if>
            <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
            <if test="status != null "> and p.status = #{status}</if>
            <if test="keywords != null and keywords != ''">
                and (
                <foreach collection="keywords.split(',')" item="keyword" separator="or">
                    p.keywords like concat('%', #{keyword}, '%')
                    or p.title like concat('%', #{keyword}, '%')
                </foreach>
                )
            </if>
            <if test="overview != null and overview != ''"> and p.overview like concat('%', #{overview}, '%')</if>
            <if test="views != null "> and p.views = #{views}</if>
            <if test="collections != null "> and p.collections = #{collections}</if>
            and p.del_flag = '0'
        </where>
        order by
        p.orderno desc,  -- 成果排序序号
        p.create_time desc  -- 最后按创建时间倒序
    </select>

    <select id="selectAchievementsByAchievementId" parameterType="Long" resultMap="AchievementsResult">
        <include refid="selectAchievementsVo"/>
        where p.achievement_id = #{achievementId}
    </select>

    <insert id="insertAchievements" parameterType="Achievements" useGeneratedKeys="true" keyProperty="achievementId">
        insert into achievements
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="keywords != null">keywords,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="status != null">status,</if>
            <if test="views != null">views,</if>
            <if test="collections != null">collections,</if>
            <if test="overview != null">overview,</if>
            <if test="content != null">content,</if>
            <if test="orderNo != null">orderno,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="status != null">#{status},</if>
            <if test="views != null">#{views},</if>
            <if test="collections != null">#{collections},</if>
            <if test="overview != null">#{overview},</if>
            <if test="content != null">#{content},</if>
            <if test="orderNo != null">#{orderNo},</if>
            '0',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateAchievements" parameterType="Achievements">
        update achievements
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="views != null">views = #{views},</if>
            <if test="collections != null">collections = #{collections},</if>
            <if test="overview != null">overview = #{overview},</if>
            <if test="content != null">content = #{content},</if>
            <if test="orderNo != null">orderno = #{orderNo},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where achievement_id = #{achievementId}
    </update>

    <update id="deleteAchievementsByAchievementId" parameterType="Long">
        update achievements set del_flag = '1' where achievement_id = #{achievementId}
    </update>

    <update id="deleteAchievementsByAchievementIds" parameterType="String">
        update achievements set del_flag = '1' where achievement_id in
        <foreach item="achievementId" collection="array" open="(" separator="," close=")">
            #{achievementId}
        </foreach>
    </update>
</mapper>