package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.common.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class RegisterUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /**
     * 用户名（账号密码登录使用，不能为空，长度不能少于5个字符）
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 5, message = "用户名长度不能少于5个字符")
    private String username;

    /**
     * 密码（账号密码登录使用，不能为空，存储加密后的值，必须包含英文和数字）
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码长度不能少于6个字符")
    @Pattern(regexp = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+$", message = "密码必须包含英文和数字")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 验证确认密码是否与密码相同
     */
    @AssertTrue(message = "确认密码必须与密码一致")
    public boolean isPasswordConfirmed() {
        return password != null && password.equals(confirmPassword);
    }

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码编码
     */
    private String uuid;

    /** 手机号（手机验证码登录使用，可为空） */
    private String phone;

    /** 邮箱（登录使用，可为空） */
    private String email;

    /** 微信OpenID（微信扫码登录使用，可为空） */
    private String wechatOpenid;

    /** 微信UnionID（用于跨公众号和小程序唯一识别用户，可为空） */
    private String wechatUnionid;

    /** 昵称 */
    private String nickname;

    /** 头像URL */
    private String avatar;

    /** 性别 */
    private String gender;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /** 账号状态（1=正常，0=禁用） */
    private Integer status;

    /** 用户类型（1个人用户 2企业用户） */
    @NotBlank(message = "用户类型不能为空")
    private String type;

    private Date lastLoginTime;

    // 个人用户信息字段
    /** 真实姓名 */
    private String realName;

    /** 身份证号 */
    private String idCardNumber;

    /** 生日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /** 职业 */
    private String occupation;

    /** 地址 */
    private String address;

    /** 个人简介 */
    private String profile;

    /** 教育背景 */
    private String education;

    /** 工作经历 */
    private String workExperience;

    /** 证书 */
    private String certifications;

    /** 社交媒体 */
    private String socialMedia;

    // 企业用户信息字段
    /** 企业名称 */
    private String companyName;

    /** 企业类型 */
    private String companyType;

    /** 营业执照号 */
    private String businessLicense;

    /** 税号 */
    private String taxNumber;

    /** 企业地址 */
    private String companyAddress;

    /** 法定代表人 */
    private String legalRepresentative;

    /** 联系人姓名 */
    private String contactPerson;

    /** 联系人电话 */
    private String contactPhone;

    /** 联系人邮箱 */
    private String contactEmail;

    /** 企业网站 */
    private String companyWebsite;

    /** 所属行业 */
    private String industry;

    /** 成立日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishedDate;

    /** 员工人数 */
    private Integer employeesCount;

    /** 经营范围 */
    private String businessScope;

    /** 企业简介 */
    private String introduction;

    /** 企业logo */
    private String logo;

    /**
     * 验证个人信息必填字段
     */
    public void validatePersonalInfo() {
        if (StringUtils.isEmpty(realName)) {
            throw new ServiceException("真实姓名不能为空");
        }
        if (StringUtils.isEmpty(gender)) {
            throw new ServiceException("性别不能为空");
        }
        if (StringUtils.isEmpty(idCardNumber)) {
            throw new ServiceException("身份证号不能为空");
        }
    }

    /**
     * 验证企业信息必填字段
     */
    public void validateCompanyInfo() {
        if (StringUtils.isEmpty(companyName)) {
            throw new ServiceException("企业名称不能为空");
        }
        if (StringUtils.isEmpty(businessLicense)) {
            throw new ServiceException("营业执照号不能为空");
        }
        if (StringUtils.isEmpty(taxNumber)) {
            throw new ServiceException("税号不能为空");
        }
        if (StringUtils.isEmpty(legalRepresentative)) {
            throw new ServiceException("法定代表人不能为空");
        }
        if (StringUtils.isEmpty(contactPerson)) {
            throw new ServiceException("联系人姓名不能为空");
        }
        if (StringUtils.isEmpty(contactPhone)) {
            throw new ServiceException("联系人电话不能为空");
        }
        if (StringUtils.isEmpty(contactEmail)) {
            throw new ServiceException("联系人邮箱不能为空");
        }
    }

}
