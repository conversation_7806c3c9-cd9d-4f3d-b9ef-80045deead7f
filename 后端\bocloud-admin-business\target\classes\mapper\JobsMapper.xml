<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.JobsMapper">

    <resultMap type="Jobs" id="JobsResult">
        <result property="jobId"    column="job_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="title"    column="title"    />
        <result property="location"    column="location"    />
        <result property="salaryMin"    column="salary_min"    />
        <result property="salaryMax"    column="salary_max"    />
        <result property="description"    column="description"    />
        <result property="requirements"    column="requirements"    />
        <result property="status"    column="status"    />
        <result property="deadline"    column="deadline"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectJobsVo">
        select job_id, company_name, title, location, salary_min, salary_max, description, requirements, status, deadline, create_by, create_time, update_by, update_time from jobs
    </sql>

    <select id="selectJobsList" parameterType="Jobs" resultMap="JobsResult">
        <include refid="selectJobsVo"/>
        <where>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="salaryMin != null "> and salary_min = #{salaryMin}</if>
            <if test="salaryMax != null "> and salary_max = #{salaryMax}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="requirements != null  and requirements != ''"> and requirements = #{requirements}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
        </where>
    </select>

    <select id="selectJobsByJobId" parameterType="Long" resultMap="JobsResult">
        <include refid="selectJobsVo"/>
        where job_id = #{jobId}
    </select>

    <insert id="insertJobs" parameterType="Jobs" useGeneratedKeys="true" keyProperty="jobId">
        insert into jobs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="requirements != null and requirements != ''">requirements,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="deadline != null">deadline,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="requirements != null and requirements != ''">#{requirements},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateJobs" parameterType="Jobs">
        update jobs
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="requirements != null and requirements != ''">requirements = #{requirements},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where job_id = #{jobId}
    </update>

    <delete id="deleteJobsByJobId" parameterType="Long">
        delete from jobs where job_id = #{jobId}
    </delete>

    <delete id="deleteJobsByJobIds" parameterType="String">
        delete from jobs where job_id in
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>
</mapper>