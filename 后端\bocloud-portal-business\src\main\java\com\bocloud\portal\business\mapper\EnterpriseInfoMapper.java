package com.bocloud.portal.business.mapper;

import com.bocloud.domain.web.EnterpriseInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 企业信息Mapper接口
 */
public interface EnterpriseInfoMapper {
    /**
     * 查询企业信息列表
     * 
     * @param enterpriseInfo 企业信息
     * @return 企业信息集合
     */
    List<EnterpriseInfo> selectEnterpriseInfoList(EnterpriseInfo enterpriseInfo);

    /**
     * 查询企业信息
     * 
     * @param enterpriseId 企业信息主键
     * @return 企业信息
     */
    EnterpriseInfo selectEnterpriseInfoById(Long enterpriseId);

    /**
     * 根据用户ID查询企业信息
     * 
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectEnterpriseInfoByUserId(Long userId);

    /**
     * 根据用户ID和状态查询最新的企业信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 企业信息
     */
    EnterpriseInfo selectLatestByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 查询用户当前有效的企业信息
     * 
     * @param userId 用户ID
     * @return 企业信息
     */
    EnterpriseInfo selectActiveByUserId(Long userId);

    /**
     * 新增企业信息
     * 
     * @param enterpriseInfo 企业信息
     * @return 结果
     */
    int insertEnterpriseInfo(EnterpriseInfo enterpriseInfo);

    /**
     * 修改企业信息
     * 
     * @param enterpriseInfo 企业信息
     * @return 结果
     */
    int updateEnterpriseInfo(EnterpriseInfo enterpriseInfo);

    /**
     * 删除企业信息
     * 
     * @param enterpriseId 企业信息主键
     * @return 结果
     */
    int deleteEnterpriseInfoById(Long enterpriseId);

    /**
     * 批量删除企业信息
     * 
     * @param enterpriseIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEnterpriseInfoByIds(Long[] enterpriseIds);

} 