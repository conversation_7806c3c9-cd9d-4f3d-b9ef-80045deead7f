package com.bocloud.admin.business.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.admin.business.mapper.DemandChargeMapper;
import com.bocloud.domain.system.demand.DemandCharge;
import com.bocloud.admin.business.service.IDemandChargeService;

/**
 * 需求收费Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class DemandChargeServiceImpl implements IDemandChargeService {
    @Autowired
    private DemandChargeMapper demandChargeMapper;

    /**
     * 新增需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    @Override
    public int insertDemandCharge(DemandCharge demandCharge) {
        demandCharge.setCreateTime(new Date());
        return demandChargeMapper.insertDemandCharge(demandCharge);
    }

    /**
     * 查询需求收费列表
     * 
     * @param demandCharge 需求收费信息
     * @return 需求收费集合
     */
    @Override
    public List<DemandCharge> selectDemandChargeList(DemandCharge demandCharge) {
        return demandChargeMapper.selectDemandChargeList(demandCharge);
    }

    /**
     * 查询需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 需求收费信息
     */
    @Override
    public DemandCharge selectDemandChargeById(Long chargeId) {
        return demandChargeMapper.selectDemandChargeById(chargeId);
    }

    /**
     * 根据合同ID查询需求收费
     * 
     * @param contractId 合同ID
     * @return 需求收费信息
     */
    @Override
    public DemandCharge selectDemandChargeByContractId(Long contractId) {
        return demandChargeMapper.selectDemandChargeByContractId(contractId);
    }

    /**
     * 修改需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    @Override
    public int updateDemandCharge(DemandCharge demandCharge) {
        demandCharge.setUpdateTime(new Date());
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }

    /**
     * 批量确认收费
     * 
     * @param chargeIds 需要确认的收费ID数组
     * @return 结果
     */
    @Override
    public int confirmCharges(Long[] chargeIds) {
        return demandChargeMapper.updateDemandChargeStatus(chargeIds, DemandCharge.STATUS_CHARGED);
    }

    /**
     * 批量确认开票
     * 
     * @param chargeIds 需要确认开票的收费ID数组
     * @return 结果
     */
    @Override
    public int confirmInvoices(Long[] chargeIds) {
        return demandChargeMapper.updateDemandChargeStatus(chargeIds, DemandCharge.STATUS_INVOICED);
    }

    /**
     * 批量完成收费
     * 
     * @param chargeIds 需要完成的收费ID数组
     * @return 结果
     */
    @Override
    public int completeCharges(Long[] chargeIds) {
        return demandChargeMapper.updateDemandChargeStatus(chargeIds, DemandCharge.STATUS_COMPLETED);
    }

    /**
     * 批量取消收费
     * 
     * @param chargeIds 需要取消的收费ID数组
     * @return 结果
     */
    @Override
    public int cancelCharges(Long[] chargeIds) {
        return demandChargeMapper.updateDemandChargeStatus(chargeIds, DemandCharge.STATUS_CANCELLED);
    }
} 