package com.bocloud.domain.system.demand;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 需求合同对象 demand_contract
 * 
 * <AUTHOR>
 */
@Data
public class DemandContract extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 收费类型
    public static final String CHARGE_TYPE_FIXED = "1";      // 固定金额
    public static final String CHARGE_TYPE_PERCENT = "2";    // 百分比

    /** 合同ID */
    private Long contractId;

    /** 合同编号 */
    @Excel(name = "合同编号")
    private String contractNo;

    /** 需求ID */
    @Excel(name = "需求ID")
    private Long demandId;

    /** 方案ID */
    @Excel(name = "方案ID")
    private Long solutionId;

    /** 甲方会员ID */
    @Excel(name = "甲方会员ID")
    private Long memberIdA;

    /** 甲方会员名称 */
    @Excel(name = "甲方会员名称")
    private String memberNameA;

    /** 乙方会员ID */
    @Excel(name = "乙方会员ID")
    private Long memberIdB;

    /** 乙方会员名称 */
    @Excel(name = "乙方会员名称")
    private String memberNameB;

    /** 合同名称 */
    @Excel(name = "合同名称")
    private String contractName;

    /** 合同文件路径 */
    @Excel(name = "合同文件路径")
    private String contractFile;

    /** 签订日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签订日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signDate;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expiryDate;

    /** 合同金额 */
    @Excel(name = "合同金额")
    private BigDecimal contractAmount;

    /** 平台收费类型（1-固定金额，2-百分比） */
    @Excel(name = "收费类型", readConverterExp = "1=固定金额,2=百分比")
    private String chargeType;

    /** 固定收费金额 */
    @Excel(name = "固定收费金额")
    private BigDecimal fixedAmount;

    /** 收费比例（百分比时使用） */
    @Excel(name = "收费比例")
    private BigDecimal chargeRate;

    /** 状态（0-待签约，1-待审核，2-进行中，3-审核不通过，4-已到期，-1-已作废） */
    @Excel(name = "状态", readConverterExp = "0=待签约,1=待审核,2=进行中,3=审核不通过,4=已到期,-1=已作废")
    private String status;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("contractId", getContractId())
            .append("contractNo", getContractNo())
            .append("demandId", getDemandId())
            .append("contractName", getContractName())
            .append("contractFile", getContractFile())
            .append("signDate", getSignDate())
            .append("effectiveDate", getEffectiveDate())
            .append("expiryDate", getExpiryDate())
            .append("contractAmount", getContractAmount())
            .append("chargeType", getChargeType())
            .append("fixedAmount", getFixedAmount())
            .append("chargeRate", getChargeRate())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .toString();
    }
} 