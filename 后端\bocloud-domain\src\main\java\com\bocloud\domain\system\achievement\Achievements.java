package com.bocloud.domain.system.achievement;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.system.CommonAttachment;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * @description 成果发布对象
 * <AUTHOR>
 * @date 2025-07-16
 */
public class Achievements extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 状态常量 */
    public static final String STATUS_DRAFT = "1";      // 草稿
    public static final String STATUS_PUBLISH = "2";    // 发布
    public static final String STATUS_CANCEL = "3";     // 取消
    public static final String STATUS_WAIT_AUDIT = "4"; // 待审核


    /** 信息ID */
    private Long achievementId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 关键字，多个用英文逗号分隔 */
    @Excel(name = "关键字")
    private String keywords;

    /** 分类ID */
    @Excel(name = "分类ID")
    private String categoryId;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer views;

    /** 收藏次数 */
    @Excel(name = "收藏次数")
    private Integer collections;

    /** 概述 */
    private String overview;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 排序号（数字越大越靠前） */
    @Excel(name = "成果排序")
    private Integer orderNo;

    /** 删除标志（0代表存在 1代表删除） */
    @Excel(name = "删除标志")
    private String delFlag;

    /**是否收藏*/
    private String collectFlag;

    /** 附件列表 */
    private List<CommonAttachment> attachments;

    public String getCollectFlag() {
        return collectFlag;
    }

    public void setCollectFlag(String collectFlag) {
        this.collectFlag = collectFlag;
    }

    public Long getAchievementId() {
        return achievementId;
    }

    public void setAchievementId(Long achievementId) {
        this.achievementId = achievementId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setCategoryId(String categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryId()
    {
        return categoryId;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setKeywords(String keywords)
    {
        this.keywords = keywords;
    }

    public String getKeywords()
    {
        return keywords;
    }

    public String getOverview() {
        return overview;
    }

    public void setOverview(String overview) {
        this.overview = overview;
    }

    public void setViews(Integer views)
    {
        this.views = views;
    }

    public Integer getViews()
    {
        return views;
    }

    public List<CommonAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<CommonAttachment> attachments) {
        this.attachments = attachments;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getCollections() {
        return collections;
    }

    public void setCollections(Integer collections) {
        this.collections = collections;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("achievementId", getAchievementId())
                .append("title", getTitle())
                .append("keywords", getKeywords())
                .append("categoryId", getCategoryId())
                .append("status", getStatus())
                .append("collections", getCollections())
                .append("content", getContent())
                .append("orderNo", getOrderNo())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("attachments", getAttachments())
                .append("collectFlag", getCollectFlag())
                .toString();
    }
}
