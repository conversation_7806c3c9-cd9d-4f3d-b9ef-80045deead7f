package com.bocloud.domain.system;

import java.util.Date;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统审核记录对象 sys_audit
 */
@Data
public class SysAudit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 审核记录ID */
    private Long auditId;

    /** 业务类型(solution:方案审核, bid:中标审核, account:账号审核等) */
    @Excel(name = "业务类型")
    private String businessType;

    /** 关联的业务ID */
    @Excel(name = "业务ID")
    private Long businessId;

    /** 审核类型(0:初审, 1:复审, 2:终审等) */
    @Excel(name = "审核类型", readConverterExp = "0=初审,1=复审,2=终审")
    private String auditType;

    /** 审核前状态 */
    @Excel(name = "审核前状态")
    private String fromStatus;

    /** 审核后状态 */
    @Excel(name = "审核后状态")
    private String toStatus;

    /** 审核结果(0:通过, 1:驳回) */
    @Excel(name = "审核结果", readConverterExp = "0=通过,1=驳回")
    private String auditResult;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long auditUserId;

    /** 审核人姓名 */
    @Excel(name = "审核人姓名")
    private String auditUserName;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;
} 