package com.bocloud.admin.business.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bocloud.domain.system.pilot_application.PilotApplication;
import com.bocloud.domain.system.pilot_application.PilotApplicationAuditRequest;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.admin.business.mapper.PilotApplicationMapper;
import com.bocloud.admin.business.service.IPilotApplicationService;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.SecurityUtils;

/**
 * 中试申请Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PilotApplicationServiceImpl implements IPilotApplicationService 
{
    @Autowired
    private PilotApplicationMapper pilotApplicationMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    @Autowired
    private ISysAuditService sysAuditService;

    /**
     * 查询中试申请
     * 
     * @param id 中试申请主键
     * @return 中试申请
     */
    @Override
    public PilotApplication selectPilotApplicationById(Long id)
    {
        PilotApplication pilotApplication = pilotApplicationMapper.selectPilotApplicationById(id);
        if (pilotApplication != null) {
            // 查询附件列表
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(pilotApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
            pilotApplication.setAttachments(attachments);

            // 查询报告列表
            query = new CommonAttachment();
            query.setBusinessId(pilotApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_REPORT);
            List<CommonAttachment> reports = attachmentService.selectCommonAttachmentList(query);
            pilotApplication.setReports(reports);
        }
        return pilotApplication;
    }

    /**
     * 查询中试申请列表
     * 
     * @param pilotApplication 中试申请
     * @return 中试申请集合
     */
    @Override
    public List<PilotApplication> selectPilotApplicationList(PilotApplication pilotApplication)
    {
        // 默认查询未删除的数据
        if (pilotApplication.getDelFlag() == null) {
            pilotApplication.setDelFlag("0");
        }
        return pilotApplicationMapper.selectPilotApplicationList(pilotApplication);
    }

    /**
     * 修改中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePilotApplication(PilotApplication pilotApplication)
    {
        pilotApplication.setUpdateTime(DateUtils.getNowDate());
        pilotApplication.setUpdateBy(SecurityUtils.getUsername());
        return pilotApplicationMapper.updatePilotApplication(pilotApplication);
    }

    /**
     * 审核中试申请
     * 
     * @param request 审核请求
     * @return 结果
     */
    @Override
    @Transactional
    public int auditPilotApplication(PilotApplicationAuditRequest request)
    {
        // 获取原申请信息
        PilotApplication originalApplication = pilotApplicationMapper.selectPilotApplicationById(request.getPilotApplicationId());
        if (originalApplication == null) {
            throw new ServiceException("申请不存在");
        }

        // 检查申请状态
        if (!PilotApplication.STATUS_PENDING_REVIEW.equals(originalApplication.getStatus())) {
            throw new ServiceException("该申请不是待审核状态，无法进行审核");
        }

        // 更新申请状态
        PilotApplication updateApplication = new PilotApplication();
        updateApplication.setId(request.getPilotApplicationId());
        updateApplication.setReviewComment(request.getReviewComment());
        updateApplication.setAdminPrice(request.getAdminPrice());
        updateApplication.setAdminPriceRemark(request.getAdminPriceRemark());
        updateApplication.setUpdateTime(DateUtils.getNowDate());
        updateApplication.setUpdateBy(SecurityUtils.getUsername());

        updateApplication.setStatus(request.getAuditResult());

        int rows = pilotApplicationMapper.updatePilotApplication(updateApplication);
        if (rows <= 0) {
            throw new ServiceException("更新申请状态失败");
        }

        // 创建审核记录
        SysAudit sysAudit = new SysAudit();
        sysAudit.setBusinessType("PILOT_APPLICATION");
        sysAudit.setBusinessId(request.getPilotApplicationId());
        sysAudit.setAuditType("0");  // 审核类型：初审
        sysAudit.setFromStatus(originalApplication.getStatus());
        sysAudit.setToStatus(updateApplication.getStatus());
        sysAudit.setAuditResult(request.getAuditResult());
        sysAudit.setReviewComment(request.getReviewComment());
        sysAudit.setAuditUserId(SecurityUtils.getUserId());
        sysAudit.setAuditUserName(SecurityUtils.getUsername());
        sysAudit.setAuditTime(new Date());
        sysAudit.setCreateTime(new Date());

        rows = sysAuditService.insertSysAudit(sysAudit);
        if (rows <= 0) {
            throw new ServiceException("记录审核信息失败");
        }

        return rows;
    }

    @Override
    @Transactional
    public int auditPayment(PilotApplication pilotApplication) {
        // 验证申请是否存在
        PilotApplication oldApplication = pilotApplicationMapper.selectPilotApplicationById(pilotApplication.getId());
        if (oldApplication == null) {
            throw new ServiceException("申请不存在");
        }
        // 验证付款状态是否为已上传凭证
        if (!PilotApplication.PAYMENT_STATUS_UPLOADED.equals(oldApplication.getPaymentStatus())) {
            throw new ServiceException("当前付款状态不允许审核");
        }
        // 验证审核结果
        if (pilotApplication.getPaymentStatus() == null) {
            throw new ServiceException("请选择审核结果");
        }
        // 设置审核结果
        oldApplication.setPaymentStatus(pilotApplication.getPaymentStatus());
        oldApplication.setReviewComment(pilotApplication.getReviewComment());
        oldApplication.setAuditPaymentDate(DateUtils.getNowDate());
        oldApplication.setUpdateBy(SecurityUtils.getUsername());
        // 如果确认收款，同时更新主状态为已付款
        if (PilotApplication.PAYMENT_STATUS_CONFIRMED.equals(pilotApplication.getPaymentStatus())) {
            oldApplication.setStatus(PilotApplication.STATUS_PAID);
        }
        return pilotApplicationMapper.updatePilotApplication(oldApplication);
    }
} 