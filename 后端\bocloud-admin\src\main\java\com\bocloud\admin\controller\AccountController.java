package com.bocloud.admin.controller;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.admin.business.service.IAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 账号管理控制器
 */
@RestController
@RequestMapping("/admin/account")
public class AccountController extends BaseController {

    @Autowired
    private IAccountService accountService;

    /**
     * 获取账号列表
     */
    @PreAuthorize("@ss.hasPermi('admin:account:list')")
    @GetMapping("/list")
    public TableDataInfo list(PortalUser portalUser) {
        startPage();
        List<PortalUser> list = accountService.list(portalUser);
        return getDataTable(list);
    }

    /**
     * 获取会员详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:account:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return success(accountService.getInfo(userId));
    }


    /**
     * 根据用户ID，获取用户待审核信息
     * @param userId 用户ID
     * @return 待审核信息
     */
    @GetMapping(value = "/pendingInfo/{userId}")
    public AjaxResult getPendingInfo(@PathVariable("userId") Long userId) {
        return success(accountService.getPendingInfo(userId));
    }

    /**
     * 获取所有待审核的认证信息列表
     * @param type 认证类型：personal-个人认证，enterprise-企业认证，不传则查询所有
     * @return 待审核信息列表
     */
    @PreAuthorize("@ss.hasPermi('admin:account:list')")
    @GetMapping("/pending/list")
    public TableDataInfo getPendingList(@RequestParam(value = "type", required = false) String type) {
        startPage();
        List<Map<String, Object>> list = accountService.getPendingList(type);
        return getDataTable(list);
    }

    /**
     * 审核实名信息
     * 审核流程：
     * 1. 验证用户是否存在
     * 2. 验证用户是否已提交实名认证信息
     * 3. 根据审核状态(status)进行不同处理：
     *    - 通过：更新用户实名认证状态为已认证，并记录审核备注
     *    - 驳回：更新用户实名认证状态为未认证，并记录驳回原因
     * 4. 记录审核操作日志
     * 
     */
    @PreAuthorize("@ss.hasPermi('admin:account:audit')")
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("id").toString());
        String status = params.get("status").toString();
        String remark = (String) params.get("remark");
        return toAjax(accountService.audit(userId, status, remark));
    }

    /**
     * 获取专家列表
     */
    @PreAuthorize("@ss.hasPermi('admin:account:expert')")
    @GetMapping("/expertList")
    public TableDataInfo expertList(ExpertInfo expertInfo) {
        startPage();
        List<PortalUser> list = accountService.expertList(expertInfo);
        return getDataTable(list);
    }
}
