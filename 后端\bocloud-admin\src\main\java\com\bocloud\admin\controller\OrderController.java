package com.bocloud.admin.controller;

import com.bocloud.admin.business.service.IOrderService;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.system.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/admin/order")
public class OrderController extends BaseController {
    @Autowired
    private IOrderService orderService;

    /** 查询订单列表 */
    @PreAuthorize("@ss.hasPermi('admin:order:list')")
    @GetMapping("/list")
    public AjaxResult list(Order order) {
        List<Order> list = orderService.selectOrderList(order);
        return success(list);
    }

    /** 获取订单详细信息 */
    @PreAuthorize("@ss.hasPermi('admin:order:query')")
    @GetMapping("/{orderId}")
    public AjaxResult getInfo(@PathVariable Long orderId) {
        return success(orderService.selectOrderById(orderId));
    }

    /** 新增订单 */
    @PreAuthorize("@ss.hasPermi('admin:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Order order) {
        return toAjax(orderService.insertOrder(order));
    }

    /** 修改订单 */
    @PreAuthorize("@ss.hasPermi('admin:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Order order) {
        return toAjax(orderService.updateOrder(order));
    }

    /** 删除订单 */
    @PreAuthorize("@ss.hasPermi('admin:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds) {
        return toAjax(orderService.deleteOrderByIds(orderIds));
    }
} 