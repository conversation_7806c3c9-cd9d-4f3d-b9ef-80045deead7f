package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.post.Posts;

import java.util.List;

public interface PostsMapper
{
    /**
     * 查询信息发布
     *
     * @param postId 信息发布主键
     * @return 信息发布
     */
    public Posts selectPostsByPostId(Long postId);

    /**
     * 查询信息发布列表
     *
     * @param posts 信息发布
     * @return 信息发布集合
     */
    public List<Posts> selectPostsList(Posts posts);

    /**
     * 更新信息浏览次数
     *
     * @param postId 信息ID
     * @return 结果
     */
    public int updatePostViews(Long postId);

    /**
     * 获取相关新闻列表
     *
     * @param posts 当前新闻信息
     * @return 相关新闻列表
     */
    public List<Posts> selectRelatedPosts(Posts posts);

    /**
     * 获取热门资讯列表
     *
     * @param posts 查询条件
     * @return 热门资讯列表
     */
    public List<Posts> selectHotPosts(Posts posts);
}