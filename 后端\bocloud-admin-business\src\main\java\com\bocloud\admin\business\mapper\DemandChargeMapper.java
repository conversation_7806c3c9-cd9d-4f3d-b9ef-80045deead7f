package com.bocloud.admin.business.mapper;

import java.util.List;
import com.bocloud.domain.system.demand.DemandCharge;
import org.apache.ibatis.annotations.Mapper;

/**
 * 需求收费Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DemandChargeMapper {
    /**
     * 新增需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int insertDemandCharge(DemandCharge demandCharge);

    /**
     * 查询需求收费列表
     * 
     * @param demandCharge 需求收费信息
     * @return 需求收费集合
     */
    public List<DemandCharge> selectDemandChargeList(DemandCharge demandCharge);

    /**
     * 查询需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeById(Long chargeId);

    /**
     * 根据合同ID查询需求收费
     * 
     * @param contractId 合同ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeByContractId(Long contractId);

    /**
     * 修改需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int updateDemandCharge(DemandCharge demandCharge);

    /**
     * 批量更新需求收费状态
     * 
     * @param chargeIds 需要更新的收费ID数组
     * @param status 状态
     * @return 结果
     */
    public int updateDemandChargeStatus(Long[] chargeIds, String status);
} 