package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.AdsMapper;
import com.bocloud.common.utils.DateUtils;
import  com.bocloud.domain.system.platform_service.Ads;
import com.bocloud.admin.business.service.IAdsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告申请Service业务层处理
 *
 * @date 2025-03-11
 */
@Service
public class AdsServiceImpl implements IAdsService
{
    @Autowired
    private AdsMapper adsMapper;

    /**
     * 查询广告申请
     *
     * @param adId 广告申请主键
     * @return 广告申请
     */
    @Override
    public Ads selectAdsByAdId(Long adId)
    {
        return adsMapper.selectAdsByAdId(adId);
    }

    /**
     * 查询广告申请列表
     *
     * @param ads 广告申请
     * @return 广告申请
     */
    @Override
    public List<Ads> selectAdsList(Ads ads)
    {
        return adsMapper.selectAdsList(ads);
    }

    /**
     * 新增广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    @Override
    public int insertAds(Ads ads)
    {
        ads.setCreateTime(DateUtils.getNowDate());
        return adsMapper.insertAds(ads);
    }

    /**
     * 修改广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    @Override
    public int updateAds(Ads ads)
    {
        ads.setUpdateTime(DateUtils.getNowDate());
        return adsMapper.updateAds(ads);
    }

    /**
     * 批量删除广告申请
     *
     * @param adIds 需要删除的广告申请主键
     * @return 结果
     */
    @Override
    public int deleteAdsByAdIds(Long[] adIds)
    {
        return adsMapper.deleteAdsByAdIds(adIds);
    }

    /**
     * 删除广告申请信息
     *
     * @param adId 广告申请主键
     * @return 结果
     */
    @Override
    public int deleteAdsByAdId(Long adId)
    {
        return adsMapper.deleteAdsByAdId(adId);
    }
}