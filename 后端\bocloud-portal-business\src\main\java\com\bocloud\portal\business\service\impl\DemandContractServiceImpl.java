package com.bocloud.portal.business.service.impl;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.DemandContractStatus;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.portal.business.mapper.DemandContractMapper;
import com.bocloud.portal.business.mapper.EnterpriseDemandMapper;
import com.bocloud.portal.business.mapper.DemandSolutionMapper;
import com.bocloud.portal.business.service.IDemandContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 需求方合同管理实现
 */
@Service
public class DemandContractServiceImpl implements IDemandContractService {

    @Autowired
    private DemandContractMapper demandContractMapper;
    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;
    @Autowired
    private DemandSolutionMapper demandSolutionMapper;

    @Override
    @Transactional
    public AjaxResult uploadContract(DemandContract contract) {
        // 查询该需求的合同记录（待签约且未删除）
        DemandContract exist = demandContractMapper.selectDemandContractByDemandId(contract.getDemandId());
        if (exist == null) {
            this.createContractForBid(contract.getDemandId(),contract.getSolutionId(), SecurityUtils.getUserId());
        }
        exist = demandContractMapper.selectDemandContractByDemandId(contract.getDemandId());
        // 只允许待签约状态上传合同
        if (!DemandContractStatus.TO_SIGN.getCode().equals(exist.getStatus())) {
            return AjaxResult.error("当前合同状态不允许上传合同文件！");
        }
        // 更新合同信息
        exist.setContractName(contract.getContractName());
        exist.setContractFile(contract.getContractFile());
        exist.setSignDate(contract.getSignDate());
        exist.setEffectiveDate(contract.getEffectiveDate());
        exist.setExpiryDate(contract.getExpiryDate());
        exist.setContractAmount(contract.getContractAmount());
        exist.setRemark(contract.getRemark());
        exist.setUpdateBy(contract.getUpdateBy());
        exist.setStatus(DemandContractStatus.PENDING.getCode()); // 1-待审核
        int rows = demandContractMapper.updateDemandContract(exist);
        if (rows > 0) {
            return AjaxResult.success("合同上传成功，等待平台审核！");
        } else {
            return AjaxResult.error("合同上传失败，请重试！");
        }
    }

    @Override
    @Transactional
    public void createContractForBid(Long demandId, Long solutionId, Long userId) {
        // 先检查是否已存在合同记录
        DemandContract existingContract = demandContractMapper.selectDemandContractByDemandId(demandId);
        if (existingContract != null) {
            return; // 如果已存在合同记录，直接返回
        }

        // 生成合同记录
        DemandContract contract = new DemandContract();
        contract.setDemandId(demandId);
        contract.setSolutionId(solutionId);
        contract.setStatus(DemandContractStatus.TO_SIGN.getCode()); // 0-待签约
        contract.setCreateBy(String.valueOf(userId));
        contract.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
        demandContractMapper.insertDemandContract(contract);
    }

    @Override
    public List<DemandContract> selectDemandContractList(DemandContract contract) {
        return demandContractMapper.selectDemandContractList(contract);
    }

    @Override
    public DemandContract detail(Long id) {
        return demandContractMapper.selectDemandContractById(id);
    }

    @Override
    @Transactional
    public int fixAllMemberIds() {
        List<DemandContract> contracts = demandContractMapper.selectAllContracts();
        int count = 0;
        for (DemandContract contract : contracts) {
            boolean needUpdate = false;
            // 甲方
            if (contract.getMemberIdA() == null) {
                Long memberIdA = null;
                if (contract.getDemandId() != null) {
                    memberIdA = enterpriseDemandMapper.selectEnterpriseDemandById(contract.getDemandId()).getPortalUserId();
                }
                contract.setMemberIdA(memberIdA);
                needUpdate = true;
            }
            // 乙方
            if (contract.getMemberIdB() == null && contract.getSolutionId() != null) {
                Long memberIdB = demandSolutionMapper.selectDemandSolutionById(contract.getSolutionId()).getPortalUserId();
                contract.setMemberIdB(memberIdB);
                needUpdate = true;
            }
            if (needUpdate) {
                demandContractMapper.updateDemandContract(contract);
                count++;
            }
        }
        return count;
    }
} 