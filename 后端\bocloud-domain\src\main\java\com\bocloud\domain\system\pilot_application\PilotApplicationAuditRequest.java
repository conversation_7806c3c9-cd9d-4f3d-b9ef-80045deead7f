package com.bocloud.domain.system.pilot_application;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中试申请审核请求对象
 * 
 * <AUTHOR>
 */
@Data
public class PilotApplicationAuditRequest
{
    /** 中试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long pilotApplicationId;

    /** 审核结果：0-通过，1-拒绝 */
    @NotNull(message = "审核结果不能为空")
    private String auditResult;

    /** 审核意见 */
    private String reviewComment;

    /** 管理员设置的价格 */
    private BigDecimal adminPrice;

    /** 管理员价格备注 */
    private String adminPriceRemark;
} 