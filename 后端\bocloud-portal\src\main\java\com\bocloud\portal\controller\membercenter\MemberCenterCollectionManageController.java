package com.bocloud.portal.controller.membercenter;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.CollectManageRequest;
import com.bocloud.portal.business.service.IAchievementsService;

@RestController
@RequestMapping("/membercenter/collectionManage")
public class MemberCenterCollectionManageController extends BaseController {

    @Autowired
    private IAchievementsService iAchievementsService;

    /**
     * 收藏/取消收藏
     * @param collectManageRequest
     * @return
     */
    @PostMapping("/CollectManage")
    public AjaxResult CollectManage(@RequestBody CollectManageRequest collectManageRequest) {
        Long userId = null;
        try {
            userId = getUserId();
        }catch (Exception e){
            userId = null;
        }
        
        if (userId!=null){
            Long id = Long.parseLong(collectManageRequest.getAchievementId());
            Map<String, Object> result = iAchievementsService.updateAchievementCollections(id, userId, collectManageRequest.getCollectFlag());
            return success(result);
        }
        return error("收藏操作失败，用户未登录");
    }
    

}
