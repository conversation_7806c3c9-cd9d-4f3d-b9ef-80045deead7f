package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.portal.business.service.IAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@Api("会员账号控制器")
@RequestMapping("/account")
public class AccountController extends BaseController {

    @Autowired
    private IAccountService accountService;

    @GetMapping("/info")
    @ApiOperation("获取账户信息")
    public AjaxResult getAccountInfo() {
        // 获取当前登录用户ID
        Long userId = SecurityUtils.getUserId();
        // 获取账户信息
        PortalUser user = accountService.getInfo(userId);
        return success(user);
    }

    
}
