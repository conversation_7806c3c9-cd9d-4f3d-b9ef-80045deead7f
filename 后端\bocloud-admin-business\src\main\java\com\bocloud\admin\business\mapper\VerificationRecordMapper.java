package com.bocloud.admin.business.mapper;

import com.bocloud.domain.web.VerificationRecord;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 认证记录Mapper接口
 */
@Mapper
public interface VerificationRecordMapper {
    
    /**
     * 根据用户ID查询认证状态
     * 
     * @param userId 用户ID
     * @return 认证状态信息
     */
    VerificationRecord selectVerificationRecordByUserIdAndStatus(@Param("userId")Long userId, @Param("status")String status);
    
    /**
     * 修改认证状态
     * 
     * @param verificationRecord 认证状态信息
     * @return 结果
     */
    int updateVerificationRecord(VerificationRecord verificationRecord);

    /**
     * 查询认证状态列表
     * @param verificationRecord 认证状态信息
     * @return 认证状态列表
     */
    List<VerificationRecord> selectVerificationRecordList(VerificationRecord verificationRecord);
}