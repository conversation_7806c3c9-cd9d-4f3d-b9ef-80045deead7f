package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.JobsMapper;
import com.bocloud.common.utils.DateUtils;
import  com.bocloud.domain.system.platform_service.Jobs;
import com.bocloud.admin.business.service.IJobsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 职位发布Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Service
public class JobsServiceImpl implements IJobsService
{
    @Autowired
    private JobsMapper jobsMapper;

    /**
     * 查询职位发布
     *
     * @param jobId 职位发布主键
     * @return 职位发布
     */
    @Override
    public Jobs selectJobsByJobId(Long jobId)
    {
        return jobsMapper.selectJobsByJobId(jobId);
    }

    /**
     * 查询职位发布列表
     *
     * @param jobs 职位发布
     * @return 职位发布
     */
    @Override
    public List<Jobs> selectJobsList(Jobs jobs)
    {
        return jobsMapper.selectJobsList(jobs);
    }

    /**
     * 新增职位发布
     *
     * @param jobs 职位发布
     * @return 结果
     */
    @Override
    public int insertJobs(Jobs jobs)
    {
        jobs.setCreateTime(DateUtils.getNowDate());
        return jobsMapper.insertJobs(jobs);
    }

    /**
     * 修改职位发布
     *
     * @param jobs 职位发布
     * @return 结果
     */
    @Override
    public int updateJobs(Jobs jobs)
    {
        jobs.setUpdateTime(DateUtils.getNowDate());
        return jobsMapper.updateJobs(jobs);
    }

    /**
     * 批量删除职位发布
     *
     * @param jobIds 需要删除的职位发布主键
     * @return 结果
     */
    @Override
    public int deleteJobsByJobIds(Long[] jobIds)
    {
        return jobsMapper.deleteJobsByJobIds(jobIds);
    }

    /**
     * 删除职位发布信息
     *
     * @param jobId 职位发布主键
     * @return 结果
     */
    @Override
    public int deleteJobsByJobId(Long jobId)
    {
        return jobsMapper.deleteJobsByJobId(jobId);
    }
}