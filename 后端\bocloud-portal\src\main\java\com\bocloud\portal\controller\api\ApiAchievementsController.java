package com.bocloud.portal.controller.api;

import com.bocloud.common.annotation.Anonymous;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.system.achievement.Achievements;
import com.bocloud.domain.web.CollectManageRequest;
import com.bocloud.portal.business.service.IAchievementsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @description 成果展示查询Controller
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/api/achievements")
@Api(tags = "成果展示接口")
public class ApiAchievementsController extends BaseController {
    @Autowired
    private IAchievementsService iAchievementsService;

    /**
     * 获取成果发布列表
     */
    @Anonymous
    @GetMapping("/list")
    @ApiOperation("获取成果展示列表")
    public TableDataInfo list(Achievements achievements) {
        achievements.setStatus("2"); // 只查询发布状态的文章
        startPage();
        List<Achievements> list = iAchievementsService.selectAchievementsList(achievements);
        return getDataTable(list);
    }

    /**
     * 获取成果发布详情
     */
    @Anonymous
    @GetMapping(value = "/{achievementId}")
    @ApiOperation("获取成果展示详情")
    public AjaxResult getInfo(@PathVariable("achievementId") Long achievementId) {
        Long userId = null;
        try {
            userId = getUserId();
        }catch (Exception e){
            userId = null;
        }
        Achievements achievements = iAchievementsService.selectAchievementsByAchievementId(achievementId,userId);
        if (achievements == null) {
            return AjaxResult.error("文章不存在");
        }
        // 检查文章状态是否为发布状态
        if (!Achievements.STATUS_PUBLISH.equals(achievements.getStatus())) {
            return AjaxResult.error("文章未发布或已下架");
        }
        // 更新浏览次数
        iAchievementsService.updateAchievementViews(achievementId);
        return success(achievements);
    }


}
