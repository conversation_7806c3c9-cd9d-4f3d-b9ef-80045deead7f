<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PortalMessageStatusMapper">
    
    <!-- 批量新增消息状态 -->
    <insert id="batchInsertPortalMessageStatus" parameterType="java.util.List">
        insert into portal_message_status(
            message_id, user_id, status, create_time, update_time
        )values
        <foreach collection="list" item="item" separator=",">
            (#{item.messageId}, #{item.userId}, #{item.status}, sysdate(), sysdate())
        </foreach>
    </insert>

    <!-- 查询用户的消息状态 -->
    <select id="selectMessageStatus" resultType="String">
        select status 
        from portal_message_status 
        where message_id = #{messageId} and user_id = #{userId}
    </select>

    <!-- 更新消息状态 -->
    <update id="updateMessageStatus">
        update portal_message_status 
        set status = #{status}, update_time = sysdate()
        where message_id = #{messageId} and user_id = #{userId}
    </update>

    <!-- 插入或更新消息状态 -->
    <insert id="insertOrUpdateMessageStatus">
        insert into portal_message_status(message_id, user_id, status, create_time, update_time)
        values(#{messageId}, #{userId}, #{status}, sysdate(), sysdate())
        on duplicate key update 
        status = #{status}, update_time = sysdate()
    </insert>
</mapper> 