package com.bocloud.portal.business.service.impl;

import com.bocloud.common.constant.Constants;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.enums.VerifyCodeType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.ServletUtils;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.portal.business.service.IVerifyCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
public class VerifyCodeServiceImpl implements IVerifyCodeService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HttpServletRequest request;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 验证码有效期（分钟）
     */
    private static final int CODE_EXPIRE_TIME = 5;

    /**
     * 发送间隔时间（秒）
     */
    private static final int SEND_INTERVAL = 60;

    /**
     * IP限制时间（小时）
     */
    private static final int IP_LIMIT_TIME = 24;

    /**
     * 单个IP最大发送次数
     */
    private static final int MAX_IP_SEND_COUNT = 50;

    /**
     * 单个账号最大发送次数
     */
    private static final int MAX_ACCOUNT_SEND_COUNT = 10;

    /**
     * 手机号正则表达式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    @Override
    public void sendVerifyCode(String account, VerifyCodeType type) {
        if (StringUtils.isEmpty(account)) {
            throw new ServiceException("账号不能为空");
        }

        if (type == null) {
            throw new ServiceException("验证码类型不能为空");
        }

        // 验证账号格式
        if (!isValidAccount(account)) {
            throw new ServiceException("账号格式不正确");
        }

        // 获取客户端IP
        String ip = ServletUtils.getClientIp(request);
        
        // 检查IP发送频率
        String ipRateKey = Constants.VERIFY_CODE_KEY + "ip_rate:" + ip;
        Integer ipSendCount = redisCache.getCacheObject(ipRateKey);
        if (ipSendCount != null && ipSendCount >= MAX_IP_SEND_COUNT) {
            throw new ServiceException("今日发送次数已达上限，请明天再试");
        }

        // 检查账号发送频率
        String accountRateKey = Constants.VERIFY_CODE_KEY + "account_rate:" + account;
        Integer accountSendCount = redisCache.getCacheObject(accountRateKey);
        if (accountSendCount != null && accountSendCount >= MAX_ACCOUNT_SEND_COUNT) {
            throw new ServiceException("该账号今日发送次数已达上限，请明天再试");
        }

        // 检查发送间隔
        String intervalKey = Constants.VERIFY_CODE_KEY + "interval:" + type.getCode() + ":" + account;
        if (redisCache.hasKey(intervalKey)) {
            throw new ServiceException("发送太频繁，请稍后再试");
        }

        // 生成6位随机验证码
        String code = generateRandomCode();
        
        // 构建验证码缓存key
        String verifyKey = Constants.VERIFY_CODE_KEY + type.getCode() + ":" + account;
        
        // 构建验证码信息
        Map<String, Object> verifyCode = new HashMap<>();
        verifyCode.put("account", account);
        verifyCode.put("code", code);
        verifyCode.put("type", type.getCode());
        
        // 存储验证码到Redis，设置5分钟有效期
        redisCache.setCacheObject(verifyKey, verifyCode, CODE_EXPIRE_TIME, TimeUnit.MINUTES);
        
        // 设置发送间隔限制
        redisCache.setCacheObject(intervalKey, "1", SEND_INTERVAL, TimeUnit.SECONDS);
        
        // 更新IP发送次数
        if (ipSendCount == null) {
            ipSendCount = 0;
        }
        redisCache.setCacheObject(ipRateKey, ipSendCount + 1, IP_LIMIT_TIME, TimeUnit.HOURS);
        
        // 更新账号发送次数
        if (accountSendCount == null) {
            accountSendCount = 0;
        }
        redisCache.setCacheObject(accountRateKey, accountSendCount + 1, IP_LIMIT_TIME, TimeUnit.HOURS);
        
        // TODO: 调用短信或邮件服务发送验证码
        log.info("发送验证码: ip={}, account={}, type={}, code={}", ip, account, type.getDesc(), code);
    }

    @Override
    public void verifyCode(String account, String code, VerifyCodeType type) {
        if (StringUtils.isEmpty(account) || StringUtils.isEmpty(code) || type == null) {
            throw new ServiceException("参数不能为空");
        }

        // 验证账号格式
        if (!isValidAccount(account)) {
            throw new ServiceException("账号格式不正确");
        }

        String verifyKey = Constants.VERIFY_CODE_KEY + type.getCode() + ":" + account;
        Map<String, Object> verifyCode = redisCache.getCacheObject(verifyKey);
        
        if (verifyCode == null) {
            throw new ServiceException("验证码已过期");
        }

        String storedCode = (String) verifyCode.get("code");
        if (!code.equals(storedCode)) {
            // 记录验证失败次数
            String failKey = Constants.VERIFY_CODE_KEY + "fail:" + type.getCode() + ":" + account;
            Integer failCount = redisCache.getCacheObject(failKey);
            if (failCount == null) {
                failCount = 0;
            }
            redisCache.setCacheObject(failKey, failCount + 1, 1, TimeUnit.HOURS);
            
            if (failCount >= 5) {
                // 验证失败次数过多，删除验证码
                redisCache.deleteObject(verifyKey);
                throw new ServiceException("验证失败次数过多，请重新获取验证码");
            }
            
            throw new ServiceException("验证码错误");
        }

        // 验证成功后删除验证码
        redisCache.deleteObject(verifyKey);
    }

    /**
     * 检查验证码是否存在且未过期
     */
    public boolean hasValidCode(String account, VerifyCodeType type) {
        if (StringUtils.isEmpty(account) || type == null) {
            return false;
        }

        String verifyKey = Constants.VERIFY_CODE_KEY + type.getCode() + ":" + account;
        return redisCache.hasKey(verifyKey);
    }

    /**
     * 生成6位随机验证码
     * 开发环境返回固定验证码 123456
     */
    private String generateRandomCode() {
        // 开发环境返回固定验证码
        if ("dev".equals(activeProfile)) {
            log.info("开发环境，使用固定验证码: 123456");
            return "123456";
        }
        
        // 生产环境生成随机验证码
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 验证账号格式（手机号或邮箱）
     */
    private boolean isValidAccount(String account) {
        return PHONE_PATTERN.matcher(account).matches() || 
               EMAIL_PATTERN.matcher(account).matches();
    }
} 