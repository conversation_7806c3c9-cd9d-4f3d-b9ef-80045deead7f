package com.bocloud.portal.business.service.impl;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.UserType;
import com.bocloud.common.enums.InfoStatus;
import com.bocloud.common.enums.UserStatus;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.RegisterUser;
import com.bocloud.domain.web.CompleteRegisterInfo;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.ExpertPaper;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.portal.business.mapper.*;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.IRegisterService;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import com.bocloud.portal.business.service.ISmsService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * 注册校验方法
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RegisterService implements IRegisterService
{
    @Autowired
    private IPortalUserService userService;

    @Autowired
    private PortalUserMapper portalUserMapper;
    
    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertPaperMapper expertPaperMapper;

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    private IPortalUserPersonalInfoService portalUserPersonalInfoService;

    @Autowired
    private ISmsService smsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult smsRegister(RegisterUser registerUser){
        try {
            PortalUser checkUser = userService.selectUserByPhone(registerUser.getPhone());
            if(!ObjectUtils.isEmpty(checkUser)){
                return AjaxResult.error("手机号已注册");
            }
            if(StringUtils.isEmpty(registerUser.getPhone())){
                return AjaxResult.error("手机号不能为空");
            }
            if(StringUtils.isEmpty(registerUser.getCode())){
                return AjaxResult.error("验证码不能为空");
            }
            smsService.checkSmsCode(registerUser.getPhone(),registerUser.getCode(),registerUser.getUuid());
            int regFlag = userService.registerUser(registerUser);
            if (regFlag < 1)
            {
                 return AjaxResult.error("注册失败,请联系系统管理人员");
            }
        }catch (Exception e){
            //AsyncManager.me().execute(AsyncFactory.recordLogininfor(registerUser.getPhone(), Constants.REGISTER, MessageUtils.message("user.register.success")));
            log.error("手机号注册异常:{}",e);
            throw new ServiceException("手机号注册失败！"+e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 完善注册信息
     * 
     * @param completeRegisterInfo 完善注册信息对象
     * @throws ServiceException 如果用户未登录或处理过程中发生错误
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeRegisterInfo(CompleteRegisterInfo completeRegisterInfo) {
        // 获取当前登录用户
        Long userId = SecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }

        // 检查用户信息完成状态
        PortalUser currentUser = portalUserMapper.selectPortalUserById(userId);
        if (currentUser == null) {
            throw new ServiceException("用户不存在");
        }
        
        // 如果信息已经是完成状态，不允许再次完善信息
        if (InfoStatus.NORMAL.getCode().equals(currentUser.getInfoStatus())) {
            throw new ServiceException("用户信息已完成，如需修改请通过变更认证信息进行");
        }

        // 根据账号类型处理不同的信息
        if (UserType.PERSONAL.getCode().equals(completeRegisterInfo.getAccountType())) {
            // 处理个人账号信息
            PersonalInfo personalInfo = completeRegisterInfo.getPersonalInfo();
            if (personalInfo != null) {
                // 设置用户基本信息
                personalInfo.setUserId(userId);
                personalInfo.setCreateBy(SecurityUtils.getUsername());
                
                // 检查是否已存在个人信息
                PersonalInfo existingInfo = portalUserPersonalInfoService.selectBestPersonalInfoByUserId(userId);
                if (existingInfo != null) {
                    // 更新个人信息
                    personalInfo.setId(existingInfo.getId());
                    portalUserPersonalInfoService.updatePortalUserPersonalInfo(personalInfo);
                } else {
                    // 新增个人信息
                    portalUserPersonalInfoService.insertPortalUserPersonalInfo(personalInfo);
                }

                // 更新用户信息状态
                PortalUser user = new PortalUser();
                user.setId(userId);
                user.setType(UserType.PERSONAL.getCode()); // 设置用户类型为个人用户
                user.setStatus(UserStatus.OK.getCode()); // 设置用户状态为正常
                user.setInfoStatus(InfoStatus.PENDING_REVIEW.getCode()); // 设置信息完善状态为待审核
                user.setUpdateBy(SecurityUtils.getUsername());
                user.setUpdateTime(new Date());
                portalUserMapper.updatePortalUser(user);

                // 如果是专家，保存专家信息
                if ("1".equals(personalInfo.getIsExpert()) && completeRegisterInfo.getExpertInfo() != null) {
                    ExpertInfo expertInfo = completeRegisterInfo.getExpertInfo();
                    
                    // 设置用户ID
                    expertInfo.setUserId(userId);
                    expertInfo.setStatus("0");
                    user.setType("3"); // 设置用户类型为专家用户
                    // 设置删除标志
                    expertInfo.setDelFlag("0");
                    
                    // 设置专家姓名
                    expertInfo.setExpertName(personalInfo.getRealName());
                    
                    // 保存专家信息
                    expertInfoMapper.insertExpertInfo(expertInfo);
                    
                    // 保存论文信息
                    if (expertInfo.getExpertPapers() != null && !expertInfo.getExpertPapers().isEmpty()) {
                        // 先删除该专家的所有论文
                        ExpertPaper query = new ExpertPaper();
                        query.setExpertId(expertInfo.getExpertId());
                        List<ExpertPaper> existingPapers = expertPaperMapper.selectExpertPaperList(query);
                        if (!existingPapers.isEmpty()) {
                            Long[] paperIds = existingPapers.stream()
                                .map(ExpertPaper::getPaperId)
                                .toArray(Long[]::new);
                            expertPaperMapper.deleteExpertPaperByIds(paperIds);
                        }

                        // 保存新的论文信息
                        expertPaperMapper.insertExpertPapers(expertInfo.getExpertPapers());
                    }
                }

            }
        } else if (UserType.ENTERPRISE.getCode().equals(completeRegisterInfo.getAccountType())) {
            // 处理企业账户信息
            PortalUser user = new PortalUser();
            user.setId(userId);
            user.setType(UserType.ENTERPRISE.getCode()); // 设置用户类型为企业用户
            user.setInfoStatus(InfoStatus.PENDING_REVIEW.getCode()); // 设置信息完善状态为待审核
            user.setUpdateBy(SecurityUtils.getUsername());
            user.setUpdateTime(new Date());
            portalUserMapper.updatePortalUser(user);
            
            // 保存企业信息
            if (completeRegisterInfo.getEnterpriseInfo() != null) {
                EnterpriseInfo enterpriseInfo = completeRegisterInfo.getEnterpriseInfo();
                enterpriseInfo.setUserId(userId);
                enterpriseInfo.setDelFlag("0");
                enterpriseInfo.setStatus("0");
                enterpriseInfo.setCreateBy(SecurityUtils.getUsername());
                enterpriseInfo.setCreateTime(new Date());
                enterpriseInfo.setUpdateBy(SecurityUtils.getUsername());
                enterpriseInfo.setUpdateTime(new Date());
                
                // 检查是否已存在企业信息
                EnterpriseInfo existingInfo = enterpriseInfoMapper.selectEnterpriseInfoByUserId(userId);
                if (existingInfo != null) {
                    enterpriseInfo.setEnterpriseId(existingInfo.getEnterpriseId());
                    enterpriseInfoMapper.updateEnterpriseInfo(enterpriseInfo);
                } else {
                    enterpriseInfoMapper.insertEnterpriseInfo(enterpriseInfo);
                }
            }
        }
    }
}
