package com.bocloud.portal.business.service.impl;

import com.bocloud.domain.system.CommonCollection;
import com.bocloud.portal.business.mapper.CommonCollectionMapper;
import com.bocloud.portal.business.service.ICommonCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用收藏业务处理层
 */
@Service
public class CommonCollectionServiceImpl implements ICommonCollectionService {

    @Autowired
    private CommonCollectionMapper commonCollectionMapper;

    @Override
    public List<CommonCollection> selectCollectionList(CommonCollection commonCollection) {
        return commonCollectionMapper.selectCollectionList(commonCollection);

    }

    @Override
    public int insertCommonCollection(CommonCollection commonCollection) {
        return commonCollectionMapper.insertCommonCollection(commonCollection);
    }

    @Override
    public int deleteCommonCollectionByBussiness(CommonCollection commonCollection) {
        return commonCollectionMapper.deleteCommonCollectionByBussiness(commonCollection);
    }
}
