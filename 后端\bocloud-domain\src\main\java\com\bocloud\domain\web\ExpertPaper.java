package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 专家论文实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpertPaper extends BaseEntity {
    /** 论文ID */
    private Long paperId;

    /** 专家ID */
    private Long expertId;

    /** 用户ID */
    private Long userId;

    /** 论文标题 */
    private String title;

    /** 发表期刊 */
    private String journal;

    /** 发表日期 */
    private String publishDate;

    /** DOI编号 */
    private String doi;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;
} 