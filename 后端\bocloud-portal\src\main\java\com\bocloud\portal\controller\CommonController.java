package com.bocloud.portal.controller;

import com.bocloud.common.config.RuoYiConfig;
import com.bocloud.common.constant.Constants;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.common.utils.file.FileUploadUtils;
import com.bocloud.common.utils.file.FileUtils;
import com.bocloud.framework.config.ServerConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 通用请求处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
@Api("上传下载")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     * 
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @ApiOperation("通用下载请求")
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            log.info("[fileDownload] 开始下载文件，fileName: {}", fileName);
            
            if (StringUtils.isEmpty(fileName))
            {
                log.error("[fileDownload] 文件名为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            if (!FileUtils.checkAllowDownload(fileName))
            {
                log.error("[fileDownload] 文件名称({})非法，不允许下载", fileName);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            
            // 构建文件路径 - 支持多种路径格式
            String filePath;
            String realFileName;
            
            // 1. 如果是完整的profile路径（如：/profile/upload/2024/01/01/filename_12345.ext）
            if (fileName.startsWith(Constants.RESOURCE_PREFIX)) {
                String localPath = RuoYiConfig.getProfile();
                filePath = localPath + StringUtils.substringAfter(fileName, Constants.RESOURCE_PREFIX);
                realFileName = StringUtils.substringAfterLast(filePath, "/");
                log.info("[fileDownload] 使用profile路径，filePath: {}", filePath);
            }
            // 2. 如果是相对路径（如：upload/2024/01/01/filename_12345.ext）
            else if (fileName.startsWith("upload/") || fileName.startsWith("download/")) {
                filePath = RuoYiConfig.getProfile() + "/" + fileName;
                realFileName = StringUtils.substringAfterLast(filePath, "/");
                log.info("[fileDownload] 使用相对路径，filePath: {}", filePath);
            }
            // 3. 如果是纯文件名，默认从download目录查找
            else {
                filePath = RuoYiConfig.getDownloadPath() + fileName;
                // 生成下载文件名
                try {
                    if (fileName.contains("_")) {
                        realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
                    } else {
                        realFileName = fileName;
                    }
                } catch (Exception e) {
                    log.warn("[fileDownload] 文件名处理异常，使用原文件名: {}", e.getMessage());
                    realFileName = fileName;
                }
                log.info("[fileDownload] 使用download路径，filePath: {}", filePath);
            }
            
            log.info("[fileDownload] 最终文件路径: {}", filePath);
            
            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists())
            {
                log.error("[fileDownload] 文件不存在: {}", filePath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            if (!file.isFile())
            {
                log.error("[fileDownload] 路径不是文件: {}", filePath);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            log.info("[fileDownload] 下载文件名: {}", realFileName);

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            
            log.info("[fileDownload] 文件下载成功: {}", fileName);
            
            if (delete != null && delete)
            {
                boolean deleted = FileUtils.deleteFile(filePath);
                log.info("[fileDownload] 文件删除结果: {}", deleted);
            }
        }
        catch (Exception e)
        {
            log.error("[fileDownload] 下载文件失败，fileName: {}", fileName, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (Exception ex) {
                log.error("[fileDownload] 设置响应状态失败", ex);
            }
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @ApiOperation("通用上传请求")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String relativePath = FileUploadUtils.upload(filePath, file);
            String fullUrl = serverConfig.getUrl() + relativePath;

            // 处理原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = ""; // 防止空指针异常
            }
            String fileNameWithoutExtension = originalFilename;
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0) { // 确保扩展名有效（如避免处理类似 ".gitignore" 的情况）
                fileNameWithoutExtension = originalFilename.substring(0, lastDotIndex);
                fileExtension = originalFilename.substring(lastDotIndex + 1);
            }

            AjaxResult ajax = AjaxResult.success();
            ajax.put("fullUrl", fullUrl);
            ajax.put("relativePath", relativePath);
            ajax.put("newFileName", FileUtils.getName(fullUrl));
            ajax.put("originalFilename", fileNameWithoutExtension); // 不包含扩展名
            ajax.put("fileExtension", fileExtension); // 新增扩展名字段
            ajax.put("fileSize", file.getSize()); // 文件大小
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 通用上传请求（多个）
     */
    @ApiOperation("通用上传请求多个")
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @ApiOperation("本地资源通用下载")
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            log.info("[resourceDownload] 开始下载资源，resource: {}", resource);
            
            if (StringUtils.isEmpty(resource))
            {
                log.error("[resourceDownload] 资源路径为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            if (!FileUtils.checkAllowDownload(resource))
            {
                log.error("[resourceDownload] 资源文件({})非法，不允许下载", resource);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            log.info("[resourceDownload] 下载路径: {}", downloadPath);
            
            // 检查文件是否存在
            File file = new File(downloadPath);
            if (!file.exists())
            {
                log.error("[resourceDownload] 文件不存在: {}", downloadPath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            if (!file.isFile())
            {
                log.error("[resourceDownload] 路径不是文件: {}", downloadPath);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            log.info("[resourceDownload] 下载文件名: {}", downloadName);
            
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
            
            log.info("[resourceDownload] 资源下载成功: {}", resource);
        }
        catch (Exception e)
        {
            log.error("[resourceDownload] 下载文件失败，resource: {}", resource, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (Exception ex) {
                log.error("[resourceDownload] 设置响应状态失败", ex);
            }
        }
    }

    /**
     * 下载上传的文件
     */
    @ApiOperation("下载上传的文件")
    @GetMapping("/file/download")
    public void downloadUploadFile(String filePath, HttpServletResponse response, HttpServletRequest request)
            throws Exception
    {
        try
        {
            log.info("[downloadUploadFile] 开始下载上传文件，filePath: {}", filePath);
            
            if (StringUtils.isEmpty(filePath))
            {
                log.error("[downloadUploadFile] 文件路径为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            // URL解码处理
            String decodedFilePath;
            try {
                decodedFilePath = java.net.URLDecoder.decode(filePath, "UTF-8");
                log.info("[downloadUploadFile] URL解码后，filePath: {}", decodedFilePath);
            } catch (Exception e) {
                log.warn("[downloadUploadFile] URL解码失败，使用原始路径: {}", e.getMessage());
                decodedFilePath = filePath;
            }
            
            // 构建完整的文件路径
            String fullPath;
            String downloadFileName;
            
            // 如果filePath是上传方法返回的relativePath格式（如：/profile/upload/2024/01/01/filename_12345.ext）
            if (decodedFilePath.startsWith(Constants.RESOURCE_PREFIX)) {
                String localPath = RuoYiConfig.getProfile();
                fullPath = localPath + StringUtils.substringAfter(decodedFilePath, Constants.RESOURCE_PREFIX);
                downloadFileName = StringUtils.substringAfterLast(decodedFilePath, "/");
                log.info("[downloadUploadFile] 使用relativePath格式，fullPath: {}", fullPath);
            }
            // 如果是相对路径（如：upload/2024/01/01/filename_12345.ext）
            else if (decodedFilePath.startsWith("upload/")) {
                fullPath = RuoYiConfig.getProfile() + "/" + decodedFilePath;
                downloadFileName = StringUtils.substringAfterLast(decodedFilePath, "/");
                log.info("[downloadUploadFile] 使用相对路径格式，fullPath: {}", fullPath);
            }
            // 其他格式，尝试直接使用
            else {
                fullPath = RuoYiConfig.getProfile() + "/" + decodedFilePath;
                downloadFileName = StringUtils.substringAfterLast(decodedFilePath, "/");
                log.info("[downloadUploadFile] 使用其他格式，fullPath: {}", fullPath);
            }
            
            log.info("[downloadUploadFile] 最终文件路径: {}", fullPath);
            
            // 检查文件是否存在
            File file = new File(fullPath);
            if (!file.exists())
            {
                log.error("[downloadUploadFile] 文件不存在: {}", fullPath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            if (!file.isFile())
            {
                log.error("[downloadUploadFile] 路径不是文件: {}", fullPath);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }
            
            // 检查文件是否可下载
            if (!FileUtils.checkAllowDownload(downloadFileName))
            {
                log.error("[downloadUploadFile] 文件类型不允许下载: {}", downloadFileName);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            
            log.info("[downloadUploadFile] 下载文件名: {}", downloadFileName);
            
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadFileName);
            FileUtils.writeBytes(fullPath, response.getOutputStream());
            
            log.info("[downloadUploadFile] 文件下载成功: {}", decodedFilePath);
        }
        catch (Exception e)
        {
            log.error("[downloadUploadFile] 下载文件失败，filePath: {}", filePath, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (Exception ex) {
                log.error("[downloadUploadFile] 设置响应状态失败", ex);
            }
        }
    }

    /**
     * 通用上传请求（单个）- 基于UUID的版本，用于微信端
     */
    @ApiOperation("通用上传请求（UUID版本）")
    @PostMapping("/uploadWithUuid")
    public AjaxResult uploadFileWithUuid(MultipartFile file, @RequestParam(value = "originalFilename", required = false) String userOriginalFilename) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String relativePath = FileUploadUtils.upload(filePath, file);
            String fullUrl = serverConfig.getUrl() + relativePath;

            // 处理原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = ""; // 防止空指针异常
            }
            // 如果微信端传递了原始文件名，优先使用微信端传递的
            if (userOriginalFilename != null && !userOriginalFilename.isEmpty()) {
                originalFilename = userOriginalFilename;
                log.info("[uploadFileWithUuid] 使用微信端传递的原始文件名: {}", originalFilename);
            } else {
                log.info("[uploadFileWithUuid] 使用系统获取的原始文件名: {}", originalFilename);
            }
            String fileNameWithoutExtension = originalFilename;
            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0) { // 确保扩展名有效（如避免处理类似 ".gitignore" 的情况）
                fileNameWithoutExtension = originalFilename.substring(0, lastDotIndex);
                fileExtension = originalFilename.substring(lastDotIndex + 1);
            }

            AjaxResult ajax = AjaxResult.success();
            ajax.put("fullUrl", fullUrl);
            ajax.put("relativePath", relativePath);
            ajax.put("newFileName", FileUtils.getName(fullUrl));
            ajax.put("originalFilename", fileNameWithoutExtension); // 不包含扩展名
            ajax.put("fileExtension", fileExtension); // 新增扩展名字段
            ajax.put("fileSize", file.getSize()); // 文件大小

            // 打印ajax
            log.info("[uploadFileWithUuid] 上传成功，ajax: {}", ajax);

            // 生成UUID
            String uploadId = UUID.randomUUID().toString();
            
            // 将完整结果存入Redis，设置5分钟过期
            String redisKey = "upload:result:" + uploadId;
            stringRedisTemplate.opsForValue().set(redisKey, objectMapper.writeValueAsString(ajax), 5, TimeUnit.MINUTES);

            // 从redis中获取上传结果
            String json = stringRedisTemplate.opsForValue().get(redisKey);
            log.info("[uploadFileWithUuid] 从redis中获取上传结果，redisKey: {}, json: {}", redisKey, json);
            
            // 只返回UUID
            AjaxResult result = AjaxResult.success();
            result.put("uploadId", uploadId);
            return result;
        } catch (Exception e) {
            log.error("[uploadFileWithUuid] 上传失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据UUID获取上传结果
     */
    @ApiOperation("根据UUID获取上传结果")
    @GetMapping("/upload/result")
    public AjaxResult getUploadResult(@RequestParam("uploadId") String uploadId) {
        try {
            String redisKey = "upload:result:" + uploadId;
            String json = stringRedisTemplate.opsForValue().get(redisKey);
            
            if (json == null) {
                log.warn("[getUploadResult] 上传结果不存在或已过期，uploadId: {}", uploadId);
                return AjaxResult.error("上传结果不存在或已过期");
            }
            
            // 反序列化为AjaxResult
            AjaxResult result = objectMapper.readValue(json, AjaxResult.class);
            log.info("[getUploadResult] 成功获取上传结果，uploadId: {}, result: {}", uploadId, result);
            return result;
        } catch (Exception e) {
            log.error("[getUploadResult] 获取上传结果失败，uploadId: {}", uploadId, e);
            return AjaxResult.error("获取上传结果失败: " + e.getMessage());
        }
    }
}
