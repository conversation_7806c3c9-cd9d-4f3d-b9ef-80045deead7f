package com.bocloud.admin.business.service.impl;

import java.util.Date;
import java.util.List;

import com.bocloud.admin.business.mapper.TestApplicationMapper;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.domain.system.test_application.TestApplication;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.admin.business.service.ITestApplicationService;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.admin.business.service.IPortalMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bocloud.domain.system.test_application.TestApplicationAuditRequest;
import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.enums.PortalMessageType;

/**
 * 检测申请Service业务层处理
 *
 * @date 2025-03-12
 */
@Service
public class TestApplicationServiceImpl implements ITestApplicationService
{
    @Autowired
    private TestApplicationMapper testApplicationMapper;

    @Autowired
    private ISysAuditService sysAuditService;

    @Autowired
    private IPortalMessageService portalMessageService;

    @Autowired
    private ICommonAttachmentService attachmentService;

    /**
     * 查询检测申请
     *
     * @param id 检测申请主键
     * @return 检测申请
     */
    @Override
    public TestApplication selectTestApplicationById(Long id)
    {
        TestApplication testApplication = testApplicationMapper.selectTestApplicationById(id);
        if (testApplication != null) {
            // 查询附件列表
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(testApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
            testApplication.setAttachments(attachments);

            // 查询报告列表
            query = new CommonAttachment();
            query.setBusinessId(testApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_REPORT);
            List<CommonAttachment> reports = attachmentService.selectCommonAttachmentList(query);
            testApplication.setReports(reports);
        }
        return testApplication;
    }

    /**
     * 查询检测申请列表
     *
     * @param testApplication 检测申请
     * @return 检测申请
     */
    @Override
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication)
    {
        return testApplicationMapper.selectTestApplicationList(testApplication);
    }

    /**
     * 新增检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    @Override
    public int insertTestApplication(TestApplication testApplication)
    {
        return testApplicationMapper.insertTestApplication(testApplication);
    }

    /**
     * 修改检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    @Override
    public int updateTestApplication(TestApplication testApplication)
    {
        return testApplicationMapper.updateTestApplication(testApplication);
    }

    /**
     * 批量删除检测申请
     *
     * @param ids 需要删除的检测申请主键
     * @return 结果
     */
    @Override
    public int deleteTestApplicationByIds(Long[] ids)
    {
        return testApplicationMapper.deleteTestApplicationByIds(ids);
    }

    /**
     * 删除检测申请信息
     *
     * @param id 检测申请主键
     * @return 结果
     */
    @Override
    public int deleteTestApplicationById(Long id)
    {
        return testApplicationMapper.deleteTestApplicationById(id);
    }

    @Override
    @Transactional
    public int auditPayment(TestApplication testApplication) {
        // 验证申请是否存在
        TestApplication oldApplication = testApplicationMapper.selectTestApplicationById(testApplication.getId());
        if (oldApplication == null) {
            throw new ServiceException("申请不存在");
        }

        // 验证付款状态是否为已上传凭证
        if (!TestApplication.PAYMENT_STATUS_UPLOADED.equals(oldApplication.getPaymentStatus())) {
            throw new ServiceException("当前付款状态不允许审核");
        }

        // 验证审核结果
        if (testApplication.getPaymentStatus() == null) {
            throw new ServiceException("请选择审核结果");
        }

        // 设置审核结果
        oldApplication.setPaymentStatus(testApplication.getPaymentStatus());
        oldApplication.setAuditPaymentDate(DateUtils.getNowDate());
        oldApplication.setReviewComment(testApplication.getReviewComment());
        oldApplication.setUpdateBy(SecurityUtils.getUsername());

        // 如果确认收款，同时更新主状态为已付款
        if (TestApplication.PAYMENT_STATUS_CONFIRMED.equals(testApplication.getPaymentStatus())) {
            oldApplication.setStatus(TestApplication.STATUS_PAID);
        }

        int rows = testApplicationMapper.updateTestApplication(oldApplication);

        // 如果拒绝，发送站内消息
        if (TestApplication.PAYMENT_STATUS_REJECTED.equals(testApplication.getPaymentStatus())) {
            // 发送站内消息
            PortalMessage portalMessage = new PortalMessage();
            portalMessage.setReceiverId(oldApplication.getPortalUserId());
            portalMessage.setType(PortalMessageType.SYSTEM.getCode());
            portalMessage.setTitle("付款凭证审核拒绝");
            portalMessage.setContent(testApplication.getPaymentRejectReason());
            portalMessageService.insertPortalMessage(portalMessage);
        }
        return rows;
    }

    /**
     * 审核检测申请
     *
     * @param request 审核请求信息
     * @return 结果
     */
    @Override
    @Transactional
    public int auditTestApplication(TestApplicationAuditRequest request)
    {
        // 获取原申请信息
        TestApplication originalApplication = testApplicationMapper.selectTestApplicationById(request.getTestApplicationId());
        if (originalApplication == null) {
            throw new RuntimeException("申请不存在");
        }

        // 更新申请状态
        TestApplication testApplication = new TestApplication();
        testApplication.setId(request.getTestApplicationId());
        testApplication.setStatus(request.getAuditResult());
        testApplication.setReviewComment(request.getReviewComment());
        testApplication.setAdminPrice(request.getAdminPrice());
        testApplication.setAdminPriceRemark(request.getAdminPriceRemark());
        
        int rows = testApplicationMapper.updateTestApplication(testApplication);
        if (rows <= 0) {
            throw new ServiceException("更新申请状态失败");
        }

        // 创建审核记录
        SysAudit sysAudit = new SysAudit();
        sysAudit.setBusinessType("TEST_APPLICATION");
        sysAudit.setBusinessId(request.getTestApplicationId());
        sysAudit.setAuditType("0");  // 审核类型：初审
        sysAudit.setFromStatus(originalApplication.getStatus());
        sysAudit.setToStatus(request.getAuditResult());
        sysAudit.setAuditResult(request.getAuditResult());
        sysAudit.setReviewComment(request.getReviewComment());
        sysAudit.setAuditUserId(SecurityUtils.getUserId());
        sysAudit.setAuditUserName(SecurityUtils.getUsername());
        sysAudit.setAuditTime(new Date());
        sysAudit.setCreateTime(new Date());

        rows = sysAuditService.insertSysAudit(sysAudit);
        if (rows <= 0) {
            throw new ServiceException("记录审核信息失败");
        }

        return rows;
    }
}
