package com.bocloud.portal.business.service.impl;

import java.util.List;

import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.RegisterUser;
import com.bocloud.common.constant.UserConstants;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.portal.business.mapper.PortalUserMapper;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.UserType;
import com.bocloud.common.enums.AccountStatus;
import com.bocloud.common.enums.InfoStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.common.enums.VerificationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 门户用户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class PortalUserServiceImpl implements IPortalUserService
{
    @Autowired
    private PortalUserMapper portalUserMapper;

    /**
     * 查询门户用户信息
     * 
     * @param userId 门户用户信息主键
     * @return 门户用户信息
     */
    @Override
    public PortalUser selectPortalUserById(Long id)
    {
        return portalUserMapper.selectPortalUserById(id);
    }

    /**
     * 查询门户用户信息列表
     * 
     * @param portalUser 门户用户信息
     * @return 门户用户信息
     */
    @Override
    public List<PortalUser> selectPortalUserList(PortalUser portalUser)
    {
        return portalUserMapper.selectPortalUserList(portalUser);
    }

    @Override
    public PortalUser selectUserByPhone(String phone) {
        return portalUserMapper.selectPortalUserByPhone(phone);
    }

    @Override
    public PortalUser selectUserByUserName(String userName) {
        return portalUserMapper.selectUserByUserName(userName);
    }

    @Override
    public int registerUser(RegisterUser registerUser) {
        return portalUserMapper.insertPortalUser(registerUser);
    }

    /**
     * 修改门户用户信息
     * 
     * @param portalUser 门户用户信息
     * @return 结果
     */
    @Override
    public int updatePortalUser(PortalUser portalUser)
    {
        portalUser.setUpdateTime(DateUtils.getNowDate());
        return portalUserMapper.updatePortalUser(portalUser);
    }

    public int resetPassword(PortalUser portalUser){
        portalUser.setUpdateTime(DateUtils.getNowDate());
        return portalUserMapper.resetPassword(portalUser);
    }

    @Override
    public int insertPortalUser(PortalUser portalUser) {
        return portalUserMapper.insertPortalUser(portalUser);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(PortalUser user)
    {
        Long userId = StringUtils.isNull(user.getId()) ? -1L : user.getId();
        PortalUser info = portalUserMapper.checkUserNameUnique(user.getUsername());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public PortalUser selectUserByWxOpenId(String openId) {
        return portalUserMapper.selectUserByWxOpenId(openId);
    }

    @Override
    public PortalUser selectUserById(Long userId) {
        return portalUserMapper.selectUserById(userId);
    }

    @Override
    public PortalUser bindPhoneAndWechatOpenId(String phone, String openId) {
        PortalUser openIdUser = selectUserByWxOpenId(openId);
        PortalUser phoneUser = selectUserByPhone(phone);

        if (openIdUser != null && phoneUser != null) {
            if (openIdUser.getId().equals(phoneUser.getId())) {
                // 已是同一用户，直接返回
                return phoneUser;
            } else {
                // 合并账号：保留手机号用户，但合并微信用户的信息
                phoneUser.setWechatOpenid(openId);
                phoneUser.setWechatUnionid(openIdUser.getWechatUnionid());
                if (StringUtils.isEmpty(phoneUser.getNickname()) && StringUtils.isNotEmpty(openIdUser.getNickname())) {
                    phoneUser.setNickname(openIdUser.getNickname());
                }
                if (StringUtils.isEmpty(phoneUser.getAvatar()) && StringUtils.isNotEmpty(openIdUser.getAvatar())) {
                    phoneUser.setAvatar(openIdUser.getAvatar());
                }
                updatePortalUser(phoneUser);
                // 逻辑删除 openId 用户
                openIdUser.setDelFlag(CommonStatus.DEL_FLAG_DELETED.getCode());
                updatePortalUser(openIdUser);
                // 合并后，始终返回保留的手机号用户对象（重新查一次，确保最新）
                return selectUserByPhone(phone);
            }
        } else if (openIdUser != null) {
            // openId 用户存在，手机号用户不存在，补充手机号
            openIdUser.setPhone(phone);
            openIdUser.setUsername(phone); // 可选：用手机号做用户名
            updatePortalUser(openIdUser);
            return openIdUser;
        } else if (phoneUser != null) {
            // openId 用户不存在，手机号用户存在，绑定 openId
            phoneUser.setWechatOpenid(openId);
            updatePortalUser(phoneUser);
            return phoneUser;
        } else {
            // 都不存在，新建
            PortalUser portalUser = new PortalUser();
            portalUser.setUsername(phone);
            portalUser.setPhone(phone);
            portalUser.setWechatOpenid(openId);
                    portalUser.setType(UserType.PERSONAL.getCode());
        portalUser.setStatus(AccountStatus.NORMAL.getCode());
        portalUser.setInfoStatus(InfoStatus.INCOMPLETE.getCode());
        portalUser.setVerificationType(VerificationType.PERSONAL.getCode());
        portalUser.setVerificationStatus(VerificationStatus.PENDING.getCode());
            portalUser.setCreateBy("wx-web");
            portalUser.setCreateTime(DateUtils.getNowDate());
            portalUser.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode());
            insertPortalUser(portalUser);
            return portalUser;
        }
    }
}
