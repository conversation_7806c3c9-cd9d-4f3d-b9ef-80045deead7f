package com.bocloud.portal.business.service.impl;

import com.bocloud.common.enums.UserType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import com.bocloud.portal.business.service.IExpertInfoService;
import com.bocloud.portal.business.service.IEnterpriseInfoService;
import com.bocloud.portal.business.mapper.PortalUserMapper;
import com.bocloud.portal.business.service.IAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 账户管理服务实现
 */
@Service
public class AccountServiceImpl implements IAccountService {

    @Autowired
    private PortalUserMapper portalUserMapper;

    @Autowired
    private IPortalUserPersonalInfoService portalUserPersonalInfoService;

    @Autowired
    private IExpertInfoService expertInfoService;

    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;

    @Override
    public PortalUser getInfo(Long userId) {
        // 获取用户基本信息
        PortalUser user = portalUserMapper.selectPortalUserById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        
        // 根据用户类型获取详细信息
        String type = user.getType();
        if (UserType.PERSONAL.getCode().equals(type)) {
            // 个人用户，获取个人信息和专家信息
            PersonalInfo personalInfo = portalUserPersonalInfoService.selectBestPersonalInfoByUserId(userId);
            user.setPersonalInfo(personalInfo);
            if (personalInfo != null && "1".equals(personalInfo.getIsExpert())) {
                ExpertInfo expertInfo = expertInfoService.getExpertDetailByUserId(userId);
                user.setExpertInfo(expertInfo);
            }
        } else if (UserType.ENTERPRISE.getCode().equals(type)) {
            // 企业用户，获取企业信息
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getEnterpriseInfo(userId);
            user.setEnterpriseInfo(enterpriseInfo);
        }
        
        return user;
    }
}