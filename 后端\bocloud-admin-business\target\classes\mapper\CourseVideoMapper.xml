<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.CourseVideoMapper">
    
    <resultMap type="CourseVideo" id="CourseVideoResult">
        <result property="videoId"    column="video_id"    />
        <result property="title"      column="title"      />
        <result property="duration"   column="duration"   />
        <result property="videoUrl"   column="video_url"  />
        <result property="uploadTime" column="upload_time"/>
        <result property="chapter"    column="chapter"    />
        <result property="courseId"   column="course_id"  />
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCourseVideoVo">
        select video_id, title, duration, video_url, upload_time, chapter, course_id, create_time, update_time
        from course_video
    </sql>

    <select id="selectCourseVideoList" parameterType="CourseVideo" resultMap="CourseVideoResult">
        <include refid="selectCourseVideoVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
            <if test="chapter != null  and chapter != ''"> and chapter = #{chapter}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
        </where>
        order by chapter, upload_time
    </select>
    
    <select id="selectCourseVideoByVideoId" parameterType="Integer" resultMap="CourseVideoResult">
        <include refid="selectCourseVideoVo"/>
        where video_id = #{videoId}
    </select>
        
    <insert id="insertCourseVideo" parameterType="CourseVideo" useGeneratedKeys="true" keyProperty="videoId">
        insert into course_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="duration != null">duration,</if>
            <if test="videoUrl != null and videoUrl != ''">video_url,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="chapter != null">chapter,</if>
            <if test="courseId != null">course_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="duration != null">#{duration},</if>
            <if test="videoUrl != null and videoUrl != ''">#{videoUrl},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="chapter != null">#{chapter},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseVideo" parameterType="CourseVideo">
        update course_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="videoUrl != null and videoUrl != ''">video_url = #{videoUrl},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="chapter != null">chapter = #{chapter},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where video_id = #{videoId}
    </update>

    <delete id="deleteCourseVideoByVideoId" parameterType="Integer">
        delete from course_video where video_id = #{videoId}
    </delete>

    <delete id="deleteCourseVideoByVideoIds" parameterType="String">
        delete from course_video where video_id in 
        <foreach item="videoId" collection="array" open="(" separator="," close=")">
            #{videoId}
        </foreach>
    </delete>
</mapper> 