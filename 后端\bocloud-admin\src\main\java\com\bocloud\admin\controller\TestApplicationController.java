package com.bocloud.admin.controller;


import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.poi.ExcelUtil;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.admin.business.service.ITestApplicationService;
import com.bocloud.domain.system.test_application.TestApplication;
import com.bocloud.domain.system.test_application.TestApplicationAuditRequest;
import com.bocloud.domain.system.test_application.TestApplicationConfirmPaymentRequest;
import com.bocloud.domain.system.test_application.TestApplicationPriceRequest;
import com.bocloud.domain.system.test_application.TestApplicationRejectPaymentRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 检测申请Controller
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@RestController
@RequestMapping("/system/testApplication")
public class TestApplicationController extends BaseController
{
    @Autowired
    private ITestApplicationService testApplicationService;

    @Autowired
    private ISysAuditService sysAuditService;

    /**
     * 查询检测申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:application:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestApplication testApplication)
    {
        startPage();
        List<TestApplication> list = testApplicationService.selectTestApplicationList(testApplication);
        return getDataTable(list);
    }

    /**
     * 导出检测申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:application:export')")
    @Log(title = "检测申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestApplication testApplication)
    {
        List<TestApplication> list = testApplicationService.selectTestApplicationList(testApplication);
        ExcelUtil<TestApplication> util = new ExcelUtil<TestApplication>(TestApplication.class);
        util.exportExcel(response, list, "检测申请数据");
    }

    /**
     * 获取检测申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:application:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(testApplicationService.selectTestApplicationById(id));
    }

    /**
     * 审核检测申请
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "检测申请", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@Valid @RequestBody TestApplicationAuditRequest testApplicationAuditRequest)
    {
        try {
            return toAjax(testApplicationService.auditTestApplication(testApplicationAuditRequest));
        } catch (RuntimeException e) {
            return error(e.getMessage());
        }
    }

    /**
     * 审核付款凭证
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "付款凭证审核", businessType = BusinessType.UPDATE)
    @PutMapping("/auditPayment")
    public AjaxResult auditPayment(@RequestBody TestApplication testApplication)
    {
        try {
            int rows = testApplicationService.auditPayment(testApplication);
            if (rows > 0) {
                String message = TestApplication.PAYMENT_STATUS_CONFIRMED.equals(testApplication.getPaymentStatus()) 
                    ? "付款凭证审核通过，已确认收款" 
                    : "付款凭证审核拒绝";
                return success(message);
            }
            return error("审核操作失败");
        } catch (Exception e) {
            return error("审核操作异常：" + e.getMessage());
        }
    }

    /**
     * 开始检测
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "开始检测", businessType = BusinessType.UPDATE)
    @PutMapping("/handleStartTest")
    public AjaxResult startTesting(@RequestBody TestApplication testApplication)
    {
        try {
            // 验证申请是否存在
            TestApplication oldApplication = testApplicationService.selectTestApplicationById(testApplication.getId());
            if (oldApplication == null) {
                return error("申请不存在");
            }

            // 验证申请状态是否为已付款
            if (!TestApplication.STATUS_PAID.equals(oldApplication.getStatus())) {
                return error("当前申请状态不允许开始检测，需要先确认收款");
            }

            // 验证付款状态是否为已确认收款
            if (!TestApplication.PAYMENT_STATUS_CONFIRMED.equals(oldApplication.getPaymentStatus())) {
                return error("当前付款状态不允许开始检测，需要先确认收款");
            }

            // 设置状态为进行中
            oldApplication.setStatus(TestApplication.STATUS_IN_PROGRESS);
            oldApplication.setUpdateBy(getUsername());

            int rows = testApplicationService.updateTestApplication(oldApplication);
            if (rows > 0) {
                return success("检测已开始");
            }
            return error("开始检测操作失败");
        } catch (Exception e) {
            return error("开始检测操作异常：" + e.getMessage());
        }
    }

    /**
     * 完成检测
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "完成检测", businessType = BusinessType.UPDATE)
    @PutMapping("/completeTesting")
    public AjaxResult completeTesting(@RequestBody TestApplication testApplication)
    {
        try {
            // 验证申请是否存在
            TestApplication oldApplication = testApplicationService.selectTestApplicationById(testApplication.getId());
            if (oldApplication == null) {
                return error("申请不存在");
            }

            // 验证申请状态是否为进行中
            if (!TestApplication.STATUS_IN_PROGRESS.equals(oldApplication.getStatus())) {
                return error("当前申请状态不允许完成检测");
            }

            // 设置状态为已完成
            oldApplication.setStatus(TestApplication.STATUS_COMPLETED);
            oldApplication.setUpdateBy(getUsername());

            int rows = testApplicationService.updateTestApplication(oldApplication);
            if (rows > 0) {
                return success("检测已完成");
            }
            return error("完成检测操作失败");
        } catch (Exception e) {
            return error("完成检测操作异常：" + e.getMessage());
        }
    }

    /**
     * 管理员报价
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "管理员报价", businessType = BusinessType.UPDATE)
    @PutMapping("/setPrice")
    public AjaxResult setPrice(@Valid @RequestBody TestApplicationPriceRequest request)
    {
        try {
            // 验证申请是否存在
            TestApplication oldApplication = testApplicationService.selectTestApplicationById(request.getTestApplicationId());
            if (oldApplication == null) {
                return error("申请不存在");
            }

            // 验证申请状态是否为待审核
            if (!TestApplication.STATUS_PENDING_REVIEW.equals(oldApplication.getStatus())) {
                return error("当前申请状态不允许设置报价");
            }

            // 设置报价信息
            oldApplication.setAdminPrice(request.getAdminPrice()); // 管理员报价
            oldApplication.setAdminPriceRemark(request.getAdminPriceRemark()); // 报价备注
            oldApplication.setStatus(TestApplication.STATUS_PENDING_PAYMENT); // 状态改为待付款
            oldApplication.setPaymentStatus(TestApplication.PAYMENT_STATUS_PENDING); // 付款状态改为待付款
            oldApplication.setUpdateBy(getUsername());

            int rows = testApplicationService.updateTestApplication(oldApplication);
            if (rows > 0) {
                return success("报价设置成功，金额：" + request.getAdminPrice() + "元");
            }
            return error("报价设置失败");
        } catch (Exception e) {
            return error("报价设置异常：" + e.getMessage());
        }
    }

    /**
     * 确认收款
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "确认收款", businessType = BusinessType.UPDATE)
    @PutMapping("/confirmPayment")
    public AjaxResult confirmPayment(@Valid @RequestBody TestApplicationConfirmPaymentRequest request)
    {
        try {
            // 验证申请是否存在
            TestApplication oldApplication = testApplicationService.selectTestApplicationById(request.getTestApplicationId());
            if (oldApplication == null) {
                return error("申请不存在");
            }

            // 验证付款状态是否为已上传凭证
            if (!TestApplication.PAYMENT_STATUS_UPLOADED.equals(oldApplication.getPaymentStatus())) {
                return error("当前付款状态不允许确认收款，需要先上传付款凭证");
            }

            // 设置确认收款信息
            oldApplication.setConfirmedAmount(request.getConfirmedAmount()); // 实际确认收款金额
            oldApplication.setConfirmPaymentRemark(request.getConfirmPaymentRemark()); // 确认收款备注
            oldApplication.setPaymentStatus(TestApplication.PAYMENT_STATUS_CONFIRMED); // 已确认收款
            oldApplication.setStatus(TestApplication.STATUS_PAID); // 主状态改为已付款
            oldApplication.setUpdateBy(getUsername());

            int rows = testApplicationService.updateTestApplication(oldApplication);
            if (rows > 0) {
                String message = "收款确认成功，实际收款金额：" + request.getConfirmedAmount() + "元";
                // 如果实际收款金额与报价不一致，添加提示
                if (oldApplication.getAdminPrice() != null && 
                    oldApplication.getAdminPrice().compareTo(request.getConfirmedAmount()) != 0) {
                    message += "（与报价金额" + oldApplication.getAdminPrice() + "元不一致）";
                }
                return success(message);
            }
            return error("确认收款操作失败");
        } catch (Exception e) {
            return error("确认收款操作异常：" + e.getMessage());
        }
    }

    /**
     * 拒绝支付凭证
     */
    @PreAuthorize("@ss.hasPermi('system:application:audit')")
    @Log(title = "拒绝支付凭证", businessType = BusinessType.UPDATE)
    @PutMapping("/rejectPayment")
    public AjaxResult rejectPayment(@Valid @RequestBody TestApplicationRejectPaymentRequest request)
    {
        try {
            // 验证申请是否存在
            TestApplication oldApplication = testApplicationService.selectTestApplicationById(request.getTestApplicationId());
            if (oldApplication == null) {
                return error("申请不存在");
            }

            // 验证付款状态是否为已上传凭证
            if (!TestApplication.PAYMENT_STATUS_UPLOADED.equals(oldApplication.getPaymentStatus())) {
                return error("当前付款状态不允许拒绝，需要先上传付款凭证");
            }

            // 设置拒绝状态和拒绝原因
            oldApplication.setPaymentStatus(TestApplication.PAYMENT_STATUS_REJECTED); // 付款凭证被拒绝
            oldApplication.setPaymentRejectReason(request.getPaymentRejectReason()); // 设置拒绝原因
            oldApplication.setAuditPaymentDate(DateUtils.getNowDate());
            oldApplication.setUpdateBy(getUsername());

            // 如果确认收款，同时更新主状态为已付款
            if (TestApplication.PAYMENT_STATUS_CONFIRMED.equals(oldApplication.getPaymentStatus())) {
                oldApplication.setStatus(TestApplication.STATUS_PAID);
            }

            int rows = testApplicationService.updateTestApplication(oldApplication);
            if (rows > 0) {
                return success("支付凭证已拒绝，用户需要重新上传");
            }
            return error("拒绝支付凭证操作失败");
        } catch (Exception e) {
            return error("拒绝支付凭证操作异常：" + e.getMessage());
        }
    }

}
