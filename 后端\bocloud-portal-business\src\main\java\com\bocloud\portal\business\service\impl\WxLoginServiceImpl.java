package com.bocloud.portal.business.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.domain.model.LoginUser;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.enums.CommonStatus;
import com.bocloud.common.enums.UserType;
import com.bocloud.common.enums.AccountStatus;
import com.bocloud.common.enums.InfoStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.uuid.IdUtils;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.framework.web.service.TokenService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.IWxLoginService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import com.bocloud.domain.web.dto.WxPhoneLoginBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bocloud.common.core.domain.entity.SysUser;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WxLoginServiceImpl implements IWxLoginService {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WxMaService wxMaService;

    /**
     * 二维码有效期（秒）
     */
    private static final int QR_CODE_EXPIRE_SECONDS = 180;

    /**
     * Redis中存储登录状态的key前缀
     */
    private static final String REDIS_KEY_PREFIX = "wx:login:";

    @Override
    public AjaxResult generateLoginQrCode() {
        log.info("[generateLoginQrCode] 开始生成微信登录二维码");
        try {
            // 生成场景值
            String sceneStr = IdUtils.simpleUUID();
            log.info("[generateLoginQrCode] 生成场景值: {}", sceneStr);
            
            // 创建临时二维码
            log.info("[generateLoginQrCode] 开始调用微信API创建临时二维码，有效期: {}秒", QR_CODE_EXPIRE_SECONDS);
            WxMpQrCodeTicket ticket = wxMpService.getQrcodeService()
                    .qrCodeCreateTmpTicket(sceneStr, QR_CODE_EXPIRE_SECONDS);
            log.info("[generateLoginQrCode] 微信API返回ticket: {}", ticket.getTicket());

            // 获取二维码URL
            String qrcodeUrl = wxMpService.getQrcodeService().qrCodePictureUrl(ticket.getTicket());
            log.info("[generateLoginQrCode] 生成二维码URL: {}", qrcodeUrl);

            // 保存登录状态到Redis
            String redisKey = REDIS_KEY_PREFIX + sceneStr;
            Map<String, Object> loginInfo = new HashMap<>();
            loginInfo.put("status", "WAITING");
            loginInfo.put("createTime", System.currentTimeMillis());
            redisCache.setCacheObject(redisKey, loginInfo, QR_CODE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("[generateLoginQrCode] 登录状态已保存到Redis，key: {}, 过期时间: {}秒", redisKey, QR_CODE_EXPIRE_SECONDS);

            // 返回结果
            AjaxResult ajax = AjaxResult.success();
            ajax.put("qrcodeUrl", qrcodeUrl);
            ajax.put("sceneStr", sceneStr);
            ajax.put("expireSeconds", QR_CODE_EXPIRE_SECONDS);
            log.info("[generateLoginQrCode] 生成二维码成功，sceneStr: {}, expireSeconds: {}", sceneStr, QR_CODE_EXPIRE_SECONDS);
            return ajax;
        } catch (WxErrorException e) {
            log.error("[generateLoginQrCode] 生成微信登录二维码失败，微信API错误: {}", e.getMessage(), e);
            
            // 检查是否是权限不足的错误
            if (e.getError().getErrorCode() == 48001) {
                log.warn("[generateLoginQrCode] 检测到权限不足错误(48001)，公众号可能未获得二维码接口权限");
                return AjaxResult.error("当前公众号权限不足，无法生成登录二维码。请联系管理员配置微信接口权限或使用其他登录方式。");
            }
            
            // 检查是否是AppSecret无效的错误
            if (e.getError().getErrorCode() == 40125) {
                log.warn("[generateLoginQrCode] 检测到AppSecret无效错误(40125)，请检查微信配置");
                return AjaxResult.error("微信配置错误：AppSecret无效。请检查微信公众平台的AppID和AppSecret配置是否正确。");
            }
            
            // 检查是否是AppID无效的错误
            if (e.getError().getErrorCode() == 40013) {
                log.warn("[generateLoginQrCode] 检测到AppID无效错误(40013)，请检查微信配置");
                return AjaxResult.error("微信配置错误：AppID无效。请检查微信公众平台的AppID配置是否正确。");
            }
            
            return AjaxResult.error("生成二维码失败，请重试");
        } catch (Exception e) {
            log.error("[generateLoginQrCode] 生成微信登录二维码失败，系统异常", e);
            return AjaxResult.error("生成二维码失败，请重试");
        }
    }

    @Override
    public AjaxResult checkLoginStatus(String sceneStr) {
        log.info("[checkLoginStatus] 开始检查登录状态，sceneStr: {}", sceneStr);
        try {
        String redisKey = REDIS_KEY_PREFIX + sceneStr;
        Map<String, Object> loginInfo = redisCache.getCacheObject(redisKey);
        
        if (loginInfo == null) {
                log.warn("[checkLoginStatus] 登录信息已过期或不存在，sceneStr: {}", sceneStr);
            return AjaxResult.error("二维码已过期，请重新获取");
        }

            String status = (String) loginInfo.get("status");
            log.info("[checkLoginStatus] 当前登录状态: {}, sceneStr: {}", status, sceneStr);

        AjaxResult ajax = AjaxResult.success();
            ajax.put("status", status);
        
        // 如果登录成功，返回token
            if ("SUCCESS".equals(status)) {
                String token = (String) loginInfo.get("token");
                ajax.put("token", token);
            // 登录成功后删除Redis中的登录信息
            redisCache.deleteObject(redisKey);
                log.info("[checkLoginStatus] 登录成功，返回token，并清理Redis缓存，sceneStr: {}", sceneStr);
        }
        
        return ajax;
        } catch (Exception e) {
            log.error("[checkLoginStatus] 检查登录状态失败，sceneStr: {}", sceneStr, e);
            return AjaxResult.error("检查登录状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void handleScanEvent(String openId, String sceneStr, String eventType) {
        log.info("[handleScanEvent] 开始处理微信扫码事件，openId: {}, sceneStr: {}, eventType: {}", openId, sceneStr, eventType);
        
        String redisKey = REDIS_KEY_PREFIX + sceneStr;
        Map<String, Object> loginInfo = redisCache.getCacheObject(redisKey);
        
        if (loginInfo == null) {
            log.warn("[handleScanEvent] 登录信息已过期，openId: {}, sceneStr: {}", openId, sceneStr);
            return;
        }

        try {
            // 获取微信用户信息
            log.info("[handleScanEvent] 开始获取微信用户信息，openId: {}", openId);
            WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openId);
            log.info("[handleScanEvent] 获取微信用户信息成功，nickname: {}, headImgUrl: {}", 
                    wxMpUser.getNickname(), wxMpUser.getHeadImgUrl());
            
            // 查询或创建用户
            PortalUser portalUser = getOrCreatePortalUser(wxMpUser);
            log.info("[handleScanEvent] 用户信息处理完成，userId: {}, username: {}", portalUser.getId(), portalUser.getUsername());

            // 更新登录状态
            switch (eventType) {
                case "subscribe":
                    // 用户扫码并关注，直接完成登录
                    log.info("[handleScanEvent] 用户扫码并关注，开始生成token，openId: {}", openId);
                    SysUser sysUser = new SysUser();
                    BeanUtils.copyProperties(portalUser, sysUser);
                    LoginUser loginUser = new LoginUser(portalUser.getId(), sysUser);
                    String token = tokenService.createToken(loginUser);
                    log.info("[handleScanEvent] token生成成功，userId: {}, token: {}", portalUser.getId(), token);
                    
                    loginInfo.put("status", "SUCCESS");
                    loginInfo.put("openId", openId);
                    loginInfo.put("token", token);
                    break;
                case "SCAN":
                    // 用户已关注并扫码，生成token
                    log.info("[handleScanEvent] 用户已关注并扫码，开始生成token，openId: {}", openId);
                    SysUser sysUser2 = new SysUser();
                    BeanUtils.copyProperties(portalUser, sysUser2);
                    LoginUser loginUser2 = new LoginUser(portalUser.getId(), sysUser2);
                    String token2 = tokenService.createToken(loginUser2);
                    log.info("[handleScanEvent] token生成成功，userId: {}, token: {}", portalUser.getId(), token2);
                    
                    loginInfo.put("status", "SUCCESS");
                    loginInfo.put("openId", openId);
                    loginInfo.put("token", token2);
                    break;
                default:
                    log.warn("[handleScanEvent] 未知的事件类型：{}", eventType);
                    return;
            }

            // 更新Redis中的登录信息
            redisCache.setCacheObject(redisKey, loginInfo, QR_CODE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("[handleScanEvent] Redis登录信息更新成功，sceneStr: {}, status: {}", sceneStr, loginInfo.get("status"));
        } catch (WxErrorException e) {
            log.error("[handleScanEvent] 处理微信扫码事件失败，微信API错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("[handleScanEvent] 处理微信扫码事件失败，系统异常", e);
        }
    }

    /**
     * 查询或创建门户用户
     * 支持 openId、手机号绑定同一用户
     * @param wxMpUser 微信用户信息
     * @return PortalUser
     */
    private PortalUser getOrCreatePortalUser(WxMpUser wxMpUser) {
        log.info("[getOrCreatePortalUser] 开始查询或创建用户，openId: {}", wxMpUser.getOpenId());

        // 1. 先通过 openId 查询
        PortalUser portalUser = portalUserService.selectUserByWxOpenId(wxMpUser.getOpenId());
        if (portalUser != null) {
            log.info("[getOrCreatePortalUser] 找到已存在用户，userId: {}, username: {}", portalUser.getId(), portalUser.getUsername());
            return portalUser;
        }

        // 2. 尝试通过手机号查找（如能获取到手机号）
        String phone = null;
        try {
            // WxMpUser 默认没有手机号字段，如有扩展可在此获取
            // phone = wxMpUser.getPhoneNumber();
        } catch (Exception e) {
            log.warn("[getOrCreatePortalUser] 获取手机号异常: {}", e.getMessage());
        }
        
        if (phone != null && !phone.isEmpty()) {
            // 如果能获取到手机号，使用合并逻辑
            log.info("[getOrCreatePortalUser] 获取到手机号，使用合并逻辑，phone: {}", phone);
            return mergeUserIfNeeded(phone, wxMpUser.getOpenId());
        }

        // 3. 没有手机号信息，创建新用户
        log.info("[getOrCreatePortalUser] 用户不存在且无手机号信息，开始创建新用户，openId: {}", wxMpUser.getOpenId());
        portalUser = new PortalUser();
        portalUser.setUsername(wxMpUser.getOpenId()); // 使用openId作为用户名
        portalUser.setNickname(wxMpUser.getNickname());
        portalUser.setAvatar(wxMpUser.getHeadImgUrl());
        portalUser.setWechatOpenid(wxMpUser.getOpenId());
        portalUser.setWechatUnionid(wxMpUser.getUnionId());
        portalUser.setType(UserType.PERSONAL.getCode()); // 默认为个人用户
        portalUser.setStatus(AccountStatus.NORMAL.getCode()); // 正常状态
        portalUser.setInfoStatus(InfoStatus.INCOMPLETE.getCode()); // 未完善
        portalUser.setVerificationType(VerificationType.PERSONAL.getCode()); // 个人认证
        portalUser.setVerificationStatus(VerificationStatus.PENDING.getCode()); // 待认证
        portalUser.setCreateBy("system");
        portalUser.setCreateTime(DateUtils.getNowDate());
        portalUser.setDelFlag(CommonStatus.DEL_FLAG_NORMAL.getCode()); // 新用户默认未删除
        int result = portalUserService.insertPortalUser(portalUser);
        log.info("[getOrCreatePortalUser] 新用户创建完成，userId: {}, username: {}, insertResult: {}", 
                portalUser.getId(), portalUser.getUsername(), result);
        return portalUser;
    }

    /**
     * 小程序手机号授权登录
     */
    @Override
    @Transactional
    public AjaxResult miniappPhoneLogin(WxPhoneLoginBody body) {
        log.info("[miniappPhoneLogin] 开始小程序手机号登录，code: {}, encryptedData: {}, iv: {}", 
                body.getCode(), body.getEncryptedData(), body.getIv());
        try {
            // 1. code换取session_key和openid
            log.info("[miniappPhoneLogin] 开始调用微信API获取session信息");
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(body.getCode());
            String sessionKey = session.getSessionKey();
            String openid = session.getOpenid();
            log.info("[miniappPhoneLogin] code2session成功，openid: {}, sessionKey: {}", openid, sessionKey);

            // 2. 用session_key解密encryptedData获取手机号
            log.info("[miniappPhoneLogin] 开始解密手机号信息, code: {}, sessionKey: {}, encryptedData: {}, iv: {}", body.getCode(), sessionKey, body.getEncryptedData(), body.getIv());
            WxMaPhoneNumberInfo phoneInfo = wxMaService.getUserService().getPhoneNoInfo(sessionKey, body.getEncryptedData(), body.getIv());
            if (phoneInfo == null) {
                log.error("[miniappPhoneLogin] 获取手机号信息失败, code: {}, sessionKey: {}, encryptedData: {}, iv: {}", body.getCode(), sessionKey, body.getEncryptedData(), body.getIv());
                return AjaxResult.error("解密手机号信息失败");
            }
            String phone = phoneInfo.getPhoneNumber();
            log.info("[miniappPhoneLogin] 手机号解密成功: {}", phone);

            // 3. 先查询手机号用户是否存在
            PortalUser portalUser = portalUserService.selectUserByPhone(phone);
            // 4. 如果手机号用户不存在，才执行合并逻辑
            if (portalUser == null) {
                log.info("[miniappPhoneLogin] 手机号用户不存在，开始检查是否需要合并用户，phone: {}, openid: {}", phone, openid);
                portalUser = mergeUserIfNeeded(phone, openid);
                log.info("[miniappPhoneLogin] 用户合并完成，userId: {}, phone: {}", portalUser.getId(), phone);
            } else {
                log.info("[miniappPhoneLogin] 手机号用户已存在，无需合并，userId: {}, phone: {}", portalUser.getId(), phone);
            }
            // 5. 更新最后登录时间
            portalUser.setLastLoginTime(DateUtils.getNowDate());
            int updateResult = portalUserService.updatePortalUser(portalUser);
            log.info("[miniappPhoneLogin] 更新用户最后登录时间完成，updateResult: {}", updateResult);
            // 6. 生成token
            log.info("[miniappPhoneLogin] 开始生成登录token，userId: {}", portalUser.getId());
            SysUser sysUser = new SysUser();
            BeanUtils.copyProperties(portalUser, sysUser);
            LoginUser loginUser = new LoginUser(portalUser.getId(), sysUser);
            String token = tokenService.createToken(loginUser);
            log.info("[miniappPhoneLogin] token生成成功，userId: {}, token: {}", portalUser.getId(), token);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("token", token);
            ajax.put("user", portalUser);
            log.info("[miniappPhoneLogin] 小程序手机号登录成功，userId: {}, phone: {}", portalUser.getId(), phone);
            return ajax;
        } catch (WxErrorException e) {
            // code已被使用或无效，微信返回40029
            if (e.getError() != null && e.getError().getErrorCode() == 40029) {
                log.warn("[miniappPhoneLogin] code已被使用或无效，需前端重新获取，code={}, sessionKey=未知, encryptedData={}, iv={}",
                        body.getCode(), body.getEncryptedData(), body.getIv());
                return AjaxResult.error("登录超时或已失效，请重试");
            }
            log.error("[miniappPhoneLogin] 微信API错误: {}, code={}, encryptedData={}, iv={}", e.getMessage(), body.getCode(), body.getEncryptedData(), body.getIv(), e);
            return AjaxResult.error("微信登录失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("[miniappPhoneLogin] 小程序手机号登录失败，系统异常, code={}, encryptedData={}, iv={}", body.getCode(), body.getEncryptedData(), body.getIv(), e);
            return AjaxResult.error("微信登录失败: " + e.getMessage());
        }
    }

    /**
     * 合并用户（如果需要）
     * 处理手机号和openId可能对应不同用户的情况
     * @param phone 手机号
     * @param openId 微信openId
     * @return 合并后的用户
     */
    private PortalUser mergeUserIfNeeded(String phone, String openId) {
        log.info("[mergeUserIfNeeded] 开始检查用户合并，phone: {}, openId: {}", phone, openId);
        
        PortalUser phoneUser = portalUserService.selectUserByPhone(phone);
        PortalUser openIdUser = portalUserService.selectUserByWxOpenId(openId);
        
        if (phoneUser != null && openIdUser != null) {
            // 两个用户都存在，需要合并
            if (phoneUser.getId().equals(openIdUser.getId())) {
                // 已经是同一用户，直接返回
                log.info("[mergeUserIfNeeded] 用户已存在且已绑定，无需合并，userId: {}", phoneUser.getId());
                return phoneUser;
            } else {
                // 需要合并两个不同的用户
                log.info("[mergeUserIfNeeded] 发现需要合并的用户，手机号用户ID: {}, openId用户ID: {}", 
                        phoneUser.getId(), openIdUser.getId());
                
                // 保留手机号用户，绑定openId，删除openId用户
                phoneUser.setWechatOpenid(openId);
                phoneUser.setWechatUnionid(openIdUser.getWechatUnionid());
                portalUserService.updatePortalUser(phoneUser);
                
                // 逻辑删除openId用户
                openIdUser.setDelFlag("1");
                portalUserService.updatePortalUser(openIdUser);
                
                log.info("[mergeUserIfNeeded] 用户合并完成，保留用户ID: {}, 删除用户ID: {}", 
                        phoneUser.getId(), openIdUser.getId());
                return phoneUser;
            }
        } else if (phoneUser != null) {
            // 只有手机号用户存在，绑定openId
            log.info("[mergeUserIfNeeded] 手机号用户存在，绑定openId，userId: {}", phoneUser.getId());
            phoneUser.setWechatOpenid(openId);
            portalUserService.updatePortalUser(phoneUser);
            return phoneUser;
        } else if (openIdUser != null) {
            // 只有openId用户存在，绑定手机号
            log.info("[mergeUserIfNeeded] openId用户存在，绑定手机号，userId: {}", openIdUser.getId());
            openIdUser.setPhone(phone);
            openIdUser.setUsername(phone); // 用手机号做用户名
            portalUserService.updatePortalUser(openIdUser);
            return openIdUser;
        } else {
            // 都不存在，创建新用户
            log.info("[mergeUserIfNeeded] 用户不存在，创建新用户");
            PortalUser portalUser = new PortalUser();
            portalUser.setPhone(phone);
            portalUser.setUsername(phone);
            portalUser.setWechatOpenid(openId);
                    portalUser.setType(UserType.PERSONAL.getCode());
        portalUser.setStatus(AccountStatus.NORMAL.getCode());
        portalUser.setInfoStatus(InfoStatus.INCOMPLETE.getCode());
        portalUser.setVerificationType(VerificationType.PERSONAL.getCode());
        portalUser.setVerificationStatus(VerificationStatus.PENDING.getCode());
            portalUser.setCreateBy("miniapp");
            portalUser.setCreateTime(DateUtils.getNowDate());
            portalUser.setDelFlag("0");
            
            int insertResult = portalUserService.insertPortalUser(portalUser);
            log.info("[mergeUserIfNeeded] 新用户创建完成，userId: {}, insertResult: {}", 
                    portalUser.getId(), insertResult);
            return portalUser;
        }
    }
} 