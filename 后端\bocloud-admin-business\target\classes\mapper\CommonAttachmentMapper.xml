<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.CommonAttachmentMapper">
    
    <resultMap type="CommonAttachment" id="CommonAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="businessId"      column="business_id"      />
        <result property="businessType"    column="business_type"    />
        <result property="originalFilename" column="original_filename" />
        <result property="relativePath"    column="relative_path"    />
        <result property="fullUrl"         column="full_url"         />
        <result property="fileExtension"   column="file_extension"   />
        <result property="fileSize"        column="file_size"        />
        <result property="fileType"        column="file_type"        />
        <result property="uploadBy"        column="upload_by"        />
        <result property="uploadDate"      column="upload_time"      />
    </resultMap>

    <sql id="selectCommonAttachmentVo">
        select attachment_id, business_id, business_type, original_filename, relative_path, full_url, file_extension, file_size, file_type, upload_by, upload_time
        from common_attachments
    </sql>

    <select id="selectCommonAttachmentList" parameterType="CommonAttachment" resultMap="CommonAttachmentResult">
        <include refid="selectCommonAttachmentVo"/>
        <where>  
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="originalFilename != null  and originalFilename != ''"> and original_filename like concat('%', #{originalFilename}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="uploadBy != null "> and upload_by = #{uploadBy}</if>
        </where>
        order by upload_time desc
    </select>
    
    <select id="selectCommonAttachmentById" parameterType="Long" resultMap="CommonAttachmentResult">
        <include refid="selectCommonAttachmentVo"/>
        where attachment_id = #{attachmentId}
    </select>

    <select id="selectCommonAttachmentByBusiness" resultMap="CommonAttachmentResult">
        <include refid="selectCommonAttachmentVo"/>
        where business_id = #{businessId} and business_type = #{businessType}
    </select>
        
    <insert id="insertCommonAttachment" parameterType="CommonAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into common_attachments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null">business_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="originalFilename != null">original_filename,</if>
            <if test="relativePath != null">relative_path,</if>
            <if test="fullUrl != null">full_url,</if>
            <if test="fileExtension != null">file_extension,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="uploadBy != null">upload_by,</if>
            <if test="uploadDate != null">upload_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null">#{businessId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="originalFilename != null">#{originalFilename},</if>
            <if test="relativePath != null">#{relativePath},</if>
            <if test="fullUrl != null">#{fullUrl},</if>
            <if test="fileExtension != null">#{fileExtension},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="uploadBy != null">#{uploadBy},</if>
            <if test="uploadDate != null">#{uploadDate},</if>
         </trim>
    </insert>

    <update id="updateCommonAttachment" parameterType="CommonAttachment">
        update common_attachments
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="originalFilename != null">original_filename = #{originalFilename},</if>
            <if test="relativePath != null">relative_path = #{relativePath},</if>
            <if test="fullUrl != null">full_url = #{fullUrl},</if>
            <if test="fileExtension != null">file_extension = #{fileExtension},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="uploadBy != null">upload_by = #{uploadBy},</if>
            <if test="uploadDate != null">upload_time = #{uploadDate},</if>
        </trim>
        where attachment_id = #{attachmentId}
    </update>

    <delete id="deleteCommonAttachmentById" parameterType="Long">
        delete from common_attachments where attachment_id = #{attachmentId}
    </delete>

    <delete id="deleteCommonAttachmentByIds" parameterType="String">
        delete from common_attachments where attachment_id in
        <foreach item="attachmentId" collection="array" open="(" separator="," close=")">
            #{attachmentId}
        </foreach>
    </delete>

    <delete id="deleteAttachmentByBusiness">
        delete from common_attachments
        where business_id = #{businessId} 
        and business_type = #{businessType}
    </delete>
</mapper> 