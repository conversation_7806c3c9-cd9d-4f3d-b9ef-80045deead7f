package com.bocloud.portal.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.portal.business.mapper.CommonAttachmentMapper;
import com.bocloud.portal.business.service.ICommonAttachmentService;

/**
 * 通用附件Service业务层处理
 */
@Service
public class CommonAttachmentServiceImpl implements ICommonAttachmentService {

    @Autowired
    private CommonAttachmentMapper commonAttachmentMapper;


    /**
     * 查询通用附件
     * 
     * @param attachmentId 通用附件主键
     * @return 通用附件
     */
    @Override
    public CommonAttachment selectCommonAttachmentById(Long attachmentId) {
        return commonAttachmentMapper.selectCommonAttachmentById(attachmentId);
    }

    /**
     * 查询通用附件列表
     * 
     * @param commonAttachment 通用附件
     * @return 通用附件
     */
    @Override
    public List<CommonAttachment> selectCommonAttachmentList(CommonAttachment commonAttachment) {
        return commonAttachmentMapper.selectCommonAttachmentList(commonAttachment);
    }
    
    /**
     * 根据业务ID和业务类型查询附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 附件列表
     */
    @Override
    public List<CommonAttachment> selectCommonAttachmentByBusiness(Long businessId, String businessType) {
        return commonAttachmentMapper.selectCommonAttachmentByBusiness(businessId, businessType);
    }

    /**
     * 新增通用附件
     * 
     * @param commonAttachment 通用附件
     * @return 结果
     */
    @Override
    public int insertCommonAttachment(CommonAttachment commonAttachment) {
        return commonAttachmentMapper.insertCommonAttachment(commonAttachment);
    }

    /**
     * 修改通用附件
     * 
     * @param commonAttachment 通用附件
     * @return 结果
     */
    @Override
    public int updateCommonAttachment(CommonAttachment commonAttachment) {
        return commonAttachmentMapper.updateCommonAttachment(commonAttachment);
    }

    /**
     * 批量删除通用附件
     * 
     * @param attachmentIds 需要删除的通用附件主键
     * @return 结果
     */
    @Override
    public int deleteCommonAttachmentByIds(Long[] attachmentIds) {
        return commonAttachmentMapper.deleteCommonAttachmentByIds(attachmentIds);
    }

    /**
     * 删除通用附件信息
     * 
     * @param attachmentId 通用附件主键
     * @return 结果
     */
    @Override
    public int deleteCommonAttachmentById(Long attachmentId) {
        return commonAttachmentMapper.deleteCommonAttachmentById(attachmentId);
    }
    
    /**
     * 根据业务ID和业务类型删除附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 结果
     */
    @Override
    public int deleteAttachmentByBusiness(Long businessId, String businessType) {
        return commonAttachmentMapper.deleteAttachmentByBusiness(businessId, businessType);
    }

} 