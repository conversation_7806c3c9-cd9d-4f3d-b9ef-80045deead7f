package com.bocloud.admin.business.service;

/**
 * 消息通知服务接口
 * 用于后台管理员发送需求相关的消息通知
 */
public interface IMessageNotificationService {
    
    /**
     * 发送需求审核结果通知
     * 
     * @param demandId 需求ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     * @param approved 是否通过
     * @param reviewComment 审核意见
     */
    void sendDemandAuditNotification(Long demandId, String demandTitle, Long userId, boolean approved, String reviewComment);
    
    /**
     * 发送需求状态变更通知
     * 
     * @param demandId 需求ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     */
    void sendDemandStatusChangeNotification(Long demandId, String demandTitle, Long userId, String oldStatus, String newStatus);
    
    /**
     * 发送方案审核结果通知
     * 
     * @param solutionId 方案ID
     * @param solutionTitle 方案标题
     * @param userId 用户ID
     * @param approved 是否通过
     * @param reviewComment 审核意见
     */
    void sendSolutionAuditNotification(Long solutionId, String solutionTitle, Long userId, boolean approved, String reviewComment);
    
    /**
     * 发送方案中标通知
     * 
     * @param solutionId 方案ID
     * @param solutionTitle 方案标题
     * @param demandId 需求ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     */
    void sendSolutionBidWinNotification(Long solutionId, String solutionTitle, Long demandId, String demandTitle, Long userId);
    
    /**
     * 发送合同审核结果通知
     * 
     * @param contractId 合同ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     * @param approved 是否通过
     * @param reviewComment 审核意见
     */
    void sendContractAuditNotification(Long contractId, String demandTitle, Long userId, boolean approved, String reviewComment);
    
    /**
     * 发送需求完成通知
     * 
     * @param demandId 需求ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     */
    void sendDemandCompletedNotification(Long demandId, String demandTitle, Long userId);
    
    /**
     * 发送平台收费通知
     * 
     * @param chargeId 收费ID
     * @param demandTitle 需求标题
     * @param userId 用户ID
     * @param amount 收费金额
     */
    void sendPlatformChargeNotification(Long chargeId, String demandTitle, Long userId, String amount);

    /**
     * 发送专家审核结果通知
     *
     * @param userId 专家用户ID
     * @param approved 是否通过
     * @param reviewComment 审核意见
     */
    void sendExpertAuditNotification(Long userId, boolean approved);
} 