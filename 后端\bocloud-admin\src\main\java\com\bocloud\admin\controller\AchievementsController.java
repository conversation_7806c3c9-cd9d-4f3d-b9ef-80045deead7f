package com.bocloud.admin.controller;

import com.bocloud.admin.business.service.IAchievementsService;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import com.bocloud.domain.system.achievement.Achievements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @description 成果发布Controller
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/achievements")
public class AchievementsController extends BaseController {
    @Autowired
    private IAchievementsService iAchievementsService;

    /**
     * 查询成果发布列表
     */
    //@PreAuthorize("@ss.hasPermi('system:achievements:list')")
    @GetMapping("/list")
    public TableDataInfo list(Achievements achievements)
    {
        startPage();
        List<Achievements> list = iAchievementsService.selectAchievementsList(achievements);
        return getDataTable(list);
    }

    /**
     * 导出成果发布列表
     */
    @PreAuthorize("@ss.hasPermi('system:achievements:export')")
    @Log(title = "成果发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Achievements achievements)
    {
        List<Achievements> list = iAchievementsService.selectAchievementsList(achievements);
        ExcelUtil<Achievements> util = new ExcelUtil<Achievements>(Achievements.class);
        util.exportExcel(response, list, "成果发布数据");
    }

    /**
     * 获取成果发布详细成果
     */
    @PreAuthorize("@ss.hasPermi('system:achievements:query')")
    @GetMapping(value = "/{achievementId}")
    public AjaxResult getInfo(@PathVariable("achievementId") Long achievementId)
    {
        return success(iAchievementsService.selectAchievementsByAchievementId(achievementId));
    }

    /**
     * 新增成果发布
     */
    @PreAuthorize("@ss.hasPermi('system:achievements:add')")
    @Log(title = "成果发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Achievements achievements)
    {
        achievements.setCreateBy(getUsername());
        return toAjax(iAchievementsService.insertAchievements(achievements));
    }

    /**
     * 修改成果发布
     */
    @PreAuthorize("@ss.hasPermi('system:achievements:edit')")
    @Log(title = "成果发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Achievements achievements)
    {
        achievements.setUpdateBy(getUsername());
        return toAjax(iAchievementsService.updateAchievements(achievements));
    }

    /**
     * 删除成果发布
     */
    @PreAuthorize("@ss.hasPermi('system:achievements:remove')")
    @Log(title = "成果发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{achievementIds}")
    public AjaxResult remove(@PathVariable Long[] achievementIds)
    {
        return toAjax(iAchievementsService.deleteAchievementsByAchievementIds(achievementIds));
    }

    /**
     * 审核成果发布
     */
    //@PreAuthorize("@ss.hasPermi('system:achievements:audit')")
    @Log(title = "审核成果发布", businessType = BusinessType.UPDATE)
    @PostMapping("/audit/{achievementId}")
    public AjaxResult audit(@PathVariable Long achievementId)
    {
        Achievements achievements = iAchievementsService.selectAchievementsByAchievementId(achievementId);
        achievements.setUpdateBy(getUsername());
        achievements.setStatus(Achievements.STATUS_PUBLISH);
        return toAjax(iAchievementsService.updateAchievements(achievements));
    }
}
