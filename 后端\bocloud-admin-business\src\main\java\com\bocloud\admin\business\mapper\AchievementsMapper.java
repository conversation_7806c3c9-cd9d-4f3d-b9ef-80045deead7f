package com.bocloud.admin.business.mapper;



import com.bocloud.domain.system.achievement.Achievements;

import java.util.List;

public interface AchievementsMapper {
    /**
     * 成果信息发布
     *
     * @param AchievementId 信息发布主键
     * @return 信息发布
     */
    public Achievements selectAchievementsByAchievementId(Long AchievementId);

    /**
     * 成果信息发布列表
     *
     * @param Achievements 信息发布
     * @return 信息发布集合
     */
    public List<Achievements> selectAchievementsList(Achievements Achievements);

    /**
     * 新增信息发布
     *
     * @param Achievements 信息发布
     * @return 结果
     */
    public int insertAchievements(Achievements Achievements);

    /**
     * 修改信息发布
     *
     * @param Achievements 信息发布
     * @return 结果
     */
    public int updateAchievements(Achievements Achievements);

    /**
     * 删除信息发布
     *
     * @param AchievementId 信息发布主键
     * @return 结果
     */
    public int deleteAchievementsByAchievementId(Long AchievementId);

    /**
     * 批量删除信息发布
     *
     * @param AchievementIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAchievementsByAchievementIds(Long[] AchievementIds);
}
