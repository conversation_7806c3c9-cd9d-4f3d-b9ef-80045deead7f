package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.DemandSolutionMapper;
import com.bocloud.admin.business.service.IDemandSolutionService;
import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.DemandSolutionAuditRequest;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.admin.business.service.IEnterpriseDemandService;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.common.enums.AuditStatus;
import com.bocloud.common.enums.DemandSolutionStatus;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.domain.system.CommonAttachment;

/**
 * 需求方案Service实现类
 */
@Service
public class DemandSolutionServiceImpl implements IDemandSolutionService {
    
    @Autowired
    private DemandSolutionMapper demandSolutionMapper;

    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    @Autowired
    private ISysAuditService sysAuditService;
    
    @Autowired
    private IMessageNotificationService messageNotificationService;

    @Autowired
    private ICommonAttachmentService attachmentService;

    /**
     * 查询方案列表
     * 
     * @param solution 查询条件
     * @return 方案列表
     */
    @Override
    public List<DemandSolution> selectDemandSolutionList(DemandSolution solution) {
        return demandSolutionMapper.selectDemandSolutionList(solution);
    }

    /**
     * 查询方案详细信息
     * 
     * @param solutionId 方案ID
     * @return 方案信息
     */
    @Override
    public DemandSolution selectDemandSolutionById(Long solutionId) {
        DemandSolution solution = demandSolutionMapper.selectDemandSolutionById(solutionId);
        if (solution != null) {
            // 查询并设置附件
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentByBusiness(solutionId, "demand_solution");
            solution.setAttachments(attachments);
        }
        return solution;
    }

    /**
     * 更新方案
     * 
     * @param solution 方案信息
     * @return 结果
     */
    @Override
    public int updateDemandSolution(DemandSolution solution) {
        return demandSolutionMapper.updateDemandSolution(solution);
    }

    @Override
    @Transactional
    public int auditSolution(DemandSolutionAuditRequest request, Long auditUserId, String auditUserName) {
        // 1. 获取原方案信息
        DemandSolution originalSolution = selectDemandSolutionById(request.getSolutionId());
        if (originalSolution == null) {
            throw new RuntimeException("方案不存在");
        }

        // 2. 更新方案状态
        DemandSolution demandSolution = new DemandSolution();
        demandSolution.setSolutionId(request.getSolutionId());
        Long demandId = originalSolution.getDemandId();
        if (AuditStatus.PASSED.getCode().equals(request.getAuditResult())) {
            if (originalSolution.getStatus().equals(DemandSolutionStatus.PENDING.getCode())) {
                demandSolution.setStatus(DemandSolutionStatus.APPROVED.getCode()); // 通过 -> 待采纳
            } else if (originalSolution.getStatus().equals(DemandSolutionStatus.SELECTED.getCode())) {
                demandSolution.setStatus(DemandSolutionStatus.BID_WIN.getCode()); // 已采纳，待审核 -> 已中标
                // 同步更新需求状态为待签约
                if (demandId != null) {
                    EnterpriseDemand enterpriseDemand = new EnterpriseDemand();
                    enterpriseDemand.setDemandId(demandId);
                    enterpriseDemand.setStatus(EnterpriseDemandStatus.WAIT_SIGN.getCode()); // 5-待签约
                    enterpriseDemandService.updateEnterpriseDemandAudit(enterpriseDemand);
                }
            }
        } else {
            demandSolution.setStatus(DemandSolutionStatus.REJECTED.getCode()); // 驳回 -> 已驳回
        }
        int rows = updateDemandSolution(demandSolution);
        if (rows <= 0) {
            throw new RuntimeException("更新方案状态失败");
        }

        // 3. 记录审核信息
        SysAudit sysAudit = new SysAudit();
        sysAudit.setBusinessType("solution-audit");
        sysAudit.setBusinessId(request.getSolutionId());
        sysAudit.setAuditType("0");
        sysAudit.setFromStatus(originalSolution.getStatus());
        sysAudit.setToStatus(demandSolution.getStatus());
        sysAudit.setAuditResult(request.getAuditResult());
        sysAudit.setReviewComment(request.getReviewComment());
        sysAudit.setAuditUserId(auditUserId);
        sysAudit.setAuditUserName(auditUserName);
        sysAudit.setCreateTime(new Date());
        rows = sysAuditService.insertSysAudit(sysAudit);
        if (rows <= 0) {
            throw new RuntimeException("记录审核信息失败");
        }

        if (demandSolution.getStatus().equals(DemandSolutionStatus.BID_WIN.getCode())) {
            // 查询同一需求下除当前方案外的所有方案
            List<DemandSolution> others = demandSolutionMapper.selectByDemandIdExceptSolutionId(demandId, request.getSolutionId());
            for (DemandSolution other : others) {
                if (!DemandSolutionStatus.BID_LOSE.getCode().equals(other.getStatus())) {
                    other.setStatus(DemandSolutionStatus.BID_LOSE.getCode());
                    demandSolutionMapper.updateDemandSolution(other);
                }
            }
        }

        // 发送方案审核结果通知
        try {
            boolean approved = AuditStatus.PASSED.getCode().equals(request.getAuditResult());
            messageNotificationService.sendSolutionAuditNotification(
                request.getSolutionId(),
                originalSolution.getSolutionTitle(),
                originalSolution.getPortalUserId(),
                approved,
                request.getReviewComment()
            );
        } catch (Exception e) {
            // 消息发送失败不影响主业务流程
            // 可以记录日志，但不抛出异常
        }

        return 1;
    }
} 