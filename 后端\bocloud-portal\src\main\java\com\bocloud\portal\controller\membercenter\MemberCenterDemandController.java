package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.portal.business.service.IEnterpriseDemandService;
import com.bocloud.portal.business.service.IDemandSolutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 需求Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/membercenter/demand")
public class MemberCenterDemandController extends BaseController
{
    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    /**
     * 查询企业需求列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseDemand enterpriseDemand)
    {
        startPage();
        enterpriseDemand.setPortalUserId(SecurityUtils.getUserId());
        List<EnterpriseDemand> list = enterpriseDemandService.selectEnterpriseDemandList(enterpriseDemand);
        return getDataTable(list);
    }

    /**
     * 获取需求详细信息
     */
    @GetMapping(value = "/{demandId}")
    public AjaxResult getInfo(@PathVariable("demandId") Long demandId)
    {
        try {
            Map<String, Object> result = enterpriseDemandService.getDemandDetail(demandId, SecurityUtils.getUserId());
            return AjaxResult.success(result);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 新增需求
     */
    @Log(title = "企业需求", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody EnterpriseDemand enterpriseDemand)
    {
        try {
            enterpriseDemand.setPortalUserId(SecurityUtils.getUserId());
            EnterpriseDemand createdDemand = enterpriseDemandService.createEnterpriseDemand(enterpriseDemand);
            return AjaxResult.success("需求发布成功，请等待审核", createdDemand);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改需求
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody EnterpriseDemand enterpriseDemand)
    {
        try {
            enterpriseDemand.setPortalUserId(SecurityUtils.getUserId());
            int rows = enterpriseDemandService.updateEnterpriseDemand(enterpriseDemand);
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除需求
     */
    @Log(title = "企业需求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{demandId}")
    public AjaxResult remove(@PathVariable Long demandId)
    {
        try {
            enterpriseDemandService.deleteEnterpriseDemandById(demandId, SecurityUtils.getUserId());
            return AjaxResult.success("删除成功");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 客户主动完成需求
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{demandId}")
    public AjaxResult completeDemand(@PathVariable Long demandId) {
        try {
            int rows = enterpriseDemandService.completeEnterpriseDemand(demandId, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /*
     * 客户主动关闭需求
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{demandId}")
    public AjaxResult closeDemand(@PathVariable Long demandId) {
        try {
            int rows = enterpriseDemandService.closeEnterpriseDemand(demandId, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 公开需求
     * 将处于"待公开"状态的需求，设置为"投标中"状态，允许方案提供方开始投标。
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{demandId}")
    public AjaxResult publishDemand(@PathVariable Long demandId) {
        try {
            // Note: The service method name changed for clarity
            int rows = enterpriseDemandService.publishDemandAndStartBidding(
                demandId,
                SecurityUtils.getUserId()
            );

            if (rows > 0) {
                return AjaxResult.success("需求已成功发布并进入投标中状态");
            } else {
                // This case should ideally be handled by ServiceException if validation fails
                return AjaxResult.error("需求发布失败，请确认需求状态或联系管理员。");
            }
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            // Log the actual exception for debugging
            logger.error("发布需求并进入投标中状态失败: demandId={}, userId={}, error={}", demandId, SecurityUtils.getUserId(), e.getMessage(), e);
            return AjaxResult.error("操作失败，请稍后重试或联系管理员");
        }
    }

    /**
     * 结束投标
     * 将处于"投标中"状态的需求，设置为"待签约"状态，允许方案提供方开始投标。
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/end-bidding/{demandId}")
    public AjaxResult endBidding(@PathVariable Long demandId) {
        try {
            int rows = enterpriseDemandService.endBidding(demandId, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 选择中标方案
     * 将需求状态更新为"待签约"，并将选中的方案状态更新为"已中标"
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/select-solution/{demandId}/{solutionId}")
    public AjaxResult selectSolution(@PathVariable Long demandId, @PathVariable Long solutionId) {
        try {
            int rows = enterpriseDemandService.selectSolution(demandId, solutionId, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 上传合同
     * 将需求状态更新为"合同已上传"，等待平台审核
     */
    @Log(title = "企业需求", businessType = BusinessType.UPDATE)
    @PutMapping("/sign/{demandId}")
    public AjaxResult sign(@PathVariable Long demandId, @RequestBody DemandContract contract) {
        try {
            contract.setDemandId(demandId);
            int rows = enterpriseDemandService.sign(demandId, contract, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

} 