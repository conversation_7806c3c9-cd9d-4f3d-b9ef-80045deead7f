package com.bocloud.domain.system.demand;

/**
 * 需求状态枚举
 */
public enum EnterpriseDemandStatus {
    PENDING("0", "待审核"),
    WAIT_PUBLIC("1", "待公开"),
    BIDDING("2", "投标中"),
    SELECTING("3", "选标中"),
    PLATFORM_REVIEW("4", "待平台审核"),
    WAIT_SIGN("5", "待签约"),
    IN_PROGRESS("6", "进行中"),
    COMPLETED("7", "已完成"),
    CLOSED("8", "已关闭"),
    FAILED("-1", "审核失败");

    private final String code;
    private final String desc;

    EnterpriseDemandStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EnterpriseDemandStatus fromCode(String code) {
        for (EnterpriseDemandStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        EnterpriseDemandStatus status = fromCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
} 