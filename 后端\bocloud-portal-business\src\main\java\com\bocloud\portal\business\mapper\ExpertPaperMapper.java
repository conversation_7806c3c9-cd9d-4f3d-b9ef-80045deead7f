package com.bocloud.portal.business.mapper;

import java.util.List;

import com.bocloud.domain.web.ExpertPaper;

/**
 * 专家论文Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExpertPaperMapper {
    /**
     * 批量新增专家论文
     * 
     * @param expertPaperList 专家论文列表
     * @return 结果
     */
    public int insertExpertPapers(List<ExpertPaper> expertPaperList);

    /**
     * 修改专家论文
     * 
     * @param expertPaper 专家论文
     * @return 结果
     */
    public int updateExpertPaper(ExpertPaper expertPaper);

    /**
     * 批量删除专家论文
     * 
     * @param paperIds 需要删除的论文ID
     * @return 结果
     */
    public int deleteExpertPaperByIds(Long[] paperIds);

    /**
     * 根据专家ID删除论文
     * 
     * @param expertId 专家ID
     * @return 结果
     */
    public int deleteExpertPapersByExpertId(Long expertId);

    /**
     * 根据专家ID查询论文列表
     * 
     * @param expertId 专家ID
     * @return 论文列表
     */
    public List<ExpertPaper> selectExpertPapersByExpertId(Long expertId);

    /**
     * 查询专家论文列表
     * 
     * @param query
     * @return
     */
    public List<ExpertPaper> selectExpertPaperList(ExpertPaper query);
} 