<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.DemandSolutionInviteMapper">

    <resultMap id="DemandSolutionInviteResult" type="com.bocloud.domain.web.DemandSolutionInvite">
        <id property="id" column="id"/>
        <result property="demandId" column="demand_id"/>
        <result property="invitedUserId" column="invited_user_id"/>
        <result property="inviteStatus" column="invite_status"/>
        <result property="inviteTime" column="invite_time"/>
        <result property="operatorId" column="operator_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="demandExists" column="demand_exists"/>
    </resultMap>

    <select id="selectDemandSolutionInviteById" parameterType="Long" resultMap="DemandSolutionInviteResult">
        select dsi.*, 
               case when ed.demand_id is not null then 1 else 0 end as demand_exists
        from demand_solution_invite dsi
        left join enterprise_demand ed on dsi.demand_id = ed.demand_id and ed.del_flag = '0'
        where dsi.id = #{id}
    </select>

    <select id="selectDemandSolutionInviteList" parameterType="DemandSolutionInvite" resultMap="DemandSolutionInviteResult">
        select * from demand_solution_invite
        <where>
            <if test="demandId != null">and demand_id = #{demandId}</if>
            <if test="invitedUserId != null">and invited_user_id = #{invitedUserId}</if>
            <if test="inviteStatus != null and inviteStatus != ''">and invite_status = #{inviteStatus}</if>
            <if test="operatorId != null">and operator_id = #{operatorId}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
        </where>
        order by invite_time desc
    </select>

    <insert id="insertDemandSolutionInvite" parameterType="DemandSolutionInvite" useGeneratedKeys="true" keyProperty="id">
        insert into demand_solution_invite
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="demandId != null">demand_id,</if>
            <if test="invitedUserId != null">invited_user_id,</if>
            <if test="inviteStatus != null">invite_status,</if>
            <if test="inviteTime != null">invite_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="demandId != null">#{demandId},</if>
            <if test="invitedUserId != null">#{invitedUserId},</if>
            <if test="inviteStatus != null">#{inviteStatus},</if>
            <if test="inviteTime != null">#{inviteTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDemandSolutionInvite" parameterType="DemandSolutionInvite">
        update demand_solution_invite
        <trim prefix="SET" suffixOverrides=",">
            <if test="demandId != null">demand_id = #{demandId},</if>
            <if test="invitedUserId != null">invited_user_id = #{invitedUserId},</if>
            <if test="inviteStatus != null">invite_status = #{inviteStatus},</if>
            <if test="inviteTime != null">invite_time = #{inviteTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDemandSolutionInviteById" parameterType="Long">
        delete from demand_solution_invite where id = #{id}
    </delete>

    <delete id="deleteDemandSolutionInviteByIds" parameterType="Long[]">
        delete from demand_solution_invite where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertDemandSolutionInvite" parameterType="java.util.List">
        insert into demand_solution_invite
        (demand_id, invited_user_id, invite_status, invite_time, operator_id, remark, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.demandId}, #{item.invitedUserId}, #{item.inviteStatus}, #{item.inviteTime}, #{item.operatorId}, #{item.remark},
             #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

</mapper> 