package com.bocloud.portal.controller.membercenter;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.pilot_application.PilotApplication;
import com.bocloud.portal.business.service.IPilotApplicationService;
import com.bocloud.common.core.page.TableDataInfo;

/**
 * 中试申请Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/membercenter/pilot")
public class MemverCenterPilotApplicationController extends BaseController
{
    @Autowired
    private IPilotApplicationService pilotApplicationService;

    /**
     * 查询中试申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PilotApplication pilotApplication)
    {
        startPage();
        pilotApplication.setPortalUserId(SecurityUtils.getUserId());
        List<PilotApplication> list = pilotApplicationService.selectPilotApplicationList(pilotApplication);
        return getDataTable(list);
    }

    /**
     * 获取中试申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        try {
            PilotApplication application = pilotApplicationService.getPilotApplicationDetail(id, SecurityUtils.getUserId());
            return AjaxResult.success(application);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 新增中试申请
     */
    @Log(title = "中试申请", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody PilotApplication pilotApplication)
    {
        try {
            pilotApplication.setPortalUserId(SecurityUtils.getUserId());
            PilotApplication createdApplication = pilotApplicationService.createPilotApplication(pilotApplication);
            return AjaxResult.success("中试申请提交成功，请等待审核", createdApplication);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改中试申请
     */
    @Log(title = "中试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody PilotApplication pilotApplication)
    {
        try {
            pilotApplication.setPortalUserId(SecurityUtils.getUserId());
            int rows = pilotApplicationService.updatePilotApplication(pilotApplication);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除中试申请
     */
    @Log(title = "中试申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        try {
            int rows = pilotApplicationService.deletePilotApplicationById(id, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 上传付款信息
     * 
     * @param pilotApplication
     * @return
     */
    @Log(title = "中试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/uploadPaymentVoucher")
    public AjaxResult uploadPayment(@RequestBody PilotApplication pilotApplication)
    {
        // 验证是否是申请人本人
        PilotApplication oldApplication = pilotApplicationService.selectPilotApplicationById(pilotApplication.getId());
        if (oldApplication == null || !oldApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权修改此申请");
        }

        // 验证申请状态是否为待付款
        if (!PilotApplication.STATUS_PENDING_PAYMENT.equals(oldApplication.getStatus())) {
            return AjaxResult.error("当前申请状态不允许上传付款信息");
        }

        // 验证付款凭证URL是否提供
        if (pilotApplication.getPaymentVoucher() == null || pilotApplication.getPaymentVoucher().trim().isEmpty()) {
            return AjaxResult.error("请提供付款凭证");
        }

        // 设置付款信息并更新付款状态为已上传凭证(待审核)
        oldApplication.setPaymentVoucher(pilotApplication.getPaymentVoucher());
        oldApplication.setPaymentStatus(PilotApplication.PAYMENT_STATUS_UPLOADED); // 已上传凭证，待审核
        oldApplication.setUpdateBy(SecurityUtils.getUsername());

        int rows = pilotApplicationService.updatePilotApplication(oldApplication);
        if (rows > 0) {
            return AjaxResult.success("付款凭证上传成功，请等待管理员审核确认");
        }
        return AjaxResult.error("付款凭证上传失败，请稍后重试");
    }

    /**
     * 取消中试申请，只有待审核、待付款的状态才可以取消。
     */
    @Log(title = "取消中试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable Long id)
    {
        // 验证是否是申请人本人
        PilotApplication oldApplication = pilotApplicationService.selectPilotApplicationById(id);
        if (oldApplication == null || !oldApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权取消此申请");
        }

        // 验证申请状态是否允许取消（待审核和待付款状态的申请可以取消）
        if (!PilotApplication.STATUS_PENDING_REVIEW.equals(oldApplication.getStatus()) 
            && !PilotApplication.STATUS_PENDING_PAYMENT.equals(oldApplication.getStatus())) {
            return AjaxResult.error("当前申请状态不允许取消，只有待审核和待付款状态的申请可以取消");
        }

        // 设置申请状态为已取消
        oldApplication.setStatus(PilotApplication.STATUS_CANCELLED);
        oldApplication.setUpdateBy(SecurityUtils.getUsername());

        int rows = pilotApplicationService.updatePilotApplication(oldApplication);
        if (rows > 0) {
            return AjaxResult.success("申请取消成功");
        }
        return AjaxResult.error("申请取消失败，请稍后重试");
    }
} 