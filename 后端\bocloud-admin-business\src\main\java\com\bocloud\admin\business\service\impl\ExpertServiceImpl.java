package com.bocloud.admin.business.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bocloud.admin.business.mapper.ExpertInfoMapper;
import com.bocloud.admin.business.service.IExpertService;
import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.ExpertPaper;
import com.bocloud.domain.web.ExpertWorkExperience;
import com.bocloud.admin.business.mapper.ExpertWorkExperienceMapper;
import com.bocloud.admin.business.mapper.ExpertPaperMapper;

@Service
public class ExpertServiceImpl implements IExpertService {

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private ExpertWorkExperienceMapper expertWorkExperienceMapper;

    @Autowired
    private ExpertPaperMapper expertPaperMapper;

    @Autowired
    private IMessageNotificationService messageNotificationService;

    @Override
    public List<ExpertInfo> list(ExpertInfo expertInfo) {
        return expertInfoMapper.selectExpertInfoList(expertInfo);
    }

    @Override
    public ExpertInfo getExpertDetailByExpertId(Long expertId) {
        ExpertInfo expertInfo = expertInfoMapper.selectExpertInfoByExpertId(expertId);
        if (expertInfo != null) {
            // 查询专家工作经历
            List<ExpertWorkExperience> expertWorkExperiences = expertWorkExperienceMapper.selectExpertWorkExperienceList(expertId);
            expertInfo.setExpertWorkExperiences(expertWorkExperiences);
            // 查询专家论文
            List<ExpertPaper> expertPapers = expertPaperMapper.selectExpertPaperList(expertId);
            expertInfo.setExpertPapers(expertPapers);
        }
        return expertInfo;
    }

    @Override
    public int updateExpertInfo(ExpertInfo expertInfo) {
        return expertInfoMapper.updateExpertInfo(expertInfo);
    }

    @Override
    public void auditExpert(ExpertInfo expertInfo) {
        updateExpertInfo(expertInfo);
        // 如果审核通过，发送审核通过的消息给专家
        if ("1".equals(expertInfo.getStatus())) {
            messageNotificationService.sendExpertAuditNotification(expertInfo.getUserId(), true);
        } else if ("-1".equals(expertInfo.getStatus())) {
            messageNotificationService.sendExpertAuditNotification(expertInfo.getUserId(), false);
        }
    }
}
