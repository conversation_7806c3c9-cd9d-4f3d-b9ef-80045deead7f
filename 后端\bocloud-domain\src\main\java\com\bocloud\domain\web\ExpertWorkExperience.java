package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 专家工作履历实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpertWorkExperience extends BaseEntity {
    /** 履历ID */
    private Long experienceId;
    
    /** 专家ID */
    private Long expertId;

    /** 用户ID */
    private Long userId;
    
    /** 开始时间 */
    private String startTime;
    
    /** 结束时间 */
    private String endTime;
    
    /** 单位名称 */
    private String companyName;
    
    /** 职位 */
    private String position;
    
    /** 工作内容 */
    private String description;
    
    /** 排序号 */
    private Integer sort;
    
    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;
}