package com.bocloud.portal.business.mapper;

import java.util.List;
import java.util.Map;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import org.apache.ibatis.annotations.Param;

/**
 * 企业需求Mapper接口
 * 
 * <AUTHOR>
 */
public interface EnterpriseDemandMapper 
{
    /**
     * 查询企业需求
     * 
     * @param demandId 企业需求主键
     * @return 企业需求
     */
    public EnterpriseDemand selectEnterpriseDemandById(Long demandId);

    /**
     * 查询企业需求列表
     * 
     * @param enterpriseDemand 企业需求
     * @return 企业需求集合
     */
    public List<EnterpriseDemand> selectEnterpriseDemandList(EnterpriseDemand enterpriseDemand);

    /**
     * 新增企业需求
     * 
     * @param enterpriseDemand 企业需求
     * @return 结果
     */
    public int insertEnterpriseDemand(EnterpriseDemand enterpriseDemand);

    /**
     * 修改企业需求
     * 
     * @param enterpriseDemand 企业需求
     * @return 结果
     */
    public int updateEnterpriseDemand(EnterpriseDemand enterpriseDemand);

    /**
     * 删除企业需求信息
     * 
     * @param demandId 企业需求ID
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteEnterpriseDemandById(@Param("demandId") Long demandId, @Param("userId") Long userId);

    /**
     * 查询需求对应的方案数量
     * 
     * @param demandId 需求ID
     * @return 方案数量
     */
    public int selectSolutionCountByDemandId(@Param("demandId") Long demandId);

    /**
     * 批量查询需求对应的方案数量
     * 
     * @param demandIds 需求ID列表
     * @return 需求ID和方案数量的映射
     */
    public List<Map<String, Object>> selectSolutionCountByDemandIds(@Param("demandIds") List<Long> demandIds);
} 