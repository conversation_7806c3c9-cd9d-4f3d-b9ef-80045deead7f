package com.bocloud.admin.business.mapper;

import java.util.List;
import com.bocloud.domain.system.demand.DemandContract;
import org.apache.ibatis.annotations.Mapper;

/**
 * 需求合同Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DemandContractMapper {
    /**
     * 查询需求合同列表
     * 
     * @param demandContract 需求合同信息
     * @return 需求合同集合
     */
    public List<DemandContract> selectDemandContractList(DemandContract demandContract);

    /**
     * 查询需求合同
     * 
     * @param contractId 需求合同ID
     * @return 需求合同信息
     */
    public DemandContract selectDemandContractById(Long contractId);

    /**
     * 修改需求合同
     * 
     * @param demandContract 需求合同信息
     * @return 结果
     */
    public int updateDemandContract(DemandContract demandContract);

    /**
     * 批量更新需求合同状态
     * 
     * @param contractIds 需要更新的合同ID数组
     * @param status 状态
     * @return 结果
     */
    public int updateDemandContractStatus(Long[] contractIds, String status);

    /**
     * 根据需求ID查询合同
     */
    DemandContract selectDemandContractByDemandId(Long demandId);
} 