package com.bocloud.admin.business.mapper;

import java.util.List;
import com.bocloud.domain.system.pilot_application.PilotApplication;

/**
 * 中试申请Mapper接口
 * 
 * <AUTHOR>
 */
public interface PilotApplicationMapper 
{
    /**
     * 查询中试申请
     * 
     * @param id 中试申请主键
     * @return 中试申请
     */
    public PilotApplication selectPilotApplicationById(Long id);

    /**
     * 查询中试申请列表
     * 
     * @param pilotApplication 中试申请
     * @return 中试申请集合
     */
    public List<PilotApplication> selectPilotApplicationList(PilotApplication pilotApplication);

    /**
     * 新增中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    public int insertPilotApplication(PilotApplication pilotApplication);

    /**
     * 修改中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    public int updatePilotApplication(PilotApplication pilotApplication);

    /**
     * 删除中试申请
     * 
     * @param id 中试申请主键
     * @return 结果
     */
    public int deletePilotApplicationById(Long id);

    /**
     * 批量删除中试申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePilotApplicationByIds(Long[] ids);
} 