package com.bocloud.portal.business.service;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.dto.WxPhoneLoginBody;

public interface IWxLoginService {
    /**
     * 生成微信登录二维码
     * @return 二维码信息
     */
    AjaxResult generateLoginQrCode();

    /**
     * 检查扫码状态
     * @param sceneStr 场景值
     * @return 登录状态
     */
    AjaxResult checkLoginStatus(String sceneStr);

    /**
     * 处理微信扫码事件
     * @param openId 用户openId
     * @param sceneStr 场景值
     * @param eventType 事件类型
     * @return 处理结果
     */
    void handleScanEvent(String openId, String sceneStr, String eventType);

    /**
     * 小程序手机授权登录
     * 
     * @param body
     * @return 
     */
    AjaxResult miniappPhoneLogin(WxPhoneLoginBody body);
} 