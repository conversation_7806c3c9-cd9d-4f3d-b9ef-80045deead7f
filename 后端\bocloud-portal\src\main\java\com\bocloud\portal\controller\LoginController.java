package com.bocloud.portal.controller;

import com.bocloud.common.annotation.Anonymous;
import com.bocloud.common.constant.Constants;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.domain.model.LoginBody;
import com.bocloud.portal.business.service.impl.LoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api("登陆")
public class LoginController
{
    @Autowired
    private LoginService loginService;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation("账户邮箱登陆")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }


    /**
     * 手机号登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation("手机号验证码登录")
    @ApiImplicitParam(name = "loginBody", value = "登录信息", dataType = "LoginBody")
    @PostMapping("/sms/login")
    public AjaxResult smsLogin(@RequestBody LoginBody loginBody)
    {
        String mobile = loginBody.getMobile();
        String smsCode = loginBody.getSmsCode();
        String smsUuid = loginBody.getSmsUuid();
        String openId = loginBody.getOpenId(); // 小程序openId（可选）
        
        log.info("[smsLogin] 收到短信验证码登录请求，mobile: {}, openId: {}", mobile, openId);
        
        AjaxResult ajax = loginService.smsLogin(mobile, smsCode, smsUuid, openId);
        return ajax;
    }





}
