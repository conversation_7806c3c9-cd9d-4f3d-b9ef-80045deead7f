package com.bocloud.common.enums;

/**
 * 通用状态枚举
 * 包含系统中通用的状态值
 * 
 * <AUTHOR>
 */
public enum CommonStatus {
    
    // 删除标志
    DEL_FLAG_NORMAL("0", "未删除"),
    DEL_FLAG_DELETED("1", "已删除"),
    
    // 是否标志
    YES("1", "是"),
    NO("0", "否"),
    
    // 启用/禁用状态
    ENABLE("1", "启用"),
    DISABLE("0", "禁用"),
    
    // 正常/异常状态
    NORMAL("1", "正常"),
    ABNORMAL("0", "异常");
    
    private final String code;
    private final String desc;
    
    CommonStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static CommonStatus getByCode(String code) {
        for (CommonStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        CommonStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断是否为未删除
     */
    public static boolean isNotDeleted(String code) {
        return DEL_FLAG_NORMAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为已删除
     */
    public static boolean isDeleted(String code) {
        return DEL_FLAG_DELETED.getCode().equals(code);
    }
    
    /**
     * 判断是否为是
     */
    public static boolean isYes(String code) {
        return YES.getCode().equals(code);
    }
    
    /**
     * 判断是否为否
     */
    public static boolean isNo(String code) {
        return NO.getCode().equals(code);
    }
    
    /**
     * 判断是否为启用
     */
    public static boolean isEnable(String code) {
        return ENABLE.getCode().equals(code);
    }
    
    /**
     * 判断是否为禁用
     */
    public static boolean isDisable(String code) {
        return DISABLE.getCode().equals(code);
    }
} 