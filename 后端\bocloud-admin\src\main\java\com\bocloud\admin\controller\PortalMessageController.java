package com.bocloud.admin.controller;

import com.bocloud.admin.business.service.IPortalMessageService;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import com.bocloud.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 站内消息控制器
 */
@RestController
@RequestMapping("/admin/message")
public class PortalMessageController extends BaseController {
    @Autowired
    private IPortalMessageService portalMessageService;

    /**
     * 获取消息列表
     */
    @PreAuthorize("@ss.hasPermi('admin:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(PortalMessageQuery query) {
        query.setBeginTime(DateUtils.setDayStartTime(query.getBeginTime()));
        query.setEndTime(DateUtils.setDayEndTime(query.getEndTime()));
        startPage();
        List<PortalMessage> list = portalMessageService.selectPortalMessageList(query);
        return getDataTable(list);
    }

    /**
     * 获取消息详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:message:query')")
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable("messageId") Long messageId) {
        return success(portalMessageService.selectPortalMessageById(messageId));
    }

    /**
     * 新增消息
     */
    @PreAuthorize("@ss.hasPermi('admin:message:add')")
    @Log(title = "站内消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PortalMessage message) {
        return toAjax(portalMessageService.insertPortalMessage(message));
    }

    /**
     * 修改消息
     */
    @PreAuthorize("@ss.hasPermi('admin:message:edit')")
    @Log(title = "站内消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PortalMessage message) {
        return toAjax(portalMessageService.updatePortalMessage(message));
    }

    /**
     * 删除消息
     */
    @PreAuthorize("@ss.hasPermi('admin:message:remove')")
    @Log(title = "站内消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageId}")
    public AjaxResult remove(@PathVariable Long messageId) {
        return toAjax(portalMessageService.deletePortalMessageById(messageId));
    }
} 