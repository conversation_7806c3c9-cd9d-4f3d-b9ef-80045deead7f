package com.bocloud.portal.business.service.impl;

import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.domain.system.CommonAttachment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bocloud.domain.system.test_application.TestApplication;
import com.bocloud.portal.business.mapper.TestApplicationMapper;
import com.bocloud.portal.business.service.ICommonAttachmentService;
import com.bocloud.portal.business.service.ITestApplicationService;

/**
 * 检测申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class TestApplicationServiceImpl implements ITestApplicationService 
{
    @Autowired
    private TestApplicationMapper testApplicationMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    /**
     * 查询检测申请
     * 
     * @param id 检测申请主键
     * @return 检测申请
     */
    @Override
    public TestApplication selectTestApplicationById(Long id)
    {
        TestApplication testApplication = testApplicationMapper.selectTestApplicationById(id);
        if (testApplication != null) {
            /// 查询附件列表
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(testApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
            testApplication.setAttachments(attachments);

            // 查询报告列表
            query = new CommonAttachment();
            query.setBusinessId(testApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_REPORT);
            List<CommonAttachment> reports = attachmentService.selectCommonAttachmentList(query);
            testApplication.setReports(reports);
        }
        return testApplication;
    }

    /**
     * 查询检测申请列表
     * 
     * @param testApplication 检测申请
     * @return 检测申请
     */
    @Override
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication)
    {
        return testApplicationMapper.selectTestApplicationList(testApplication);
    }

    /**
     * 新增检测申请
     * 
     * @param testApplication 检测申请
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTestApplication(TestApplication testApplication)
    {
        // 生成申请编号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = "TS" + dateStr;
        int seq = getNextApplicationSeq(prefix);
        testApplication.setApplicationId(String.format("%s%04d", prefix, seq));
        
        // 设置初始状态为待审核
        testApplication.setStatus("0");
        testApplication.setDelFlag("0");
        
        // 保存测试申请
        int rows = testApplicationMapper.insertTestApplication(testApplication);
        
        // 保存附件信息
        if (testApplication.getAttachments() != null && !testApplication.getAttachments().isEmpty()) {
            testApplication.getAttachments().forEach(attachment -> {
                attachment.setBusinessId(testApplication.getId());
                attachment.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
                attachmentService.insertCommonAttachment(attachment);
            });
        }
        
        return rows;
    }

    /**
     * 修改检测申请
     * 
     * @param testApplication 检测申请
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTestApplication(TestApplication testApplication)
    {
        // 只有当用户明确提供了新的附件时，才处理附件更新
        if (testApplication.getAttachments() != null) {
            // 删除原有附件
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(testApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
            List<CommonAttachment> oldAttachments = attachmentService.selectCommonAttachmentList(query);
            if (!oldAttachments.isEmpty()) {
                Long[] attachmentIds = oldAttachments.stream()
                    .map(CommonAttachment::getAttachmentId)
                    .toArray(Long[]::new);
                attachmentService.deleteCommonAttachmentByIds(attachmentIds);
            }
            
            // 保存新的附件信息
            if (!testApplication.getAttachments().isEmpty()) {
                testApplication.getAttachments().forEach(attachment -> {
                    attachment.setBusinessId(testApplication.getId());
                    attachment.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
                    attachmentService.insertCommonAttachment(attachment);
                });
            }
        }
        // 如果 attachments 为 null，说明用户没有修改附件，保持原有附件不变
        
        return testApplicationMapper.updateTestApplication(testApplication);
    }

    /**
     * 批量删除检测申请
     * 
     * @param ids 需要删除的检测申请主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTestApplicationById(Long id, Long userId)
    {
        // 获取申请信息
        TestApplication testApplication = selectTestApplicationById(id);
        if (testApplication == null) {
            throw new ServiceException("申请不存在");
        }
        // 验证是否是当前用户的申请
        if (!testApplication.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权删除该申请");
        }
        // 验证状态是否可以删除
        if (!TestApplication.STATUS_PENDING_REVIEW.equals(testApplication.getStatus())) {
            throw new ServiceException("只有待审核的申请可以删除");
        }

        // 删除附件
        CommonAttachment query = new CommonAttachment();
        query.setBusinessId(id);
        query.setBusinessType(CommonAttachmentBusinessType.TEST_APPLICATION_ATTACHMENT);
        List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
        if (!attachments.isEmpty()) {
            Long[] attachmentIds = attachments.stream()
                .map(CommonAttachment::getAttachmentId)
                .toArray(Long[]::new);
            attachmentService.deleteCommonAttachmentByIds(attachmentIds);
        }
        
        return testApplicationMapper.deleteTestApplicationById(id);
    }

    /**
     * 获取下一个申请序号
     * 使用synchronized确保并发安全
     * 
     * @param prefix 申请编号前缀
     * @return 下一个序号
     */
    @Override
    public synchronized int getNextApplicationSeq(String prefix) {
        // 查询当天最大的序号
        TestApplication query = new TestApplication();
        query.setApplicationId(prefix + "%");
        List<TestApplication> list = testApplicationMapper.selectTestApplicationList(query);
        int maxSeq = 0;
        if (list != null && !list.isEmpty()) {
            for (TestApplication app : list) {
                String appId = app.getApplicationId();
                if (appId != null && appId.startsWith(prefix)) {
                    try {
                        int seq = Integer.parseInt(appId.substring(prefix.length()));
                        maxSeq = Math.max(maxSeq, seq);
                    } catch (NumberFormatException e) {
                        // 忽略非数字序号
                    }
                }
            }
        }
        return maxSeq + 1;
    }
} 