package com.bocloud.admin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bocloud.admin.business.service.IVerificationRecordService;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.domain.web.VerificationRecord;

@RestController
@RequestMapping("/system/verification")
public class VerificationRecordController extends BaseController{

    @Autowired
    private IVerificationRecordService verificationRecordService;

    @GetMapping("/list")
    public TableDataInfo list(VerificationRecord verificationRecord) {
        startPage();
        List<VerificationRecord> list = verificationRecordService.selectVerificationRecordList(verificationRecord);
        return getDataTable(list);
    }
}
