package com.bocloud.portal.controller;

import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.RegisterUser;
import com.bocloud.domain.web.CompleteRegisterInfo;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.portal.business.service.impl.RegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 注册验证控制器
 */
@Slf4j
@Api("注册-重置密码")
@RestController
@RequestMapping("/register")
@Validated
public class RegisterController extends BaseController
{
    @Autowired
    private RegisterService registerService;

    @ApiOperation("手机号注册")
    @PostMapping("/smsRegister")
    public AjaxResult smsRegister(@RequestBody RegisterUser registerUser)
    {
        return registerService.smsRegister(registerUser);
    }

    @ApiOperation("完善注册信息")
    @PostMapping("/completeRegisterInfo")
    public AjaxResult completeRegisterInfo(@RequestBody @Valid CompleteRegisterInfo completeRegisterInfo)
    {
        try {
            registerService.completeRegisterInfo(completeRegisterInfo);
            return AjaxResult.success("注册信息完善成功");
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("完善注册信息失败", e);
            return AjaxResult.error("完善注册信息失败，请联系管理员");
        }
    }
}
