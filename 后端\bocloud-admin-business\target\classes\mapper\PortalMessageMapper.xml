<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PortalMessageMapper">
    
    <resultMap type="com.bocloud.domain.web.PortalMessage" id="PortalMessageResult">
        <id     property="messageId"    column="message_id"    />
        <result property="title"        column="title"        />
        <result property="content"      column="content"      />
        <result property="type"         column="type"         />
        <result property="senderId"     column="sender_id"    />
        <result property="receiverId"   column="receiver_id"  />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="delFlag"      column="del_flag"     />
    </resultMap>

    <sql id="selectPortalMessageVo">
        select m.message_id, m.title, m.content, m.type, m.sender_id, m.receiver_id, 
               m.create_by, m.create_time, m.update_by, m.update_time, m.del_flag
        from portal_message m
    </sql>

    <select id="selectPortalMessageList" parameterType="PortalMessageQuery" resultMap="PortalMessageResult">
        <include refid="selectPortalMessageVo"/>
        <where>
            <if test="title != null and title != ''">
                AND m.title like concat('%', #{title}, '%')
            </if>
            <if test="type != null and type != ''">
                AND m.type = #{type}
            </if>
            <if test="beginTime != null">
                AND m.create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND m.create_time &lt;= #{endTime}
            </if>
            AND m.del_flag = '0'
        </where>
        order by m.create_time desc
    </select>

    <select id="selectPortalMessageById" parameterType="Long" resultMap="PortalMessageResult">
        <include refid="selectPortalMessageVo"/>
        where m.message_id = #{messageId} and m.del_flag = '0'
    </select>

    <insert id="insertPortalMessage" parameterType="PortalMessage" useGeneratedKeys="true" keyProperty="messageId">
        insert into portal_message(
            title, content, type, sender_id, receiver_id, 
            create_by, create_time, update_by, update_time, del_flag
        )values(
            #{title}, #{content}, #{type}, #{senderId}, #{receiverId},
            #{createBy}, sysdate(), #{updateBy}, #{updateTime}, '0'
        )
    </insert>

    <update id="updatePortalMessage" parameterType="PortalMessage">
        update portal_message
        <set>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where message_id = #{messageId}
    </update>

    <update id="deletePortalMessageById" parameterType="Long">
        update portal_message set del_flag = '1' where message_id = #{messageId}
    </update>
</mapper> 