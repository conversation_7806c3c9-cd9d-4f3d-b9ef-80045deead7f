package com.bocloud.framework.filters;

import com.alibaba.fastjson2.JSON;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.framework.util.JsonUtil;
import com.bocloud.framework.util.RSAUtil;
import com.bocloud.framework.util.SignUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Controller 统一返回包装
 */
@RestControllerAdvice
@Component
@Slf4j
public class ResBodyAdvice implements ResponseBodyAdvice<Object> {

    //时间戳
    private static final String TIMESTAMP_KEY = "timeStamp";

    //随机字符串
    private static final String RAND_KEY = "randStr";

    //签名值
    private static final String SIGN_KEY = "sign";

    @Value("${encrypt}")
    private boolean encrypt;
    @Value("${signKey:}")
    private String secret;
    @Value("${publickey}")
    private String publickey;

    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        if(!encrypt || (methodParameter.getMethodAnnotation(PostMapping.class)!=null && Arrays.stream(methodParameter.getMethodAnnotation(PostMapping.class).path()).collect(Collectors.toList()).contains("/check"))){
            return false;
        }
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
                                  ServerHttpResponse response) {
        // 此处获取到request 为特殊需要的时候处理使用
		HttpServletRequest httpServletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        String reqMethod = httpServletRequest.getMethod();
        String uri = httpServletRequest.getRequestURI();
        //如果是rest接口统一封装返回对象
        if (body instanceof AjaxResult) {
            AjaxResult res = (AjaxResult) body;
            //如果返回成功
            if (!ObjectUtils.isEmpty(res.get("code"))) {
                Object data = res;
                if (null != data) {
                    JsonNode json = JsonUtil.beanToNode(data);
                    //仅处理object类型
                    if (json.isObject()) {
                        TreeMap<String, String> map = new TreeMap<>();
                        Iterator<Map.Entry<String, JsonNode>> fields = json.fields();
                        while(fields.hasNext()){
                            Map.Entry<String, JsonNode> entry = fields.next();
                            map.put(entry.getKey(), JsonUtil.toStr(entry.getValue()));
                        }
                        SignUtil.sign(map, secret);
                        res.put(TIMESTAMP_KEY,map.get(TIMESTAMP_KEY));
                        res.put(RAND_KEY,map.get(RAND_KEY));
                        res.put(SIGN_KEY,map.get(SIGN_KEY));
                        try {
                           String resdata = RSAUtil.encrypt(JSON.toJSONString(res),publickey);
                           log.info("加密后的:{}",resdata);
                           return AjaxResult.success("",resdata);
                        }catch (Exception e){
                            return AjaxResult.error("加密失败");
                        }
                    }
                }
            }
        }
        return body;
    }

}