package com.bocloud.admin.business.mapper;

import java.util.List;

import com.bocloud.domain.web.ExpertInfo;

/**
 * 专家信息Mapper接口
 */
public interface ExpertInfoMapper {
    /**
     * 查询专家信息列表
     * 
     * @param expertInfo 专家信息
     * @return 专家信息集合
     */
    List<ExpertInfo> selectExpertInfoList(ExpertInfo expertInfo);

    /**
     * 根据用户ID查询专家信息
     * 
     * @param userId 用户ID
     * @return 专家信息
     */
    ExpertInfo selectExpertInfoByUserId(Long userId);

    /**
     * 根据专家ID查询专家详情
     *
     * @param expertId 专家ID
     * @return 专家信息
     */
    ExpertInfo selectExpertInfoByExpertId(Long expertId);

    /**
     * 更新专家信息
     * 
     * @param expertInfo 专家信息
     * @return 结果
     */
    int updateExpertInfo(ExpertInfo expertInfo);
} 