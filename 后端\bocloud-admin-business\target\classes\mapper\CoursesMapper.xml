<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.CoursesMapper">

    <resultMap type="Courses" id="CoursesResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="description"    column="description"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="status"    column="status"    />
        <result property="price"    column="price"    />
        <result property="views"    column="views"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCoursesVo">
        select c.course_id, c.course_name, c.category_id, c.description, c.creator_id, c.status, c.price, c.views, 
               c.create_by, c.create_time, c.update_by, c.update_time, p.real_name as creator_name
        from courses c
        left join portal_user_personal p on c.creator_id = p.user_id
    </sql>

    <select id="selectCoursesList" parameterType="Courses" resultMap="CoursesResult">
        <include refid="selectCoursesVo"/>
        <where>
            <if test="courseName != null  and courseName != ''"> and course_name = #{courseName}</if>
            <if test="categoryId != null and categoryId != ''"> and category_id = #{categoryId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="creatorId != null  and creatorId != ''"> and creator_id = #{creatorId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="views != null "> and views = #{views}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCoursesByCourseId" parameterType="Integer" resultMap="CoursesResult">
        <include refid="selectCoursesVo"/>
        where course_id = #{courseId}
    </select>

    <insert id="insertCourses" parameterType="Courses" useGeneratedKeys="true" keyProperty="courseId">
        insert into courses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="description != null">description,</if>
            <if test="creatorId != null and creatorId != ''">creator_id,</if>
            <if test="status != null">status,</if>
            <if test="price != null">price,</if>
            <if test="views != null">views,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="description != null">#{description},</if>
            <if test="creatorId != null and creatorId != ''">#{creatorId},</if>
            <if test="status != null">#{status},</if>
            <if test="price != null">#{price},</if>
            <if test="views != null">#{views},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourses" parameterType="Courses">
        update courses
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="creatorId != null and creatorId != ''">creator_id = #{creatorId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="price != null">price = #{price},</if>
            <if test="views != null">views = #{views},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteCoursesByCourseId" parameterType="Integer">
        delete from courses where course_id = #{courseId}
    </delete>

    <delete id="deleteCoursesByCourseIds" parameterType="String">
        delete from courses where course_id in
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>
</mapper>