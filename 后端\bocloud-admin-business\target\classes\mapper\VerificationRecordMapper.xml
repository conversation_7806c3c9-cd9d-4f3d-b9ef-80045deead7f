<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.VerificationRecordMapper">
    
    <resultMap type="com.bocloud.domain.web.VerificationRecord" id="VerificationRecordResult">
        <id     property="id"              column="id"                />
        <result property="userId"          column="user_id"           />
        <result property="name"            column="name"              />
        <result property="idCardNumber"    column="id_card_number"    />
        <result property="verificationType" column="verification_type"/>
        <result property="status"          column="status"            />
        <result property="rejectReason"    column="reject_reason"     />
        <result property="legalPersonName" column="legal_person_name" />
        <result property="creditCode" column="credit_code" />
        <result property="createTime"      column="create_time"       />
        <result property="finishTime"      column="finish_time"       />
        <result property="delFlag"         column="del_flag"          />
    </resultMap>
    
    <sql id="selectVerificationRecordVo">
        select id, user_id, name, id_card_number, verification_type, status, reject_reason, legal_person_name, credit_code, create_time, finish_time, del_flag
        from verification_record
    </sql>
    
    <select id="selectVerificationRecordByUserIdAndStatus" resultMap="VerificationRecordResult">
        <include refid="selectVerificationRecordVo"/>
        where user_id = #{userId} and status = #{status} and del_flag = '0' order by create_time desc limit 1
    </select>
        
    <update id="updateVerificationRecord" parameterType="com.bocloud.domain.web.VerificationRecord">
        update verification_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="idCardNumber != null">id_card_number = #{idCardNumber},</if>
            <if test="status != null">status = #{status},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="legalPersonName != null">legal_person_name = #{legalPersonName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectVerificationRecordList" resultMap="VerificationRecordResult">
        <include refid="selectVerificationRecordVo"/>
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="idCardNumber != null and idCardNumber != ''">
                and id_card_number like concat('%', #{idCardNumber}, '%')
            </if>
            <if test="verificationType != null and verificationType != ''">
                and verification_type = #{verificationType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper> 