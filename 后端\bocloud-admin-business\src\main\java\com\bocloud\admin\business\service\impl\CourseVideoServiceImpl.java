package com.bocloud.admin.business.service.impl;

import java.util.List;

import com.bocloud.admin.business.mapper.CourseVideoMapper;
import com.bocloud.admin.business.service.ICourseVideoService;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.domain.system.online_education.CourseVideo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 课程视频Service业务层处理
 *
 * @date 2025-03-18
 */
@Service
public class CourseVideoServiceImpl implements ICourseVideoService
{
    @Autowired
    private CourseVideoMapper courseVideoMapper;

    /**
     * 查询课程视频
     *
     * @param videoId 课程视频主键
     * @return 课程视频
     */
    @Override
    public CourseVideo selectCourseVideoByVideoId(Integer videoId)
    {
        return courseVideoMapper.selectCourseVideoByVideoId(videoId);
    }

    /**
     * 查询课程视频列表
     *
     * @param courseVideo 课程视频
     * @return 课程视频
     */
    @Override
    public List<CourseVideo> selectCourseVideoList(CourseVideo courseVideo)
    {
        return courseVideoMapper.selectCourseVideoList(courseVideo);
    }

    /**
     * 新增课程视频
     *
     * @param courseVideo 课程视频
     * @return 结果
     */
    @Override
    public int insertCourseVideo(CourseVideo courseVideo)
    {
        courseVideo.setCreateTime(DateUtils.getNowDate());
        return courseVideoMapper.insertCourseVideo(courseVideo);
    }

    /**
     * 修改课程视频
     *
     * @param courseVideo 课程视频
     * @return 结果
     */
    @Override
    public int updateCourseVideo(CourseVideo courseVideo)
    {
        courseVideo.setUpdateTime(DateUtils.getNowDate());
        return courseVideoMapper.updateCourseVideo(courseVideo);
    }

    /**
     * 批量删除课程视频
     *
     * @param videoIds 需要删除的课程视频主键
     * @return 结果
     */
    @Override
    public int deleteCourseVideoByVideoIds(Integer[] videoIds)
    {
        return courseVideoMapper.deleteCourseVideoByVideoIds(videoIds);
    }

    /**
     * 删除课程视频信息
     *
     * @param videoId 课程视频主键
     * @return 结果
     */
    @Override
    public int deleteCourseVideoByVideoId(Integer videoId)
    {
        return courseVideoMapper.deleteCourseVideoByVideoId(videoId);
    }
} 