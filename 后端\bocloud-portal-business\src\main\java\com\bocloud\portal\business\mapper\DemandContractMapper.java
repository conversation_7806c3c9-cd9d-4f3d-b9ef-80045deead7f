package com.bocloud.portal.business.mapper;

import java.util.List;
import com.bocloud.domain.system.demand.DemandContract;

/**
 * 需求合同Mapper接口
 * 
 * <AUTHOR>
 */
public interface DemandContractMapper 
{
    /**
     * 查询需求合同
     * 
     * @param contractId 需求合同主键
     * @return 需求合同
     */
    public DemandContract selectDemandContractById(Long contractId);

    /**
     * 查询需求合同列表
     * 
     * @param demandContract 需求合同
     * @return 需求合同集合
     */
    public List<DemandContract> selectDemandContractList(DemandContract demandContract);

    /**
     * 新增需求合同
     * 
     * @param demandContract 需求合同
     * @return 结果
     */
    public int insertDemandContract(DemandContract demandContract);

    /**
     * 修改需求合同
     * 
     * @param demandContract 需求合同
     * @return 结果
     */
    public int updateDemandContract(DemandContract demandContract);

    /**
     * 删除需求合同
     * 
     * @param contractId 需求合同主键
     * @return 结果
     */
    public int deleteDemandContractById(Long contractId);

    /**
     * 根据需求ID查询合同
     * 
     * @param demandId 需求ID
     * @return 需求合同
     */
    public DemandContract selectDemandContractByDemandId(Long demandId);

    /**
     * 查询所有合同
     */
    List<DemandContract> selectAllContracts();
} 