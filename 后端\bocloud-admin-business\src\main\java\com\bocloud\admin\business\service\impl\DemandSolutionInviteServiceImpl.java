package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.DemandSolutionInviteMapper;
import com.bocloud.admin.business.mapper.EnterpriseDemandMapper;
import com.bocloud.admin.business.mapper.ExpertInfoMapper;
import com.bocloud.admin.business.service.IDemandSolutionInviteService;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.enums.ExpertStatusEnum;
import com.bocloud.domain.web.dto.SingleInviteRequest;
import com.bocloud.domain.web.BatchInviteRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 需求解决方案邀请Service实现
 */
@Service
public class DemandSolutionInviteServiceImpl implements IDemandSolutionInviteService {

    @Autowired
    private DemandSolutionInviteMapper demandSolutionInviteMapper;

    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Override
    public DemandSolutionInvite selectDemandSolutionInviteById(Long id) {
        return demandSolutionInviteMapper.selectDemandSolutionInviteById(id);
    }

    @Override
    public List<DemandSolutionInvite> selectDemandSolutionInviteList(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.selectDemandSolutionInviteList(invite);
    }

    @Override
    public int insertDemandSolutionInvite(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.insertDemandSolutionInvite(invite);
    }

    @Override
    public int updateDemandSolutionInvite(DemandSolutionInvite invite) {
        return demandSolutionInviteMapper.updateDemandSolutionInvite(invite);
    }

    @Override
    public int batchInsertDemandSolutionInvite(List<DemandSolutionInvite> inviteList) {
        return demandSolutionInviteMapper.batchInsertDemandSolutionInvite(inviteList);
    }

    @Override
    public boolean validateDemandExists(Long demandId) {
        if (demandId == null) {
            return false;
        }
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        return demand != null && "0".equals(demand.getDelFlag());
    }

    @Override
    public boolean validateExpertExists(Long userId) {
        if (userId == null) {
            return false;
        }
        ExpertInfo expertInfo = expertInfoMapper.selectExpertInfoByUserId(userId);
        return expertInfo != null && ExpertStatusEnum.isNormal(expertInfo.getStatus());
    }

    @Override
    public boolean checkInviteExists(Long demandId, Long userId) {
        if (demandId == null || userId == null) {
            return false;
        }
        DemandSolutionInvite query = new DemandSolutionInvite();
        query.setDemandId(demandId);
        query.setInvitedUserId(userId);
        List<DemandSolutionInvite> existingInvites = demandSolutionInviteMapper.selectDemandSolutionInviteList(query);
        return !existingInvites.isEmpty();
    }

    @Override
    public List<Long> getAlreadyInvitedUsers(Long demandId, List<Long> userIds) {
        if (demandId == null || userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        return userIds.stream()
                .filter(userId -> checkInviteExists(demandId, userId))
                .collect(Collectors.toList());
    }

    @Override
    public DemandSolutionInvite inviteSingleExpert(SingleInviteRequest request, Long operatorId, String operatorName) {
        // 验证需求是否存在
        if (!validateDemandExists(request.getDemandId())) {
            throw new ServiceException("需求不存在或已被删除");
        }
        
        // 验证专家是否存在且状态正常
        if (!validateExpertExists(request.getInvitedUserId())) {
            throw new ServiceException("专家不存在或状态异常");
        }
        
        // 检查是否已经邀请过该专家
        if (checkInviteExists(request.getDemandId(), request.getInvitedUserId())) {
            throw new ServiceException("该专家已被邀请，请勿重复邀请");
        }
        
        // 创建邀请记录
        DemandSolutionInvite invite = new DemandSolutionInvite();
        invite.setDemandId(request.getDemandId());
        invite.setInvitedUserId(request.getInvitedUserId());
        invite.setInviteStatus(DemandSolutionInvite.STATUS_PENDING);
        invite.setInviteTime(new Date());
        invite.setOperatorId(operatorId);
        invite.setRemark(request.getRemark());
        invite.setCreateBy(operatorName);
        invite.setCreateTime(new Date());
        invite.setUpdateBy(operatorName);
        invite.setUpdateTime(new Date());
        
        int rows = insertDemandSolutionInvite(invite);
        if (rows <= 0) {
            throw new ServiceException("邀请发送失败");
        }
        
        return invite;
    }

    @Override
    public int batchInviteExperts(BatchInviteRequest request, Long operatorId, String operatorName) {
        // 验证需求是否存在
        if (!validateDemandExists(request.getDemandId())) {
            throw new ServiceException("需求不存在或已被删除");
        }
        
        // 验证所有专家是否存在且状态正常
        for (Long userId : request.getUserIds()) {
            if (!validateExpertExists(userId)) {
                throw new ServiceException("专家用户ID " + userId + " 不存在或状态异常");
            }
        }
        
        // 检查是否有重复邀请
        /* List<Long> alreadyInvitedUsers = getAlreadyInvitedUsers(request.getDemandId(), request.getUserIds());
        if (!alreadyInvitedUsers.isEmpty()) {
            throw new ServiceException("以下专家已被邀请，请勿重复邀请: " + alreadyInvitedUsers);
        } */
        
        // 创建邀请记录列表
        List<DemandSolutionInvite> inviteList = new ArrayList<>();
        Date now = new Date();
        
        for (Long userId : request.getUserIds()) {
            DemandSolutionInvite invite = new DemandSolutionInvite();
            invite.setDemandId(request.getDemandId());
            invite.setInvitedUserId(userId);
            invite.setInviteStatus(DemandSolutionInvite.STATUS_PENDING);
            invite.setInviteTime(now);
            invite.setOperatorId(operatorId);
            invite.setRemark(request.getRemark());
            invite.setCreateBy(operatorName);
            invite.setCreateTime(now);
            invite.setUpdateBy(operatorName);
            invite.setUpdateTime(now);
            inviteList.add(invite);
        }
        
        int rows = batchInsertDemandSolutionInvite(inviteList);
        if (rows <= 0) {
            throw new ServiceException("批量邀请发送失败");
        }
        
        return rows;
    }
} 