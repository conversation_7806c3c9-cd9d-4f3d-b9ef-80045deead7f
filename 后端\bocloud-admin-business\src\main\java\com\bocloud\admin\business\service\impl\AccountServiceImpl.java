package com.bocloud.admin.business.service.impl;

import com.bocloud.common.enums.*;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.domain.web.PersonalInfo;
import com.bocloud.admin.business.mapper.PortalUserMapper;
import com.bocloud.admin.business.mapper.EnterpriseInfoMapper;
import com.bocloud.admin.business.service.IAccountService;
import com.bocloud.admin.business.service.IPortalUserPersonalInfoService;
import com.bocloud.admin.business.service.IVerificationRecordService;
import com.bocloud.common.exception.ServiceException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 账号管理服务实现类
 */
@Service
@Slf4j
public class AccountServiceImpl implements IAccountService {

    @Autowired
    private PortalUserMapper portalUserMapper;

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    private IPortalUserPersonalInfoService personalInfoService;

    @Autowired
    private IVerificationRecordService verificationStatusService;

    /**
     * 获取账号列表
     */
    @Override
    public List<PortalUser> list(PortalUser portalUser) {
        // 查询账号列表
        return portalUserMapper.selectPortalUserList(portalUser);
    }

    /**
     * 获取会员详细信息
     */
    @Override
    public PortalUser getInfo(Long userId) {
        PortalUser user = portalUserMapper.selectPortalUserById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        String type = user.getType();
        if (UserType.ENTERPRISE.getCode().equals(type)) {
            // 企业账号，填充企业信息
            EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectActiveByUserId(userId);
            user.setEnterpriseInfo(enterpriseInfo);
            user.setPersonalInfo(null);
        } else if (UserType.PERSONAL.getCode().equals(type)) {
            // 个人账号，填充个人信息
            PersonalInfo personalInfo = personalInfoService.selectBestPersonalInfoByUserId(userId);
            user.setPersonalInfo(personalInfo);
            user.setEnterpriseInfo(null);
        } else {
            // 其他类型，默认只返回基本信息
            user.setPersonalInfo(null);
            user.setEnterpriseInfo(null);
        }
        return user;
    }

    /**
     * 审核实名信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int audit(Long userId, String status, String remark) {
        PortalUser portalUser = portalUserMapper.selectPortalUserById(userId);
        if (portalUser == null) {
            throw new ServiceException("用户不存在");
        }

        int rows = 0;

        // 设置用户信息状态
        if (AuditStatus.PASSED.getCode().equals(status)) {
            // 审核通过
            if (VerificationType.PERSONAL.getCode().equals(portalUser.getVerificationType())) {
                portalUser.setType(UserType.PERSONAL.getCode());
            } else if (VerificationType.ENTERPRISE.getCode().equals(portalUser.getVerificationType())) {
                portalUser.setType(UserType.ENTERPRISE.getCode());
            }
            portalUser.setStatus(AccountStatus.NORMAL.getCode());
            portalUser.setVerificationType(portalUser.getVerificationType());
            portalUser.setVerificationStatus(VerificationStatus.APPROVED.getCode());
            portalUser.setAuditComment(remark);
            rows = portalUserMapper.updatePortalUser(portalUser);

        } else {
            // 审核失败
            portalUser.setVerificationStatus(VerificationStatus.REJECTED.getCode());
            portalUser.setAuditComment(remark);
            rows = portalUserMapper.updatePortalUser(portalUser);
        }

        // 处理VerificationStatus的审核记录
        VerificationRecord verificationRecord = verificationStatusService
                .selectVerificationRecordByUserIdAndStatus(userId, VerificationStatus.REVIEWING.getCode());

        if (AuditStatus.PASSED.getCode().equals(status)) {
            // 审核通过
            verificationRecord.setStatus(VerificationStatus.APPROVED.getCode());
            verificationRecord.setFinishTime(new Date());
            verificationRecord.setRejectReason(remark);
            verificationStatusService.updateVerificationRecord(verificationRecord);
        } else {
            // 审核失败
            verificationRecord.setStatus(VerificationStatus.REJECTED.getCode());
            verificationRecord.setRejectReason(remark);
            verificationRecord.setFinishTime(new Date());
            verificationStatusService.updateVerificationRecord(verificationRecord);
        }

        if (VerificationType.PERSONAL.getCode().equals(portalUser.getVerificationType())) {
            // 个人认证
            // 处理个人信息表
            PersonalInfo personalInfo = personalInfoService.selectPendingPersonalInfoByUserId(userId);
            if (AuditStatus.PASSED.getCode().equals(status)) {
                // 审核通过
                personalInfo.setStatus(PersonalInfoStatus.STATUS_NORMAL.getCode());
                personalInfo.setActive(PersonalInfoStatus.ACTIVE_YES.getCode());
                personalInfoService.updatePortalUserPersonalInfo(personalInfo);

                // 老的认证过的记录，active设置为"0"
                PersonalInfo oldPersonalInfo = personalInfoService.selectActiveByUserId(userId);
                if (oldPersonalInfo != null) {
                    oldPersonalInfo.setActive(PersonalInfoStatus.ACTIVE_NO.getCode());
                    personalInfoService.updatePortalUserPersonalInfo(oldPersonalInfo);
                }

            } else {
                // 审核失败
                log.info("审核失败,userId:{}, 名字:{}", userId, personalInfo.getRealName());
                personalInfo.setStatus(PersonalInfoStatus.STATUS_REJECTED.getCode());
                personalInfoService.updatePortalUserPersonalInfo(personalInfo);
            }
        } else if (VerificationType.ENTERPRISE.getCode().equals(portalUser.getVerificationType())) {
            // 处理企业信息表
            EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, EnterpriseInfoStatus.STATUS_PENDING.getCode());
            if (AuditStatus.PASSED.getCode().equals(status)) {
                // 审核通过
                enterpriseInfo.setStatus(EnterpriseInfoStatus.STATUS_NORMAL.getCode());
                enterpriseInfo.setActive(EnterpriseInfoStatus.ACTIVE_YES.getCode());
                enterpriseInfoMapper.updateEnterpriseInfo(enterpriseInfo);

                // 认证过的记录，active设置为"0"
                EnterpriseInfo enterpriseInfo2 = enterpriseInfoMapper.selectActiveByUserId(userId);
                enterpriseInfo2.setActive(EnterpriseInfoStatus.ACTIVE_NO.getCode());
                enterpriseInfoMapper.updateEnterpriseInfo(enterpriseInfo2);

                // 同步企业关键信息到认证记录
                verificationRecord.setLegalPersonName(enterpriseInfo.getLegalPersonName());
                verificationRecord.setCreditCode(enterpriseInfo.getCreditCode());
                verificationStatusService.updateVerificationRecord(verificationRecord);
            } else {
                // 审核失败
                enterpriseInfo.setStatus(EnterpriseInfoStatus.STATUS_REJECTED.getCode());
                enterpriseInfoMapper.updateEnterpriseInfo(enterpriseInfo);
            }
        }

        return rows;
    }

    @Override
    public List<PortalUser> expertList(ExpertInfo expertInfo) {
        // 创建查询条件
        PortalUser portalUser = new PortalUser();
        portalUser.setType("3"); // 设置用户类型为专家用户

        // 查询专家用户列表
        List<PortalUser> list = portalUserMapper.selectExpertList(portalUser, expertInfo);

        return list;
    }

    /**
     * 根据用户ID，获取用户待审核信息
     * @param userId 用户ID
     * @return 待审核信息
     */
    @Override
    public Map<String, Object> getPendingInfo(Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 获取用户基本信息
        PortalUser user = portalUserMapper.selectPortalUserById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        result.put("user", user);
        
        // 2. 根据用户类型获取待审核信息
        if (VerificationType.PERSONAL.getCode().equals(user.getVerificationType())) {
            // 个人认证
            PersonalInfo personalInfo = personalInfoService.selectLatestByUserIdAndStatus(userId, PersonalInfoStatus.STATUS_PENDING.getCode());
            if (personalInfo != null) {
                result.put("type", VerificationType.PERSONAL.getCode());
                result.put("info", personalInfo);
            }
        } else if (VerificationType.ENTERPRISE.getCode().equals(user.getVerificationType())) {
            // 企业认证
            EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectLatestByUserIdAndStatus(userId, EnterpriseInfoStatus.STATUS_PENDING.getCode());
            if (enterpriseInfo != null) {
                result.put("type", VerificationType.ENTERPRISE.getCode());
                result.put("info", enterpriseInfo);
            }
        }
        
        return result;
    }

    /**
     * 获取所有待审核的认证信息列表
     * @param type 认证类型：personal-个人认证，enterprise-企业认证，不传则查询所有
     * @return 待审核信息列表
     */
    @Override
    public List<Map<String, Object>> getPendingList(String type) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 查询个人认证待审核信息
        if (type == null || "personal".equals(type)) {
            List<PersonalInfo> personalInfoList = personalInfoService.selectListByStatus(PersonalInfoStatus.STATUS_PENDING.getCode());
            for (PersonalInfo personalInfo : personalInfoList) {
                Map<String, Object> item = new HashMap<>();
                PortalUser user = portalUserMapper.selectPortalUserById(personalInfo.getUserId());
                item.put("user", user);
                item.put("type", VerificationType.PERSONAL.getCode());
                item.put("info", personalInfo);
                item.put("submitTime", personalInfo.getCreateTime());
                resultList.add(item);
            }
        }
        
        // 查询企业认证待审核信息
        if (type == null || "enterprise".equals(type)) {
            List<EnterpriseInfo> enterpriseInfoList = enterpriseInfoMapper.selectListByStatus(EnterpriseInfoStatus.STATUS_PENDING.getCode());
            for (EnterpriseInfo enterpriseInfo : enterpriseInfoList) {
                Map<String, Object> item = new HashMap<>();
                PortalUser user = portalUserMapper.selectPortalUserById(enterpriseInfo.getUserId());
                item.put("user", user);
                item.put("type", VerificationType.ENTERPRISE.getCode());
                item.put("info", enterpriseInfo);
                item.put("submitTime", enterpriseInfo.getCreateTime());
                resultList.add(item);
            }
        }
        
        // 按提交时间倒序排列
        resultList.sort((a, b) -> {
            Date dateA = (Date) a.get("submitTime");
            Date dateB = (Date) b.get("submitTime");
            return dateB.compareTo(dateA);
        });
        
        return resultList;
    }
}