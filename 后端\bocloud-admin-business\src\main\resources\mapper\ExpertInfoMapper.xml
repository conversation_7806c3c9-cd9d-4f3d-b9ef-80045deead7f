<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.ExpertInfoMapper">
    
    <resultMap type="com.bocloud.domain.web.ExpertInfo" id="ExpertInfoResult">
        <id     property="expertId"           column="expert_id"           />
        <result property="userId"             column="user_id"             />
        <result property="expertName"         column="expert_name"         />
        <result property="avatar"             column="avatar"              />
        <result property="expertType"         column="expert_type"         />
        <result property="title"              column="title"               />
        <result property="researchField"      column="research_field"      />
        <result property="organization"       column="organization"        />
        <result property="department"         column="department"          />
        <result property="researchDirection"  column="research_direction"  />
        <result property="keywords"           column="keywords"            />
        <result property="introduction"       column="introduction"        />
        <result property="status"             column="status"              />
        <result property="delFlag"            column="del_flag"            />
        <result property="createBy"           column="create_by"           />
        <result property="createTime"         column="create_time"         />
        <result property="updateBy"           column="update_by"           />
        <result property="updateTime"         column="update_time"         />
    </resultMap>

    <sql id="selectExpertInfoVo">
        select expert_id, user_id, expert_name, avatar, expert_type, title, research_field, organization, department, research_direction, keywords, introduction, status, del_flag, create_by, create_time, update_by, update_time
        from expert_info
    </sql>

    <select id="selectExpertInfoList" parameterType="ExpertInfo" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        <where>
            <if test="expertName != null and expertName != ''">and expert_name like concat('%', #{expertName}, '%')</if>
            <if test="expertType != null and expertType != ''">and expert_type = #{expertType}</if>
            <if test="researchField != null and researchField != ''">and research_field = #{researchField}</if>
            <if test="organization != null and organization != ''">and organization = #{organization}</if>
            <if test="department != null and department != ''">and department = #{department}</if>
            <if test="researchDirection != null and researchDirection != ''">and research_direction = #{researchDirection}</if>
            <if test="keywords != null and keywords != ''">and keywords like concat('%', #{keywords}, '%')</if>
            <if test="introduction != null and introduction != ''">and introduction = #{introduction}</if>
            <if test="status != null and status != ''">and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectExpertInfoByUserId" parameterType="Long" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        where user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectExpertInfoByExpertId" parameterType="Long" resultMap="ExpertInfoResult">
        <include refid="selectExpertInfoVo"/>
        where expert_id = #{expertId} and del_flag = '0'
    </select>

    <update id="updateExpertInfo" parameterType="ExpertInfo">
        update expert_info
        <set>
            <if test="expertType != null and expertType != ''">expert_type = #{expertType},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="researchField != null and researchField != ''">research_field = #{researchField},</if>
            <if test="organization != null and organization != ''">organization = #{organization},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="researchDirection != null and researchDirection != ''">research_direction = #{researchDirection},</if>
            <if test="keywords != null and keywords != ''">keywords = #{keywords},</if>
            <if test="introduction != null and introduction != ''">introduction = #{introduction},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        where expert_id = #{expertId}
    </update>

</mapper> 