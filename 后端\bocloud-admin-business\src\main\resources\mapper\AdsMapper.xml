<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.AdsMapper">

    <resultMap type="Ads" id="AdsResult">
        <result property="adId"    column="ad_id"    />
        <result property="userId"    column="user_id"    />
        <result property="positionId"    column="position_id"    />
        <result property="content"    column="content"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="budget"    column="budget"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAdsVo">
        select ad_id, user_id, position_id, content, image_url, video_url, budget, start_date, end_date, status, create_by, create_time, update_by, update_time from ads
    </sql>

    <select id="selectAdsList" parameterType="Ads" resultMap="AdsResult">
        <include refid="selectAdsVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="positionId != null "> and position_id = #{positionId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
            <if test="budget != null "> and budget = #{budget}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectAdsByAdId" parameterType="Long" resultMap="AdsResult">
        <include refid="selectAdsVo"/>
        where ad_id = #{adId}
    </select>

    <insert id="insertAds" parameterType="Ads" useGeneratedKeys="true" keyProperty="adId">
        insert into ads
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="positionId != null">position_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="budget != null">budget,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="positionId != null">#{positionId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="budget != null">#{budget},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAds" parameterType="Ads">
        update ads
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="budget != null">budget = #{budget},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ad_id = #{adId}
    </update>

    <delete id="deleteAdsByAdId" parameterType="Long">
        delete from ads where ad_id = #{adId}
    </delete>

    <delete id="deleteAdsByAdIds" parameterType="String">
        delete from ads where ad_id in
        <foreach item="adId" collection="array" open="(" separator="," close=")">
            #{adId}
        </foreach>
    </delete>
</mapper>