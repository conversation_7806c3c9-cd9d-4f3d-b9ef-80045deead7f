<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PilotApplicationMapper">
    
    <resultMap type="PilotApplication" id="PilotApplicationResult">
        <result property="id" column="id"/>
        <result property="applicationNo" column="application_no"/>
        <result property="portalUserId" column="portal_user_id"/>
        <result property="applicantName" column="applicant_name"/>
        <result property="phone" column="phone"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="pilotType" column="pilot_type"/>
        <result property="projectName" column="project_name"/>
        <result property="projectBackground" column="project_background"/>
        <result property="technicalRequirements" column="technical_requirements"/>
        <result property="otherInfo" column="other_info"/>
        <result property="status" column="status"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="adminPrice" column="admin_price"/>
        <result property="confirmedAmount" column="confirmed_amount"/>
        <result property="auditPaymentDate" column="audit_payment_date"/>
        <result property="paymentVoucher" column="payment_voucher"/>
        <result property="adminPriceRemark" column="admin_price_remark"/>
        <result property="confirmPaymentRemark" column="confirm_payment_remark"/>
        <result property="reviewComment" column="review_comment"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="paymentRejectReason" column="payment_reject_reason"/>
    </resultMap>

    <sql id="selectPilotApplicationVo">
        select id, application_no, portal_user_id, applicant_name, phone, enterprise_name, 
        pilot_type, project_name, project_background, technical_requirements, other_info, 
        status, payment_status, admin_price, confirmed_amount, audit_payment_date, payment_voucher, 
        admin_price_remark, confirm_payment_remark, review_comment, del_flag, create_by, create_time, 
        update_by, update_time, payment_reject_reason
        from pilot_application
    </sql>

    <select id="selectPilotApplicationList" parameterType="PilotApplication" resultMap="PilotApplicationResult">
        <include refid="selectPilotApplicationVo"/>
        <where>
            <if test="delFlag != null"> and del_flag = #{delFlag}</if>
            <if test="delFlag == null"> and del_flag = '0'</if>
            <if test="applicationNo != null  and applicationNo != ''"> and application_no like concat('%', #{applicationNo}, '%')</if>
            <if test="portalUserId != null"> and portal_user_id = #{portalUserId}</if>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="enterpriseName != null  and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="pilotType != null  and pilotType != ''"> and pilot_type = #{pilotType}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="paymentStatus != null  and paymentStatus != ''"> and payment_status = #{paymentStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPilotApplicationById" parameterType="Long" resultMap="PilotApplicationResult">
        <include refid="selectPilotApplicationVo"/>
        where id = #{id} and del_flag = '0'
    </select>
        
    <insert id="insertPilotApplication" parameterType="PilotApplication" useGeneratedKeys="true" keyProperty="id">
        insert into pilot_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationNo != null">application_no,</if>
            <if test="portalUserId != null">portal_user_id,</if>
            <if test="applicantName != null">applicant_name,</if>
            <if test="phone != null">phone,</if>
            <if test="enterpriseName != null">enterprise_name,</if>
            <if test="pilotType != null">pilot_type,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectBackground != null">project_background,</if>
            <if test="technicalRequirements != null">technical_requirements,</if>
            <if test="otherInfo != null">other_info,</if>
            <if test="status != null">status,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="adminPrice != null">admin_price,</if>
            <if test="confirmedAmount != null">confirmed_amount,</if>
            <if test="paymentVoucher != null">payment_voucher,</if>
            <if test="adminPriceRemark != null">admin_price_remark,</if>
            <if test="confirmPaymentRemark != null">confirm_payment_remark,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="paymentRejectReason != null">payment_reject_reason,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationNo != null">#{applicationNo},</if>
            <if test="portalUserId != null">#{portalUserId},</if>
            <if test="applicantName != null">#{applicantName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="enterpriseName != null">#{enterpriseName},</if>
            <if test="pilotType != null">#{pilotType},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectBackground != null">#{projectBackground},</if>
            <if test="technicalRequirements != null">#{technicalRequirements},</if>
            <if test="otherInfo != null">#{otherInfo},</if>
            <if test="status != null">#{status},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="adminPrice != null">#{adminPrice},</if>
            <if test="confirmedAmount != null">#{confirmedAmount},</if>
            <if test="paymentVoucher != null">#{paymentVoucher},</if>
            <if test="adminPriceRemark != null">#{adminPriceRemark},</if>
            <if test="confirmPaymentRemark != null">#{confirmPaymentRemark},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="paymentRejectReason != null">#{paymentRejectReason},</if>
         </trim>
    </insert>

    <update id="updatePilotApplication" parameterType="PilotApplication">
        update pilot_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationNo != null">application_no = #{applicationNo},</if>
            <if test="portalUserId != null">portal_user_id = #{portalUserId},</if>
            <if test="applicantName != null">applicant_name = #{applicantName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="enterpriseName != null">enterprise_name = #{enterpriseName},</if>
            <if test="pilotType != null">pilot_type = #{pilotType},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectBackground != null">project_background = #{projectBackground},</if>
            <if test="technicalRequirements != null">technical_requirements = #{technicalRequirements},</if>
            <if test="otherInfo != null">other_info = #{otherInfo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="adminPrice != null">admin_price = #{adminPrice},</if>
            <if test="confirmedAmount != null">confirmed_amount = #{confirmedAmount},</if>
            <if test="auditPaymentDate != null">audit_payment_date = #{auditPaymentDate},</if>
            <if test="paymentVoucher != null">payment_voucher = #{paymentVoucher},</if>
            <if test="adminPriceRemark != null">admin_price_remark = #{adminPriceRemark},</if>
            <if test="confirmPaymentRemark != null">confirm_payment_remark = #{confirmPaymentRemark},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="paymentRejectReason != null">payment_reject_reason = #{paymentRejectReason},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deletePilotApplicationById" parameterType="Long">
        update pilot_application set del_flag = '1' where id = #{id}
    </update>

    <update id="deletePilotApplicationByIds" parameterType="Long">
        update pilot_application set del_flag = '1' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper> 