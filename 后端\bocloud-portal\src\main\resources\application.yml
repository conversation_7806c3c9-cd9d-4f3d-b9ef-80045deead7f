# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.8
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  # profile: /opt/uploadPath
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8088
  servlet:
    # 应用的访问路径
    context-path: /
    # 设置编码
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.bocloud: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  #配置邮箱
  mail:
    #邮箱服务商的protocol服务器主机
    host: smtp.163.com
    port: 465
    #邮件协议
    protocol: smtps
    #指定邮箱服务商的邮箱账号
    username:  <EMAIL>
    #邮箱账号密码或者三方登录授权码
    password: EXRC7GqT7Di9VBva
    default-encoding: UTF-8
    #启动项目时验证是否可以正确通信 默认为false
    test-connection: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 3380
    # 数据库索引
    database: 0
    # 密码
    password: 123$%^
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 480

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.bocloud.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 滑块验证码配置， 详细请看 cloud.tianai.captcha.autoconfiguration.ImageCaptchaProperties 类
captcha:
  # 如果项目中使用到了redis，滑块验证码会自动把验证码数据存到redis中， 这里配置redis的key的前缀,默认是captcha:slider
  prefix: captcha
  # 验证码过期时间，默认是2分钟,单位毫秒， 可以根据自身业务进行调整
  expire:
    # 默认缓存时间 2分钟
    default: 10000
    # 针对 点选验证码 过期时间设置为 2分钟， 因为点选验证码验证比较慢，把过期时间调整大一些
    WORD_IMAGE_CLICK: 20000
  # 使用加载系统自带的资源， 默认是 false(这里系统的默认资源包含 滑动验证码模板/旋转验证码模板,如果想使用系统的模板，这里设置为true)
  init-default-resource: true
  # 缓存控制， 默认为false不开启
  local-cache-enabled: true
  # 验证码会提前缓存一些生成好的验证数据， 默认是20
  local-cache-size: 20
  # 缓存拉取失败后等待时间 默认是 5秒钟
  local-cache-wait-time: 5000
  # 缓存检查间隔 默认是2秒钟
  local-cache-period: 2000
  # 配置字体包，供文字点选验证码使用,可以配置多个，不配置使用默认的字体
  #font-path:
  #  - classpath:font/SimHei.ttf
  secondary:
    # 二次验证， 默认false 不开启
    enabled: true
    # 二次验证过期时间， 默认 2分钟
    expire: 120000
    # 二次验证缓存key前缀，默认是 captcha:secondary
    keyPrefix: "captcha:secondary"
#是否加密
encrypt: false
signkey: bocloud123456
privatekey: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAm3lEbmU6haaqK/l+YTxg82v064sHF00VftRsruqtEAJAZ3G4D/kt9cnsucrL4np3X/1YS8DzbDB95RygQl/******************************/sKbPrVmzY1K6m2KyLabgNZysTOY9zReR2iPsZ47dK9aRoqZSJ5v0I+41poDwSW0CnBAiEAx4kAVihVkmdeOFjwwvOQaJidbAEDe22vcqZ1JR0uWXkCIQDHeE4ytGxZRcYZT7ONpTPYNqCtQkF67iV2XNMG2rnbyQIgAuLkclqbAkckgcQnl2pWiGzMuic5mat4gjwOLb9CUGECID9RBel8cgQobMhuDz3AGePg+0HkDSr7WzLmJio/******************************/qx39BhfmFgXQh+DjZFQ==
publickey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJt5RG5lOoWmqiv5fmE8YPNr9OuLBxdNFX7UbK7qrRACQGdxuA/5LfXJ7LnKy+J6d1/9WEvA82wwfeUcoEJfwwECAwEAAQ==

aliyun:
  sms:
    region-id: cn-hangzhou
    access-key-id: LTAI5tC9nPxAeb5SsfZqJ6nK
    access-key-secret: ******************************
    sign-name: 衢州膜材料创新研究院
    template-code: SMS_319116148

wx:
  mp:
    appId: wx976f7a4079bbb8b3  # 微信公众号app id
    secret: 4d14d3d22350a3f920638899cb33ddb5  # 微信公众号app secret
    token: quzhouyanjiuyuan123  # 微信公众号token
  miniapp:
    appId: wxc4f466b19be582ea  # 微信小程序app id
    secret: 96663ce085e0e877a24515011f9cd2e9  # 微信小程序app secret（需要替换为真实的小程序secret）
