package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.demand.DemandCharge;
import org.apache.ibatis.annotations.Mapper;

/**
 * 需求收费Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DemandChargeMapper {
    /**
     * 新增需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int insertDemandCharge(DemandCharge demandCharge);

    /**
     * 修改需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    public int updateDemandCharge(DemandCharge demandCharge);

    /**
     * 删除需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    public int deleteDemandChargeById(Long chargeId);

    /**
     * 查询需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeById(Long chargeId);

    /**
     * 根据合同ID查询需求收费
     * 
     * @param contractId 合同ID
     * @return 需求收费信息
     */
    public DemandCharge selectDemandChargeByContractId(Long contractId);
} 