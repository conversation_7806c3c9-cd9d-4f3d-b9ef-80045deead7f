package com.bocloud.portal.business.service.impl;

import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.portal.business.mapper.DemandChargeMapper;
import com.bocloud.domain.system.demand.DemandCharge;
import com.bocloud.portal.business.service.IDemandChargeService;

/**
 * 需求收费Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class DemandChargeServiceImpl implements IDemandChargeService {
    @Autowired
    private DemandChargeMapper demandChargeMapper;

    /**
     * 新增需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    @Override
    public int insertDemandCharge(DemandCharge demandCharge) {
        demandCharge.setCreateTime(new Date());
        demandCharge.setStatus(DemandCharge.STATUS_PENDING);  // 设置初始状态为待收费
        return demandChargeMapper.insertDemandCharge(demandCharge);
    }

    /**
     * 修改需求收费
     * 
     * @param demandCharge 需求收费信息
     * @return 结果
     */
    @Override
    public int updateDemandCharge(DemandCharge demandCharge) {
        demandCharge.setUpdateTime(new Date());
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }

    /**
     * 删除需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    @Override
    public int deleteDemandChargeById(Long chargeId) {
        return demandChargeMapper.deleteDemandChargeById(chargeId);
    }

    /**
     * 查询需求收费
     * 
     * @param chargeId 需求收费ID
     * @return 需求收费信息
     */
    @Override
    public DemandCharge selectDemandChargeById(Long chargeId) {
        return demandChargeMapper.selectDemandChargeById(chargeId);
    }

    /**
     * 根据合同ID查询需求收费
     * 
     * @param contractId 合同ID
     * @return 需求收费信息
     */
    @Override
    public DemandCharge selectDemandChargeByContractId(Long contractId) {
        return demandChargeMapper.selectDemandChargeByContractId(contractId);
    }

    /**
     * 确认收费
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    @Override
    public int confirmDemandCharge(Long chargeId) {
        DemandCharge demandCharge = new DemandCharge();
        demandCharge.setChargeId(chargeId);
        demandCharge.setStatus(DemandCharge.STATUS_CHARGED);  // 更新为已收费状态
        demandCharge.setChargeTime(new Date());
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }

    /**
     * 取消收费
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    @Override
    public int cancelDemandCharge(Long chargeId) {
        DemandCharge demandCharge = new DemandCharge();
        demandCharge.setChargeId(chargeId);
        demandCharge.setStatus(DemandCharge.STATUS_CANCELLED);  // 更新为已取消状态
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }

    /**
     * 更新为已开票状态
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    @Override
    public int updateToInvoiced(Long chargeId) {
        DemandCharge demandCharge = new DemandCharge();
        demandCharge.setChargeId(chargeId);
        demandCharge.setStatus(DemandCharge.STATUS_INVOICED);  // 更新为已开票状态
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }

    /**
     * 更新为已完成状态
     * 
     * @param chargeId 需求收费ID
     * @return 结果
     */
    @Override
    public int updateToCompleted(Long chargeId) {
        DemandCharge demandCharge = new DemandCharge();
        demandCharge.setChargeId(chargeId);
        demandCharge.setStatus(DemandCharge.STATUS_COMPLETED);  // 更新为已完成状态
        return demandChargeMapper.updateDemandCharge(demandCharge);
    }
} 