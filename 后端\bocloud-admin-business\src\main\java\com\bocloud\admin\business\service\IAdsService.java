package com.bocloud.admin.business.service;

import  com.bocloud.domain.system.platform_service.Ads;

import java.util.List;

/**
 * 广告申请Service接口
 *
 * @date 2025-03-11
 */
public interface IAdsService
{
    /**
     * 查询广告申请
     *
     * @param adId 广告申请主键
     * @return 广告申请
     */
    public Ads selectAdsByAdId(Long adId);

    /**
     * 查询广告申请列表
     *
     * @param ads 广告申请
     * @return 广告申请集合
     */
    public List<Ads> selectAdsList(Ads ads);

    /**
     * 新增广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    public int insertAds(Ads ads);

    /**
     * 修改广告申请
     *
     * @param ads 广告申请
     * @return 结果
     */
    public int updateAds(Ads ads);

    /**
     * 批量删除广告申请
     *
     * @param adIds 需要删除的广告申请主键集合
     * @return 结果
     */
    public int deleteAdsByAdIds(Long[] adIds);

    /**
     * 删除广告申请信息
     *
     * @param adId 广告申请主键
     * @return 结果
     */
    public int deleteAdsByAdId(Long adId);
}