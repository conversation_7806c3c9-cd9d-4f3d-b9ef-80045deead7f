package com.bocloud.portal.controller;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.dto.BindPhoneRequest;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.portal.business.service.impl.LoginService;
import com.bocloud.portal.business.service.ISmsService;
import com.bocloud.framework.web.service.TokenService;
import com.bocloud.common.core.domain.model.LoginUser;
import com.bocloud.common.core.domain.entity.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/wx")
public class WxAuthController {

    private static final Logger log = LoggerFactory.getLogger(WxAuthController.class);

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISmsService smsService;

    @PostMapping("/bindPhone")
    public AjaxResult bindPhone(@RequestBody BindPhoneRequest request) {
        log.info("[bindPhone] 开始绑定手机号，phone: {}, openId: {}, smsUuid: {}", 
                request.getPhone(), request.getOpenId(), request.getSmsUuid());
        
        try {
            // 1. 验证短信验证码
            log.info("[bindPhone] 开始验证短信验证码");
            boolean isValidCode = smsService.checkSmsCode(request.getPhone(), request.getSmsCode(), request.getSmsUuid());
            if (!isValidCode) {
                log.error("[bindPhone] 短信验证码验证失败，phone: {}", request.getPhone());
                return AjaxResult.error("短信验证码错误或已过期");
            }
            log.info("[bindPhone] 短信验证码验证成功");
            
            // 2. 执行绑定逻辑
            PortalUser portalUser = portalUserService.bindPhoneAndWechatOpenId(request.getPhone(), request.getOpenId());
            
            if (portalUser != null) {
                boolean isNew = portalUser.getCreateBy() != null && portalUser.getCreateBy().equals("wx-web");
                log.info("[bindPhone] 绑定成功，userId: {}, isNew: {}, username: {}, nickname: {}", 
                        portalUser.getId(), isNew, portalUser.getUsername(), portalUser.getNickname());
                
                // 3. 生成新token
                SysUser sysUser = new SysUser();
                BeanUtils.copyProperties(portalUser, sysUser);
                LoginUser loginUser = new LoginUser(portalUser.getId(), sysUser);
                String token = tokenService.createToken(loginUser);
                
                AjaxResult ajax = AjaxResult.success(isNew ? "新用户创建并绑定成功" : "绑定成功", portalUser);
                ajax.put("userId", portalUser.getId());
                ajax.put("username", portalUser.getUsername());
                ajax.put("nickname", portalUser.getNickname());
                ajax.put("phone", portalUser.getPhone());
                ajax.put("wechatOpenid", portalUser.getWechatOpenid());
                ajax.put("token", token);
                return ajax;
            } else {
                log.error("[bindPhone] 绑定失败，phone: {}, openId: {}", request.getPhone(), request.getOpenId());
                return AjaxResult.error("绑定失败");
            }
        } catch (Exception e) {
            log.error("[bindPhone] 绑定手机号异常，phone: {}, openId: {}", request.getPhone(), request.getOpenId(), e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }
}