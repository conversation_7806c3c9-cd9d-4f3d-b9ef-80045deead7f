package com.bocloud.domain.web.dto;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 企业认证信息DTO
 */
@Data
public class EnterpriseVerificationDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 公司名称 */
    @NotBlank(message = "公司名称不能为空")
    @Length(max = 50, message = "公司名称长度不能超过50个字符")
    private String companyName;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @Length(min = 18, max = 18, message = "统一社会信用代码长度必须为18位")
    @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", message = "统一社会信用代码格式不正确")
    private String creditCode;

    /** 营业执照图片地址 */
    private String creditImageUrl;

    /** 法人姓名 */
    @NotBlank(message = "法人姓名不能为空")
    @Length(max = 20, message = "法人姓名长度不能超过20个字符")
    private String legalPersonName;

    /** 法人证件类型：1-身份证，2-护照，3-其他 */
    @NotBlank(message = "法人证件类型不能为空")
    @Pattern(regexp = "^[1-3]$", message = "法人证件类型不正确")
    private String legalPersonIdType;

    /** 法人证件图片地址 */
    //@NotBlank(message = "法人证件图片不能为空")
    @Length(max = 255, message = "法人证件图片地址长度不能超过255个字符")
    private String legalPersonImageUrl;

    /** 法人证件号码 */
    //@NotBlank(message = "法人证件号码不能为空")
    @Length(max = 50, message = "法人证件号码长度不能超过50个字符")
    private String legalPersonIdNumber;

    /** 身份证人像面图片路径 */
    //@NotBlank(message = "身份证人像面图片不能为空")
    @Length(max = 255, message = "身份证人像面图片路径长度不能超过255个字符")
    private String legalPersonIdCardPortrait;

    /** 身份证国徽面图片路径 */
    //@NotBlank(message = "身份证国徽面图片不能为空")
    @Length(max = 255, message = "身份证国徽面图片路径长度不能超过255个字符")
    private String legalPersonIdCardEmblem;

    /** 法人电话 */
    @NotBlank(message = "法人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "法人电话格式不正确")
    private String legalPersonPhone;

    /** 法人电话验证码 */
    @NotBlank(message = "法人电话验证码不能为空")
    @Pattern(regexp = "^\\d{4}$", message = "验证码不正确")
    private String legalPersonPhoneCode;

    /** 短信验证码UUID */
    @NotBlank(message = "短信验证码UUID不能为空")
    private String smsUuid;
}