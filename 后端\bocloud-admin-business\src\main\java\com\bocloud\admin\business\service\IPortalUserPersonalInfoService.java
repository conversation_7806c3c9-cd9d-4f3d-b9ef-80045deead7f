package com.bocloud.admin.business.service;

import com.bocloud.domain.web.PersonalInfo;

import java.util.List;

/**
 * 用户个人信息Service接口
 */
public interface IPortalUserPersonalInfoService {
    
    /** 查询待审核的个人信息 */
    PersonalInfo selectPendingPersonalInfoByUserId(Long userId);

    /** 查询已通过且有效的个人信息 */
    PersonalInfo selectActivePersonalInfoByUserId(Long userId);

    /** 查询最新未通过的个人信息 */
    PersonalInfo selectLatestRejectedPersonalInfoByUserId(Long userId);

    /**
     * 聚合优先级查询个人信息：优先返回待审核，没有则返回已通过且有效，没有则返回最新未通过
     */
    PersonalInfo selectBestPersonalInfoByUserId(Long userId);

    /**
     * 根据用户ID和状态查询最新的个人信息
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 个人信息
     */
    PersonalInfo selectLatestByUserIdAndStatus(Long userId, String status);

    /**
     * 根据用户ID查询已激活的个人信息
     * 
     * @param userId 用户ID
     * @return 个人信息
     */
    PersonalInfo selectActiveByUserId(Long userId);

    /**
     * 修改个人信息
     * 
     * @param personalInfo 个人信息
     * @return 结果
     */
    int updatePortalUserPersonalInfo(PersonalInfo personalInfo);

    /**
     * 根据状态查询个人信息列表
     * 
     * @param status 状态
     * @return 个人信息列表
     */
    List<PersonalInfo> selectListByStatus(String status);

} 