package com.bocloud.portal.business.service.impl;

import com.bocloud.domain.system.post.Posts;
import com.bocloud.portal.business.mapper.PostsMapper;
import com.bocloud.portal.business.service.ICommonAttachmentService;
import com.bocloud.portal.business.service.IPostsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 信息发布Service业务层处理
 *
 * @date 2025-03-03
 */
@Service
public class PostsServiceImpl implements IPostsService
{
    @Autowired
    private PostsMapper postsMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    /**
     * 查询信息发布
     *
     * @param postId 信息发布主键
     * @return 信息发布
     */
    @Override
    public Posts selectPostsByPostId(Long postId)
    {
        Posts posts = postsMapper.selectPostsByPostId(postId);
        if (posts != null) {
            posts.setAttachments(attachmentService.selectCommonAttachmentByBusiness(postId, "posts"));
        }
        return posts;
    }

    /**
     * 查询信息发布列表
     *
     * @param posts 信息发布
     * @return 信息发布
     */
    @Override
    public List<Posts> selectPostsList(Posts posts)
    {
        return postsMapper.selectPostsList(posts);
    }

    /**
     * 更新信息浏览次数
     *
     * @param postId 信息ID
     * @return 结果
     */
    @Override
    public int updatePostViews(Long postId)
    {
        return postsMapper.updatePostViews(postId);
    }

    @Override
    public List<Posts> selectRelatedPosts(Posts posts) {
        return postsMapper.selectRelatedPosts(posts);
    }

    /**
     * 获取热门资讯列表
     *
     * @return 热门资讯列表
     */
    @Override
    public List<Posts> selectHotPosts() {
        Posts posts = new Posts();
        posts.setStatus(Posts.STATUS_PUBLISH);
        return postsMapper.selectHotPosts(posts);
    }
}