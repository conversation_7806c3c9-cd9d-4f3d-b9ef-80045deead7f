package com.bocloud.domain.system.demand.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class ContractAuditDTO {
    private Long contractId;
    private String chargeType; // 1=固定金额, 2=百分比
    private BigDecimal fixedAmount; // 仅当 chargeType=1 时有值
    private BigDecimal chargeRate;  // 仅当 chargeType=2 时有值
    private String auditResult;     // 2=审核通过(进行中), 3=审核不通过
    private String reviewComment;   // 审核意见内容
} 