package com.bocloud.admin.business.service;

import  com.bocloud.domain.system.platform_service.AdPositions;

import java.util.List;

/**
 * 广告位置信息Service接口
 *
 * @date 2025-03-11
 */
public interface IAdPositionsService
{
    /**
     * 查询广告位置信息
     *
     * @param positionId 广告位置信息主键
     * @return 广告位置信息
     */
    public AdPositions selectAdPositionsByPositionId(Long positionId);

    /**
     * 查询广告位置信息列表
     *
     * @param adPositions 广告位置信息
     * @return 广告位置信息集合
     */
    public List<AdPositions> selectAdPositionsList(AdPositions adPositions);

    /**
     * 新增广告位置信息
     *
     * @param adPositions 广告位置信息
     * @return 结果
     */
    public int insertAdPositions(AdPositions adPositions);

    /**
     * 修改广告位置信息
     *
     * @param adPositions 广告位置信息
     * @return 结果
     */
    public int updateAdPositions(AdPositions adPositions);

    /**
     * 批量删除广告位置信息
     *
     * @param positionIds 需要删除的广告位置信息主键集合
     * @return 结果
     */
    public int deleteAdPositionsByPositionIds(Long[] positionIds);

    /**
     * 删除广告位置信息信息
     *
     * @param positionId 广告位置信息主键
     * @return 结果
     */
    public int deleteAdPositionsByPositionId(Long positionId);
}