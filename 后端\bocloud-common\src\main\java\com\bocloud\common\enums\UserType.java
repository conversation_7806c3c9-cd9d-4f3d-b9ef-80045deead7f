package com.bocloud.common.enums;

/**
 * 用户类型枚举
 * 
 * <AUTHOR>
 */
public enum UserType {
    
    PERSONAL("1", "个人用户"),
    ENTERPRISE("2", "企业用户"),
    EXPERT("3", "专家用户");
    
    private final String code;
    private final String desc;
    
    UserType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static UserType getByCode(String code) {
        for (UserType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        UserType type = getByCode(code);
        return type != null ? type.getDesc() : "";
    }
    
    /**
     * 判断是否为个人用户
     */
    public static boolean isPersonal(String code) {
        return PERSONAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为企业用户
     */
    public static boolean isEnterprise(String code) {
        return ENTERPRISE.getCode().equals(code);
    }
    
    /**
     * 判断是否为专家用户
     */
    public static boolean isExpert(String code) {
        return EXPERT.getCode().equals(code);
    }
} 