package com.bocloud.admin.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.system.demand.DemandCharge;
import com.bocloud.admin.business.service.IDemandChargeService;

/**
 * 需求收费Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/demand/charge")
public class DemandChargeController extends BaseController {
    @Autowired
    private IDemandChargeService demandChargeService;

    /**
     * 查询需求收费列表
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:list')")
    @GetMapping("/list")
    public TableDataInfo list(DemandCharge demandCharge) {
        startPage();
        List<DemandCharge> list = demandChargeService.selectDemandChargeList(demandCharge);
        return getDataTable(list);
    }

    /**
     * 获取需求收费详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:query')")
    @GetMapping(value = "/{chargeId}")
    public AjaxResult getInfo(@PathVariable("chargeId") Long chargeId) {
        return success(demandChargeService.selectDemandChargeById(chargeId));
    }

    /**
     * 修改需求收费
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:edit')")
    @Log(title = "需求收费", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DemandCharge demandCharge) {
        return toAjax(demandChargeService.updateDemandCharge(demandCharge));
    }

    /**
     * 批量确认收费
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:confirm')")
    @Log(title = "需求收费", businessType = BusinessType.UPDATE)
    @PutMapping("/confirm")
    public AjaxResult confirm(@RequestBody Long[] chargeIds) {
        return toAjax(demandChargeService.confirmCharges(chargeIds));
    }

    /**
     * 批量确认开票
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:invoice')")
    @Log(title = "需求收费", businessType = BusinessType.UPDATE)
    @PutMapping("/invoice")
    public AjaxResult invoice(@RequestBody Long[] chargeIds) {
        return toAjax(demandChargeService.confirmInvoices(chargeIds));
    }

    /**
     * 批量完成收费
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:complete')")
    @Log(title = "需求收费", businessType = BusinessType.UPDATE)
    @PutMapping("/complete")
    public AjaxResult complete(@RequestBody Long[] chargeIds) {
        return toAjax(demandChargeService.completeCharges(chargeIds));
    }

    /**
     * 批量取消收费
     */
    @PreAuthorize("@ss.hasPermi('admin:demand:charge:cancel')")
    @Log(title = "需求收费", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public AjaxResult cancel(@RequestBody Long[] chargeIds) {
        return toAjax(demandChargeService.cancelCharges(chargeIds));
    }
} 