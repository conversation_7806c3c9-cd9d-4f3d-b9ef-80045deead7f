package com.bocloud.admin.business.service.impl;

import com.bocloud.admin.business.mapper.EnterpriseDemandMapper;
import com.bocloud.admin.business.service.ICommonAttachmentService;
import com.bocloud.admin.business.service.IEnterpriseDemandService;
import com.bocloud.admin.business.service.IMessageNotificationService;
import com.bocloud.admin.business.service.ISysAuditService;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.enums.AuditStatus;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.domain.system.SysAudit;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.admin.business.mapper.DemandSolutionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 企业需求Service实现类
 * 实现企业需求的查询、删除等后台管理功能
 */
@Service
public class EnterpriseDemandServiceImpl implements IEnterpriseDemandService {

    @Autowired
    private EnterpriseDemandMapper enterpriseDemandMapper;

    @Autowired
    private DemandSolutionMapper demandSolutionMapper;
    
    @Autowired
    private IMessageNotificationService messageNotificationService;
    
    @Autowired
    private ISysAuditService sysAuditService;

    @Autowired
    private ICommonAttachmentService commonAttachmentServiceImpl;

    /**
     * 根据ID查询企业需求详情
     * @param demandId 需求ID
     * @return 企业需求详情
     */
    @Override
    public EnterpriseDemand selectEnterpriseDemandById(Long demandId) {
        //获取附件列表
        EnterpriseDemand enterpriseDemand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        List<CommonAttachment> attachments = commonAttachmentServiceImpl.selectCommonAttachmentByBusiness(enterpriseDemand.getDemandId(), CommonAttachmentBusinessType.ENTERPRISE_DEMAND);
        enterpriseDemand.setAttachments(attachments);
        return enterpriseDemand;
    }

    /**
     * 查询企业需求列表
     * @param enterpriseDemand 查询条件
     * @return 企业需求列表
     */
    @Override
    public List<EnterpriseDemand> selectEnterpriseDemandList(EnterpriseDemand enterpriseDemand) {
        return enterpriseDemandMapper.selectEnterpriseDemandList(enterpriseDemand);
    }

    /**
     * 根据ID删除企业需求
     * @param demandId 需求ID
     * @return 删除结果
     */
    @Override
    public int deleteEnterpriseDemandById(Long demandId) {
        return enterpriseDemandMapper.deleteEnterpriseDemandById(demandId);
    }

    /**
     * 批量删除企业需求
     * @param demandIds 需求ID数组
     * @return 删除结果
     */
    @Override
    public int deleteEnterpriseDemandByIds(Long[] demandIds) {
        return enterpriseDemandMapper.deleteEnterpriseDemandByIds(demandIds);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int updateEnterpriseDemandAudit(EnterpriseDemand demand) {
        // 获取原需求信息
        EnterpriseDemand originalDemand = enterpriseDemandMapper.selectEnterpriseDemandById(demand.getDemandId());
        if (originalDemand == null) {
            throw new ServiceException("需求不存在");
        }
        
        // 执行审核更新
        int result = enterpriseDemandMapper.updateEnterpriseDemandAudit(demand);
        
        return result;
    }
    
    /**
     * 发送审核结果通知
     * @param demand 审核后的需求信息
     * @param originalDemand 原始需求信息
     */
    private void sendAuditNotification(EnterpriseDemand demand, EnterpriseDemand originalDemand) {
        try {
            // 根据需求状态判断审核结果
            boolean approved = AuditStatus.PASSED.getCode().equals(demand.getStatus()) || 
                              EnterpriseDemandStatus.WAIT_PUBLIC.equals(demand.getStatus());
            messageNotificationService.sendDemandAuditNotification(
                demand.getDemandId(),
                originalDemand.getDemandTitle(),
                originalDemand.getPortalUserId(),
                approved,
                demand.getReviewComment()
            );
        } catch (Exception e) {
            // 消息发送失败不影响主业务流程
            // 可以记录日志，但不抛出异常
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int auditDemand(Long demandId, Boolean approve, String reviewComment) {
        // 获取原需求信息
        EnterpriseDemand originalDemand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (originalDemand == null) {
            throw new ServiceException("需求不存在");
        }
        
        // 验证需求状态
        if (!EnterpriseDemandStatus.PENDING.getCode().equals(originalDemand.getStatus())) {
            throw new ServiceException("当前需求状态不允许审核");
        }
        
        // 根据审核结果设置需求状态
        String status;
        if (approve) {
            // 审核通过：从待审核(0) -> 待公开(1)
            status = EnterpriseDemandStatus.WAIT_PUBLIC.getCode();
        } else {
            // 审核不通过：从待审核(0) -> 审核失败(-1)
            status = EnterpriseDemandStatus.FAILED.getCode();
        }
        
        EnterpriseDemand demand = new EnterpriseDemand();
        demand.setDemandId(demandId);
        demand.setStatus(status);
        demand.setReviewComment(reviewComment);
        
        // 执行审核更新
        int result = enterpriseDemandMapper.updateEnterpriseDemandAudit(demand);
        if (result <= 0) {
            throw new ServiceException("更新需求状态失败");
        }
        
        // 保存审核记录
        saveAuditRecord(demandId, originalDemand.getStatus(), status, approve, reviewComment);
        
        // 发送审核结果通知
        sendAuditNotification(demand, originalDemand);
        
        return result;
    }
    
    /**
     * 保存审核记录
     * @param demandId 需求ID
     * @param fromStatus 审核前状态
     * @param toStatus 审核后状态
     * @param approve 是否通过
     * @param reviewComment 审核意见
     */
    private void saveAuditRecord(Long demandId, String fromStatus, String toStatus, Boolean approve, String reviewComment) {
        try {
            SysAudit sysAudit = new SysAudit();
            sysAudit.setBusinessType("ENTERPRISE_DEMAND");
            sysAudit.setBusinessId(demandId);
            sysAudit.setAuditType("0");  // 审核类型：初审
            sysAudit.setFromStatus(fromStatus);
            sysAudit.setToStatus(toStatus);
            sysAudit.setAuditResult(approve ? AuditStatus.PASSED.getCode() : AuditStatus.NOT_PASS.getCode());
            sysAudit.setReviewComment(reviewComment);
            sysAudit.setAuditUserId(SecurityUtils.getUserId());
            sysAudit.setAuditUserName(SecurityUtils.getUsername());
            sysAudit.setAuditTime(new Date());
            sysAudit.setCreateTime(new Date());
            
            int auditResult = sysAuditService.insertSysAudit(sysAudit);
            if (auditResult <= 0) {
                throw new ServiceException("保存审核记录失败");
            }
        } catch (Exception e) {
            // 审核记录保存失败不影响主业务流程，但需要记录日志
            // 这里可以根据需要添加日志记录
            throw new ServiceException("保存审核记录失败: " + e.getMessage());
        }
    }

    /**
     * 审核企业选择的中标方案
     * 
     * @param demandId 需求ID
     * @param approved 是否通过审核
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int reviewSolution(Long demandId, Boolean approved, String reviewComment) {
        // 查询需求信息
        EnterpriseDemand demand = enterpriseDemandMapper.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            throw new ServiceException("需求不存在");
        }

        // 验证需求状态
        if (!EnterpriseDemandStatus.PLATFORM_REVIEW.getCode().equals(demand.getStatus())) {
            throw new ServiceException("当前需求状态不允许审核");
        }

        // 更新需求状态
        EnterpriseDemand updateDemand = new EnterpriseDemand();
        updateDemand.setDemandId(demandId);
        updateDemand.setReviewComment(reviewComment);
        
        if (approved) {
            // 审核通过，状态变更为待签约
            updateDemand.setStatus(EnterpriseDemandStatus.WAIT_SIGN.getCode());
        } else {
            // 审核不通过，状态变更为选择方案中
            updateDemand.setStatus(EnterpriseDemandStatus.SELECTING.getCode());
            
            // 将已中标的方案状态重置为待选择
            DemandSolution solutionQuery = new DemandSolution();
            solutionQuery.setDemandId(demandId);
            solutionQuery.setStatus(DemandSolutionStatus.SELECTED.getCode());
            List<DemandSolution> selectedSolutions = demandSolutionMapper.selectDemandSolutionList(solutionQuery);
            
            for (DemandSolution solution : selectedSolutions) {
                DemandSolution updateSolution = new DemandSolution();
                updateSolution.setSolutionId(solution.getSolutionId());
                updateSolution.setStatus(DemandSolutionStatus.APPROVED.getCode());  // 重置为待选择状态
                demandSolutionMapper.updateDemandSolution(updateSolution);
            }
        }

        return enterpriseDemandMapper.updateEnterpriseDemandAudit(updateDemand);
    }
}