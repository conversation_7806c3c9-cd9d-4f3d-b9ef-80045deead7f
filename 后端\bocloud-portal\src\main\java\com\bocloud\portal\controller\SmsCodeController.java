package com.bocloud.portal.controller;

import com.bocloud.common.annotation.Anonymous;
import com.bocloud.common.constant.Constants;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.domain.model.LoginBody;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.enums.ErrorMsgEnum;
import com.bocloud.common.utils.StringUtils;
import com.bocloud.common.utils.uuid.IdUtils;
import com.bocloud.portal.business.service.ISmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 短信验证码控制器
 * 
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api("短信验证码")
@RequestMapping("/sms")
public class SmsCodeController {

    @Autowired
    private ISmsService smsService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 当前环境配置
     */
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 发送短信验证码
     *
     * @param loginBody 登录信息，包含手机号和可选的openId
     * @return 结果
     */
    @ApiOperation("发送短信验证码")
    @ApiImplicitParam(name = "loginBody", value = "登录信息", dataType = "LoginBody")
    @PostMapping("/code")
    @Anonymous
    public AjaxResult sendSmsCode(@RequestBody LoginBody loginBody) {
        try {
            String mobile = loginBody.getMobile();
            String openId = loginBody.getOpenId(); // 小程序openId（可选）
            
            log.info("[sendSmsCode] 开始发送短信验证码，mobile: {}, openId: {}, 当前环境: {}", mobile, openId, activeProfile);
            
            if (ObjectUtils.isEmpty(mobile)) {
                return AjaxResult.error(ErrorMsgEnum.SMS_SJHWK.getCode(), ErrorMsgEnum.SMS_SJHWK.getMsg());
            }
            
            // 校验验证发送的次数
            Collection<String> codeList = redisCache.keys(Constants.SMS_CAPTCHA_CODE_KEY + mobile + ":" + "*");
            if (codeList.size() > 0) {
                return AjaxResult.error(ErrorMsgEnum.SMS_YZMYCZ.getCode(), ErrorMsgEnum.SMS_YZMYCZ.getMsg());
            }
            
            // 根据环境生成验证码
            String code;
            boolean isDev = "dev".equals(activeProfile) || "test".equals(activeProfile) || "druid".equals(activeProfile);
            
            if (isDev) {
                // 开发/测试环境使用固定验证码
                code = "1234";
                log.info("[sendSmsCode] 开发环境使用固定验证码: {}", code);
            } else {
                // 生产环境生成随机验证码
                code = String.format("%04d", new Random().nextInt(10000));
                log.info("[sendSmsCode] 生产环境生成随机验证码: {}", code);
            }
            
            // 根据环境决定是否实际发送短信
            boolean sendSuccess = true;
            if (!isDev) {
                // 生产环境才实际发送短信
                sendSuccess = smsService.sendVerificationCode(mobile, code);
            if (!sendSuccess) {
                return AjaxResult.error("验证码发送失败，请稍后重试");
                }
            } else {
                log.info("[sendSmsCode] 开发环境跳过实际短信发送，验证码: {}", code);
            }
            
            // 保存验证码信息
            String uuid = IdUtils.simpleUUID();
            String verifyKey = Constants.SMS_CAPTCHA_CODE_KEY + mobile + ":" + uuid;
            Map<String, Object> map = new HashMap<>(16);
            map.put("mobile", mobile);
            map.put("code", code);
            if (StringUtils.isNotEmpty(openId)) {
                map.put("openId", openId); // 保存openId到验证码信息中
                log.info("[sendSmsCode] 验证码信息中包含openId: {}", openId);
            }
            redisCache.setCacheObject(verifyKey, map, Constants.SMS_EXPIRATION, TimeUnit.MINUTES);
            log.info("[sendSmsCode] 手机号{}设置短信验证码：{}", mobile, code);
            
            // 开发环境返回验证码和提示信息，生产环境不返回
            if (isDev) {
                AjaxResult ajax = AjaxResult.success("当前是开发环境，验证码固定是1234");
                ajax.put("smsUuid", uuid);
                ajax.put("smsCode", code);
                log.info("[sendSmsCode] 开发环境返回验证码: {}", code);
                return ajax;
            } else {
                // 生产环境
            AjaxResult ajax = AjaxResult.success();
            ajax.put("smsUuid", uuid);
            return ajax;
            }
        } catch (Exception e) {
            log.error("[sendSmsCode] 短信验证码发送异常", e);
        }
        return AjaxResult.error("验证码发送失败");
    }
} 