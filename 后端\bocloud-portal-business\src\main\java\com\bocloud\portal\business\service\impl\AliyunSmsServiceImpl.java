package com.bocloud.portal.business.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.bocloud.portal.business.config.AliyunSmsProperties;
import com.bocloud.portal.business.service.ISmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bocloud.common.core.redis.RedisCache;
import com.bocloud.common.constant.Constants;
import com.bocloud.common.exception.ServiceException;

@Service
public class AliyunSmsServiceImpl implements ISmsService {

    private static final Logger logger = LoggerFactory.getLogger(AliyunSmsServiceImpl.class);

    @Autowired
    private AliyunSmsProperties smsProperties;

    @Autowired
    private RedisCache redisCache;

    @Override
    public boolean sendVerificationCode(String phone, String code) {
        try {
            // 记录验证码发送日志
            logger.info("[短信验证码发送] 手机号: {}，验证码: {}", phone, code);
            DefaultProfile profile = DefaultProfile.getProfile(
                smsProperties.getRegionId(),
                smsProperties.getAccessKeyId(),
                smsProperties.getAccessKeySecret()
            );
            IAcsClient client = new DefaultAcsClient(profile);

            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phone);
            request.setSignName(smsProperties.getSignName());
            request.setTemplateCode(smsProperties.getTemplateCode());
            request.setTemplateParam("{\"code\":\"" + code + "\"}");

            SendSmsResponse response = client.getAcsResponse(request);
            // 记录阿里云短信服务响应
            logger.info("[短信验证码发送结果] 手机号: {}，验证码: {}，响应码: {}，响应消息: {}", phone, code, response.getCode(), response.getMessage());
            return "OK".equals(response.getCode());
        } catch (ClientException e) {
            // 记录日志
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean checkSmsCode(String mobile, String inputCode, String uuid) {
        String verifyKey = Constants.SMS_CAPTCHA_CODE_KEY + mobile + ":" + uuid;
        java.util.Map<String, Object> map = redisCache.getCacheObject(verifyKey);
        if (map == null) {
            throw new ServiceException("验证码已失效，请重新获取");
        }
        String applyMobile = (String) map.get("mobile");
        if (!mobile.equals(applyMobile)) {
            throw new ServiceException("手机号不一致");
        }
        String code = (String) map.get("code");
        if (inputCode == null || inputCode.trim().isEmpty()) {
            throw new ServiceException("验证码不能为空");
        }
        if (code == null) {
            throw new ServiceException("验证码已失效，请重新获取");
        }
        if (!inputCode.equals(code)) {
            throw new ServiceException("验证码错误");
        }
        // 注释掉这里的验证码清除，让完整的验证流程结束后再清除
        // redisCache.deleteObject(verifyKey);
        return true;
    }
} 