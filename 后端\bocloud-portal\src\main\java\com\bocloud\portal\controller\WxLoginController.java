package com.bocloud.portal.controller;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.dto.WxPhoneLoginBody;
import com.bocloud.portal.business.service.IWxLoginService;
import com.bocloud.portal.business.service.IPortalUserService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import me.chanjar.weixin.mp.api.WxMpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "微信登录接口")
@RestController
@RequestMapping("/api/wx/login")
public class WxLoginController {

    @Autowired
    private IWxLoginService wxLoginService;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private IPortalUserService portalUserService;

    @ApiOperation("获取登录二维码")
    @GetMapping("/qrcode")
    public AjaxResult getLoginQrCode() {
        log.info("[getLoginQrCode] 开始生成微信登录二维码");
        try {
            AjaxResult result = wxLoginService.generateLoginQrCode();
            log.info("[getLoginQrCode] 生成二维码成功，sceneStr: {}", result.get("sceneStr"));
            return result;
        } catch (Exception e) {
            log.error("[getLoginQrCode] 生成二维码失败", e);
            return AjaxResult.error("生成二维码失败：" + e.getMessage());
        }
    }

    @ApiOperation("检查登录状态")
    @GetMapping("/check/{sceneStr}")
    public AjaxResult checkLoginStatus(@PathVariable String sceneStr) {
        log.info("[checkLoginStatus] 检查登录状态，sceneStr: {}", sceneStr);
        try {
            AjaxResult result = wxLoginService.checkLoginStatus(sceneStr);
            log.info("[checkLoginStatus] 检查登录状态完成，sceneStr: {}, status: {}", sceneStr, result.get("status"));
            return result;
        } catch (Exception e) {
            log.error("[checkLoginStatus] 检查登录状态失败，sceneStr: {}", sceneStr, e);
            return AjaxResult.error("检查登录状态失败：" + e.getMessage());
        }
    }

    @ApiOperation("小程序手机号授权登录")
    @PostMapping("/miniapp/phone")
    public AjaxResult miniappPhoneLogin(@RequestBody WxPhoneLoginBody body) {
        log.info("[miniappPhoneLogin] 小程序手机号登录开始，code: {}, encryptedData: {}, iv: {}", 
                body.getCode(), body.getEncryptedData(), body.getIv());
        try {
            AjaxResult result = wxLoginService.miniappPhoneLogin(body);
            log.info("[miniappPhoneLogin] 小程序手机号登录成功");
            return result;
        } catch (Exception e) {
            log.error("[miniappPhoneLogin] 小程序手机号登录失败", e);
            return AjaxResult.error("小程序登录失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取小程序openId")
    @PostMapping("/miniapp/openid")
    public AjaxResult getMiniappOpenId(@RequestBody WxPhoneLoginBody body) {
        log.info("[getMiniappOpenId] 开始获取小程序openId，code: {}", body.getCode());
        try {
            // 调用微信API获取session信息
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(body.getCode());
            String openId = session.getOpenid();
            log.info("[getMiniappOpenId] 获取openId成功: {}", openId);
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("openId", openId);
            return ajax;
        } catch (Exception e) {
            log.error("[getMiniappOpenId] 获取openId失败", e);
            return AjaxResult.error("获取openId失败：" + e.getMessage());
        }
    }

    @ApiOperation("验证微信配置（仅开发测试用）")
    @GetMapping("/test/config")
    public AjaxResult testWxConfig() {
        log.info("[testWxConfig] 开始验证微信配置");
        try {
            // 获取当前配置的AppID
            String mpAppId = wxMpService.getWxMpConfigStorage().getAppId();
            String maAppId = wxMaService.getWxMaConfig().getAppid();
            
            log.info("[testWxConfig] 当前配置 - 公众号AppID: {}, 小程序AppID: {}", mpAppId, maAppId);
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("mpAppId", mpAppId);
            ajax.put("maAppId", maAppId);
            ajax.put("message", "配置验证完成，请检查AppID是否正确");
            return ajax;
        } catch (Exception e) {
            log.error("[testWxConfig] 配置验证失败", e);
            return AjaxResult.error("配置验证失败：" + e.getMessage());
        }
    }

    @ApiOperation("测试用户合并功能（仅开发测试用）")
    @PostMapping("/test/merge")
    public AjaxResult testUserMerge(@RequestParam String phone, @RequestParam String openId) {
        log.info("[testUserMerge] 开始测试用户合并功能，phone: {}, openId: {}", phone, openId);
        try {
            // 这里需要调用私有方法，我们可以通过反射或者将方法改为public
            // 为了简化，我们直接调用现有的bindPhoneAndWechatOpenId方法
            PortalUser result = portalUserService.bindPhoneAndWechatOpenId(phone, openId);
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("message", "用户合并测试完成");
            ajax.put("result", result);
            log.info("[testUserMerge] 用户合并测试完成，userId: {}", result.getId());
            return ajax;
        } catch (Exception e) {
            log.error("[testUserMerge] 用户合并测试失败", e);
            return AjaxResult.error("用户合并测试失败：" + e.getMessage());
        }
    }
}