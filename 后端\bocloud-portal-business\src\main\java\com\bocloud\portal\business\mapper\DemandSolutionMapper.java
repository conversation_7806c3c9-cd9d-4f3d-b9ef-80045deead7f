package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.demand.DemandSolution;
import java.util.List;

/**
 * 需求方案Mapper接口
 */
public interface DemandSolutionMapper {
    /**
     * 查询需求方案
     * 
     * @param solutionId 需求方案主键
     * @return 需求方案
     */
    public DemandSolution selectDemandSolutionById(Long solutionId);

    /**
     * 查询需求方案列表
     * 
     * @param demandSolution 需求方案
     * @return 需求方案集合
     */
    public List<DemandSolution> selectDemandSolutionList(DemandSolution demandSolution);

    /**
     * 新增需求方案
     * 
     * @param demandSolution 需求方案
     * @return 结果
     */
    public int insertDemandSolution(DemandSolution demandSolution);

    /**
     * 修改需求方案
     * 
     * @param demandSolution 需求方案
     * @return 结果
     */
    public int updateDemandSolution(DemandSolution demandSolution);

    /**
     * 删除需求方案
     * 
     * @param solutionId 需求方案主键
     * @return 结果
     */
    public int deleteDemandSolutionById(Long solutionId);

    /**
     * 批量删除需求方案
     * 
     * @param solutionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDemandSolutionByIds(Long[] solutionIds);
} 