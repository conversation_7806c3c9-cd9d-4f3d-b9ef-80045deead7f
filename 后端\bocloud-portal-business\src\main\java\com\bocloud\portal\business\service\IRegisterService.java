package com.bocloud.portal.business.service;

import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.CompleteRegisterInfo;
import com.bocloud.domain.web.RegisterUser;

/**
 * 注册服务接口
 */
public interface IRegisterService {
    /**
     * 完善注册信息
     * 
     * @param completeRegisterInfo 完善注册信息对象
     */
    void completeRegisterInfo(CompleteRegisterInfo completeRegisterInfo);

    /**
     * 手机号注册
     * 
     * @param registerUser 注册用户对象
     * @return 结果
     */
    AjaxResult smsRegister(RegisterUser registerUser);

} 