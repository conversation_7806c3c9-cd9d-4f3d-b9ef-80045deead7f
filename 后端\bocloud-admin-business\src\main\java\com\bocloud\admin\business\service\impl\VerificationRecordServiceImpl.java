package com.bocloud.admin.business.service.impl;

import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.admin.business.mapper.VerificationRecordMapper;
import com.bocloud.admin.business.service.IVerificationRecordService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 认证状态Service实现
 */
@Service
public class VerificationRecordServiceImpl implements IVerificationRecordService {

    @Autowired
    private VerificationRecordMapper verificationRecordMapper;

    @Override
    public VerificationRecord selectVerificationRecordByUserIdAndStatus(Long userId, String status) {
        return verificationRecordMapper.selectVerificationRecordByUserIdAndStatus(userId, status);
    }

    @Override
    public int updateVerificationRecord(VerificationRecord verificationRecord) {
        return verificationRecordMapper.updateVerificationRecord(verificationRecord);
    }

    @Override
    public List<VerificationRecord> selectVerificationRecordList(VerificationRecord verificationRecord) {
        return verificationRecordMapper.selectVerificationRecordList(verificationRecord);
    }
}