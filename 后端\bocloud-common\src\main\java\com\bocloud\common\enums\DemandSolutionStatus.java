package com.bocloud.common.enums;

/**
 * 需求方案状态枚举
 * 
 * <AUTHOR>
 */
public enum DemandSolutionStatus {
    
    // 方案状态
    PENDING("0", "待审核"),
    APPROVED("1", "待采纳"),
    SELECTED("2", "已采纳，待审核"),
    BID_WIN("3", "已中标"),
    COMPLETED("4", "已完成"),
    REJECTED("-1", "已驳回"),
    BID_LOSE("-2", "未中标");
    
    private final String code;
    private final String desc;
    
    DemandSolutionStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static DemandSolutionStatus getByCode(String code) {
        for (DemandSolutionStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        DemandSolutionStatus status = getByCode(code);
        return status != null ? status.getDesc() : null;
    }
    
    /**
     * 判断是否为待审核
     */
    public static boolean isPending(String code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为待采纳
     */
    public static boolean isApproved(String code) {
        return APPROVED.getCode().equals(code);
    }
    
    /**
     * 判断是否为已采纳，待审核
     */
    public static boolean isSelected(String code) {
        return SELECTED.getCode().equals(code);
    }
    
    /**
     * 判断是否为已中标
     */
    public static boolean isBidWin(String code) {
        return BID_WIN.getCode().equals(code);
    }
    
    /**
     * 判断是否为已驳回
     */
    public static boolean isRejected(String code) {
        return REJECTED.getCode().equals(code);
    }
    
    /**
     * 判断是否为未中标
     */
    public static boolean isBidLose(String code) {
        return BID_LOSE.getCode().equals(code);
    }
    
    /**
     * 判断是否为最终状态（已中标、已驳回、未中标）
     */
    public static boolean isFinalStatus(String code) {
        return isBidWin(code) || isRejected(code) || isBidLose(code);
    }
    
    /**
     * 判断是否为可编辑状态（待审核、待采纳、已采纳，待审核）
     */
    public static boolean isEditableStatus(String code) {
        return isPending(code) || isApproved(code) || isSelected(code);
    }
} 