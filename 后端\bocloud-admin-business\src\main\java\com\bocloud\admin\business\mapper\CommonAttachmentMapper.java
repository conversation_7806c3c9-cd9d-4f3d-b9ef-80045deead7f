package com.bocloud.admin.business.mapper;

import com.bocloud.domain.system.CommonAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用附件Mapper接口
 * 
 * <AUTHOR>
 */
public interface CommonAttachmentMapper {
    /**
     * 查询通用附件
     * 
     * @param attachmentId 通用附件主键
     * @return 通用附件
     */
    public CommonAttachment selectCommonAttachmentById(Long attachmentId);

    /**
     * 查询通用附件列表
     * 
     * @param commonAttachment 通用附件
     * @return 通用附件集合
     */
    public List<CommonAttachment> selectCommonAttachmentList(CommonAttachment commonAttachment);

    /**
     * 根据业务ID和业务类型查询附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 附件列表
     */
    public List<CommonAttachment> selectCommonAttachmentByBusiness(@Param("businessId") Long businessId, @Param("businessType") String businessType);

    /**
     * 新增通用附件
     * 
     * @param commonAttachment 通用附件
     * @return 结果
     */
    public int insertCommonAttachment(CommonAttachment commonAttachment);

    /**
     * 修改通用附件
     * 
     * @param commonAttachment 通用附件
     * @return 结果
     */
    public int updateCommonAttachment(CommonAttachment commonAttachment);

    /**
     * 删除通用附件
     * 
     * @param attachmentId 通用附件主键
     * @return 结果
     */
    public int deleteCommonAttachmentById(Long attachmentId);

    /**
     * 批量删除通用附件
     * 
     * @param attachmentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommonAttachmentByIds(Long[] attachmentIds);

    /**
     * 根据业务ID和业务类型删除附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 结果
     */
    public int deleteAttachmentByBusiness(@Param("businessId") Long businessId, @Param("businessType") String businessType);
} 