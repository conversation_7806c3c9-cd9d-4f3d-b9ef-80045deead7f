package com.bocloud.portal.business.mapper;

import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息管理Mapper接口
 */
public interface PortalMessageMapper {
    /**
     * 获取消息列表
     */
    List<PortalMessage> selectPortalMessageList(PortalMessageQuery query);
    
    /**
     * 获取未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int selectUnreadMessageCount(@Param("userId") Long userId);
    
    /**
     * 获取消息详情
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息详情
     */
    PortalMessage selectPortalMessageById(@Param("messageId") Long messageId, @Param("userId") Long userId);
} 