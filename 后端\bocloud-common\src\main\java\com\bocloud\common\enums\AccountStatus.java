package com.bocloud.common.enums;

/**
 * 账号状态枚举
 * 
 * <AUTHOR>
 */
public enum AccountStatus {
    
    DISABLE("0", "禁用"),
    NORMAL("1", "正常");
    
    private final String code;
    private final String desc;
    
    AccountStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static AccountStatus getByCode(String code) {
        for (AccountStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据代码获取描述
     */
    public static String getDescByCode(String code) {
        AccountStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }
    
    /**
     * 判断是否为正常状态
     */
    public static boolean isNormal(String code) {
        return NORMAL.getCode().equals(code);
    }
    
    /**
     * 判断是否为禁用状态
     */
    public static boolean isDisable(String code) {
        return DISABLE.getCode().equals(code);
    }
} 