package com.bocloud.portal.business.service;

import com.bocloud.common.enums.VerifyCodeType;

/**
 * 验证码服务接口
 */
public interface IVerifyCodeService {
    
    /**
     * 发送验证码
     *
     * @param account 账号（手机号/邮箱）
     * @param type 验证码类型
     */
    void sendVerifyCode(String account, VerifyCodeType type);

    /**
     * 验证验证码
     *
     * @param account 账号（手机号/邮箱）
     * @param code 验证码
     * @param type 验证码类型
     */
    void verifyCode(String account, String code, VerifyCodeType type);
} 