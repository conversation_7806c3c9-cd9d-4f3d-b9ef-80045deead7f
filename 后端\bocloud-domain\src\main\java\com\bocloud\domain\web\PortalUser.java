package com.bocloud.domain.web;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.common.enums.AccountStatus;
import com.bocloud.common.enums.UserType;
import com.bocloud.common.enums.InfoStatus;
import com.bocloud.common.enums.VerificationType;
import com.bocloud.common.enums.VerificationStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;

/**
 * 门户用户信息对象 poral_user
 * 
 * @date 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PortalUser extends BaseEntity implements UserDetails
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户名（账号密码登录使用，可为空） */
    @Excel(name = "用户名", readConverterExp = "账=号密码登录使用，可为空")
    private String username;

    /** 密码（账号密码登录使用，可为空，存储加密后的值） */
    @Excel(name = "密码", readConverterExp = "账号密码登录使用，可为空，存储加密后的值")
    @JsonIgnore
    private String password;

    /** 手机号（手机验证码登录使用，可为空） */
    @Excel(name = "手机号", readConverterExp = "=机验证码登录使用，可为空")
    private String phone;

    /** 微信OpenID（微信扫码登录使用，可为空） */
    @Excel(name = "微信OpenID", readConverterExp = "微信扫码登录使用，可为空")
    private String wechatOpenid;

    /** 微信UnionID（用于跨公众号和小程序唯一识别用户，可为空） */
    @Excel(name = "微信UnionID", readConverterExp = "用于跨公众号和小程序唯一识别用户，可为空")
    private String wechatUnionid;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatar;

    /** 账号状态（1=正常，0=禁用） */
    @Excel(name = "账号状态", readConverterExp = "1=正常，0=禁用")
    private String status;

    /** 用户类型（1个人用户 2企业用户） */
    @Excel(name = "用户类型", readConverterExp = "1=个人用户,2=企业用户, 3=专家用户")
    private String type;

    /** 信息完善状态（-1=未完善，0=待审核，1=已完善） */
    @Excel(name = "信息完善状态", readConverterExp = "状态：-1=未完善，0=待审核，1=已完善")
    private String infoStatus;

    /** 实名认证类型（1-个人认证，2-企业认证） */
    @Excel(name = "实名认证类型", readConverterExp = "1=个人认证,2=企业认证")
    private String verificationType;

    /** 实名认证状态（1-待认证，2-审核中，3-已通过，-1-已拒绝） */
    @Excel(name = "实名认证状态", readConverterExp = "1=待认证,2=审核中,3=已通过,-1=已拒绝")
    private String verificationStatus;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 个人信息 */
    private PersonalInfo personalInfo;

    /** 企业信息 */
    private EnterpriseInfo enterpriseInfo;

    /** 专家信息 */
    private ExpertInfo expertInfo;

    /** 删除标记（0=正常，1=删除） */
    private String delFlag;

    // ==================== 显示信息便捷方法 ====================
    
    /**
     * 获取显示名称（个人显示真实姓名，企业显示公司名称）
     */
    public String getDisplayName() {
        if (isPersonalUser() && personalInfo != null) {
            return personalInfo.getRealName();
        } else if (isEnterpriseUser() && enterpriseInfo != null) {
            return enterpriseInfo.getCompanyName();
        }
        return nickname;
    }
    
    /**
     * 获取显示地址（省市区）
     */
    public String getDisplayAddress() {
        if (isPersonalUser() && personalInfo != null) {
            return buildAddressString(personalInfo.getProvince(), personalInfo.getCity(), personalInfo.getDistrict());
        } else if (isEnterpriseUser() && enterpriseInfo != null) {
            return buildAddressString(enterpriseInfo.getProvince(), enterpriseInfo.getCity(), enterpriseInfo.getDistrict());
        }
        return "";
    }
    
    /**
     * 获取完整地址（省市区+详细地址）
     */
    public String getFullAddress() {
        if (isPersonalUser() && personalInfo != null) {
            return buildFullAddressString(personalInfo.getProvince(), personalInfo.getCity(), 
                                        personalInfo.getDistrict(), personalInfo.getDetailAddress());
        } else if (isEnterpriseUser() && enterpriseInfo != null) {
            return buildFullAddressString(enterpriseInfo.getProvince(), enterpriseInfo.getCity(), 
                                        enterpriseInfo.getDistrict(), enterpriseInfo.getDetailAddress());
        }
        return "";
    }
    
    /**
     * 构建地址字符串
     */
    private String buildAddressString(String province, String city, String district) {
        StringBuilder sb = new StringBuilder();
        if (province != null && !province.trim().isEmpty()) {
            sb.append(province);
        }
        if (city != null && !city.trim().isEmpty()) {
            sb.append(city);
        }
        if (district != null && !district.trim().isEmpty()) {
            sb.append(district);
        }
        return sb.toString();
    }
    
    /**
     * 构建完整地址字符串
     */
    private String buildFullAddressString(String province, String city, String district, String detailAddress) {
        StringBuilder sb = new StringBuilder();
        String address = buildAddressString(province, city, district);
        if (!address.isEmpty()) {
            sb.append(address);
        }
        if (detailAddress != null && !detailAddress.trim().isEmpty()) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(detailAddress);
        }
        return sb.toString();
    }

    // ==================== 状态转换方法 ====================
    
    /**
     * 获取账号状态描述
     */
    public String getStatusDesc() {
        return AccountStatus.getDescByCode(this.status);
    }
    
    /**
     * 获取用户类型描述
     */
    public String getTypeDesc() {
        return UserType.getDescByCode(this.type);
    }
    
    /**
     * 获取信息完善状态描述
     */
    public String getInfoStatusDesc() {
        return InfoStatus.getDescByCode(this.infoStatus);
    }
    
    /**
     * 获取认证类型描述
     */
    public String getVerificationTypeDesc() {
        return VerificationType.getDescByCode(this.verificationType);
    }
    
    /**
     * 获取认证状态描述
     */
    public String getVerificationStatusDesc() {
        return VerificationStatus.getDescByCode(this.verificationStatus);
    }
    
    /**
     * 判断账号是否正常
     */
    public boolean isAccountNormal() {
        return AccountStatus.isNormal(this.status);
    }
    
    /**
     * 判断是否为个人用户
     */
    public boolean isPersonalUser() {
        return UserType.isPersonal(this.type);
    }
    
    /**
     * 判断是否为企业用户
     */
    public boolean isEnterpriseUser() {
        return UserType.isEnterprise(this.type);
    }
    
    /**
     * 判断是否为专家用户
     */
    public boolean isExpertUser() {
        return UserType.isExpert(this.type);
    }
    
    /**
     * 判断信息是否完善
     */
    public boolean isInfoComplete() {
        return InfoStatus.isComplete(this.infoStatus);
    }
    
    /**
     * 判断认证是否通过
     */
    public boolean isVerificationApproved() {
        return VerificationStatus.isApproved(this.verificationStatus);
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return isAccountNormal();
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("username", getUsername())
                .append("phone", getPhone())
                .append("wechatOpenid", getWechatOpenid())
                .append("wechatUnionid", getWechatUnionid())
                .append("nickname", getNickname())
                .append("avatar", getAvatar())
                .append("status", getStatus())
                .append("statusDesc", getStatusDesc())
                .append("type", getType())
                .append("typeDesc", getTypeDesc())
                .append("infoStatus", getInfoStatus())
                .append("infoStatusDesc", getInfoStatusDesc())
                .append("verificationType", getVerificationType())
                .append("verificationTypeDesc", getVerificationTypeDesc())
                .append("verificationStatus", getVerificationStatus())
                .append("verificationStatusDesc", getVerificationStatusDesc())
                .append("auditComment", getAuditComment())
                .append("lastLoginTime", getLastLoginTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("personalInfo", getPersonalInfo())
                .append("enterpriseInfo", getEnterpriseInfo())
                .append("expertInfo", getExpertInfo())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
