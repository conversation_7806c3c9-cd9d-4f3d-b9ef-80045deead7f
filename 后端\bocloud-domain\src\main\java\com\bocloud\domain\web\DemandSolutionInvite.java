package com.bocloud.domain.web;

import java.util.Date;

import com.bocloud.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 需求解决方案邀请表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DemandSolutionInvite extends BaseEntity  {
    private static final long serialVersionUID = 1L;

    /** 邀请状态：待响应 */
    public static final String STATUS_PENDING = "0";
    
    /** 邀请状态：已接受 */
    public static final String STATUS_ACCEPTED = "1";
    
    /** 邀请状态：已拒绝 */
    public static final String STATUS_REJECTED = "2";
    
    /** 邀请状态：已提交 */
    public static final String STATUS_SUBMITTED = "3";

    /** 主键ID */
    private Long id;

    /** 需求ID */
    private Long demandId;

    /** 被邀请用户ID */
    private Long invitedUserId;

    /** 邀请状态（0-待响应, 1-已接受, 2-已拒绝, 3-已提交） */
    private String inviteStatus;

    /** 邀请时间 */
    private Date inviteTime;

    /** 邀请操作人（管理员ID） */
    private Long operatorId;

    /** 关键字搜索 */
    private String keyword;

    /**
     * 需求是否存在
     * 0: 需求不存在
     * 1: 需求存在
     */
    private Integer demandExists;
}