package com.bocloud.domain.system.test_application;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 测试申请确认收款请求对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TestApplicationConfirmPaymentRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测试申请ID */
    @NotNull(message = "申请ID不能为空")
    private Long testApplicationId;

    /** 实际确认收款金额 */
    @NotNull(message = "实际确认收款金额不能为空")
    @DecimalMin(value = "0.01", message = "实际确认收款金额必须大于0")
    private BigDecimal confirmedAmount;

    /** 确认收款备注 */
    private String confirmPaymentRemark;
} 