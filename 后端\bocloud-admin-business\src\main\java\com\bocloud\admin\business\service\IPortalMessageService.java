package com.bocloud.admin.business.service;

import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import java.util.List;

public interface IPortalMessageService {
    /**
     * 查询消息列表
     */
    List<PortalMessage> selectPortalMessageList(PortalMessageQuery query);

    /**
     * 查询消息详情
     */
    PortalMessage selectPortalMessageById(Long messageId);

    /**
     * 新增消息
     */
    int insertPortalMessage(PortalMessage message);

    /**
     * 修改消息
     */
    int updatePortalMessage(PortalMessage message);

    /**
     * 删除消息
     */
    int deletePortalMessageById(Long messageId);
} 