package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.web.ExpertInfo;
import com.bocloud.portal.business.service.IExpertInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 个人中心专家信息控制器
 */
@RestController
@RequestMapping("/membercenter/expert")
public class MemberCenterExpertController extends BaseController {
    
    @Autowired
    private IExpertInfoService expertInfoService;

    /**
     * 获取专家信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        // 获取当前登录用户ID
        Long userId = getUserId();
        ExpertInfo expertInfo = expertInfoService.getExpertDetailByUserId(userId);
        return success(expertInfo);
    }

    /**
     * 更新专家信息
     */
    @PutMapping("/updateInfo")
    public AjaxResult updateInfo(@RequestBody ExpertInfo expertInfo) {
        // 获取当前登录用户ID
        Long userId = getUserId();
        // 设置用户ID，防止篡改
        expertInfo.setUserId(userId);
        // 设置更新人
        expertInfo.setUpdateBy(getLoginUser().getUsername());
        
        // 先查询是否存在专家信息
        ExpertInfo existInfo = expertInfoService.getExpertDetailByUserId(userId);
        int rows;
        if (existInfo == null) {
            // 如果不存在，则新增
            rows = expertInfoService.insertExpertInfo(expertInfo);
        } else {
            // 如果存在，则更新
            expertInfo.setExpertId(existInfo.getExpertId());
            rows = expertInfoService.updateExpertInfo(expertInfo);
        }
        return toAjax(rows);
    }
}
