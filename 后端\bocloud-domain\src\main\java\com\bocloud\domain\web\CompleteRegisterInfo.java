package com.bocloud.domain.web;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 完善注册信息对象 complete_register_info
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompleteRegisterInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 账号类型：personal-个人账号，enterprise-企业账号 */
    private String accountType;
    
    /** 个人账号信息 */
    private PersonalInfo personalInfo;
    
    /** 专家信息 */
    private ExpertInfo expertInfo;
    
    /** 企业信息 */
    private EnterpriseInfo enterpriseInfo;



    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("accountType", getAccountType())
            .append("personalInfo", getPersonalInfo())
            .append("expertInfo", getExpertInfo())
            .append("enterpriseInfo", getEnterpriseInfo())
            .append("businessScope", getEnterpriseInfo() != null ? getEnterpriseInfo().getBusinessScope() : null)
            .append("introduction", getEnterpriseInfo() != null ? getEnterpriseInfo().getIntroduction() : null)
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}


