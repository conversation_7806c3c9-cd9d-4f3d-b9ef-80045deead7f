package com.bocloud.portal.controller.membercenter;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.domain.system.test_application.TestApplication;
import com.bocloud.portal.business.service.ITestApplicationService;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.common.core.page.TableDataInfo;

/**
 * 测试申请Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/membercenter/test")
public class MemverCenterTestApplicationController extends BaseController
{
    @Autowired
    private ITestApplicationService testApplicationService;

    /**
     * 查询测试申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TestApplication testApplication)
    {
        startPage();
        //只能查询本人名下的
        testApplication.setPortalUserId(SecurityUtils.getUserId());
        List<TestApplication> list = testApplicationService.selectTestApplicationList(testApplication);
        return getDataTable(list);
    }

    /**
     * 获取测试申请详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 验证是否是申请人本人
        TestApplication testApplication = testApplicationService.selectTestApplicationById(id);
        if (testApplication == null || !testApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权查看此申请");
        }
        return AjaxResult.success(testApplication);
    }

    /**
     * 新增测试申请
     */
    @Log(title = "测试申请", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TestApplication testApplication)
    {
        testApplication.setPortalUserId(SecurityUtils.getUserId());
        int rows = testApplicationService.insertTestApplication(testApplication);
        if (rows > 0) {
            // 查询并返回创建的测试申请信息
            TestApplication createdApplication = testApplicationService.selectTestApplicationById(testApplication.getId());
            return AjaxResult.success("测试申请提交成功，请等待审核", createdApplication);
        }
        return AjaxResult.error("测试申请提交失败，请稍后重试");
    }

    /**
     * 修改测试申请
     */
    @Log(title = "测试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TestApplication testApplication)
    {
        // 验证是否是申请人本人
        TestApplication oldApplication = testApplicationService.selectTestApplicationById(testApplication.getId());
        if (oldApplication == null || !oldApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权修改此申请");
        }
        return toAjax(testApplicationService.updateTestApplication(testApplication));
    }

    /**
     * 删除测试申请
     */
    @Log(title = "测试申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        // 验证是否是申请人本人
        try {
            int rows = testApplicationService.deleteTestApplicationById(id, SecurityUtils.getUserId());
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 上传付款信息
     * 
     * @param testApplication
     * @return
     */
    @Log(title = "测试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/uploadPaymentVoucher")
    public AjaxResult uploadPayment(@RequestBody TestApplication testApplication)
    {
        // 验证是否是申请人本人
        TestApplication oldApplication = testApplicationService.selectTestApplicationById(testApplication.getId());
        if (oldApplication == null || !oldApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权修改此申请");
        }

        // 验证申请状态是否为待付款
        if (!TestApplication.STATUS_PENDING_PAYMENT.equals(oldApplication.getStatus())) {
            return AjaxResult.error("当前申请状态不允许上传付款信息");
        }

        // 验证付款凭证URL是否提供
        if (testApplication.getPaymentVoucher() == null || testApplication.getPaymentVoucher().trim().isEmpty()) {
            return AjaxResult.error("请提供付款凭证");
        }

        // 设置付款信息并更新付款状态为已上传凭证(待审核)
        oldApplication.setPaymentVoucher(testApplication.getPaymentVoucher());
        oldApplication.setPaymentStatus(TestApplication.PAYMENT_STATUS_UPLOADED); // 已上传凭证，待审核
        oldApplication.setUpdateBy(SecurityUtils.getUsername());

        int rows = testApplicationService.updateTestApplication(oldApplication);
        if (rows > 0) {
            return AjaxResult.success("付款凭证上传成功，请等待管理员审核确认");
        }
        return AjaxResult.error("付款凭证上传失败，请稍后重试");
    }

    /**
     * 取消测试申请，只有待审核、待付款的状态才可以取消。
     */
    @Log(title = "取消测试申请", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{id}")
    public AjaxResult cancel(@PathVariable Long id)
    {
        // 验证是否是申请人本人
        TestApplication oldApplication = testApplicationService.selectTestApplicationById(id);
        if (oldApplication == null || !oldApplication.getPortalUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权取消此申请");
        }

        // 验证申请状态是否允许取消（待审核和待付款状态的申请可以取消）
        if (!TestApplication.STATUS_PENDING_REVIEW.equals(oldApplication.getStatus()) 
            && !TestApplication.STATUS_PENDING_PAYMENT.equals(oldApplication.getStatus())) {
            return AjaxResult.error("当前申请状态不允许取消，只有待审核和待付款状态的申请可以取消");
        }

        // 设置申请状态为已取消
        oldApplication.setStatus(TestApplication.STATUS_CANCELLED);
        oldApplication.setUpdateBy(SecurityUtils.getUsername());

        int rows = testApplicationService.updateTestApplication(oldApplication);
        if (rows > 0) {
            return AjaxResult.success("申请取消成功");
        }
        return AjaxResult.error("申请取消失败，请稍后重试");
    }

} 