package com.bocloud.admin.controller;

import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.admin.business.service.IEnterpriseDemandService;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

/**
 * 企业需求管理后台控制器
 * 提供企业需求的分页查询、详情、删除等管理接口
 */
@RestController
@RequestMapping("/admin/enterpriseDemand")
public class DemandController extends BaseController {

    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    /**
     * 分页查询企业需求列表
     * @param enterpriseDemand 查询条件
     * @return 分页企业需求列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseDemand enterpriseDemand) {
        startPage();
        List<EnterpriseDemand> list = enterpriseDemandService.selectEnterpriseDemandList(enterpriseDemand);
        return getDataTable(list);
    }

    /**
     * 查询企业需求详情
     * @param demandId 需求ID
     * @return 企业需求详情
     */
    @GetMapping("/{demandId}")
    public AjaxResult getInfo(@PathVariable Long demandId) {
        return success(enterpriseDemandService.selectEnterpriseDemandById(demandId));
    }

    /**
     * 删除企业需求
     * @param demandId 需求ID
     * @return 删除结果
     */
    @DeleteMapping("/{demandId}")
    public AjaxResult remove(@PathVariable Long demandId) {
        return toAjax(enterpriseDemandService.deleteEnterpriseDemandById(demandId));
    }

    /**
     * 审核企业需求
     * @param demandId 需求ID
     * @param approve 是否同意（true=同意，false=拒绝）
     * @param reviewComment 审核意见
     * @return 操作结果
     */
    @PostMapping("/audit")
    public AjaxResult audit(@RequestParam Long demandId,
                            @RequestParam Boolean approve,
                            @RequestParam(required = false) String reviewComment) {
        try {
            int rows = enterpriseDemandService.auditDemand(demandId, approve, reviewComment);
            return toAjax(rows);
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 审核企业选择的中标方案
     * 
     * @param demandId 需求ID
     * @param approved 是否通过审核
     * @param reviewComment 审核意见
     * @return 操作结果
     */
    @PostMapping("/review-solution")
    public AjaxResult reviewSolution(@RequestParam Long demandId,
                                   @RequestParam Boolean approved,
                                   @RequestParam(required = false) String reviewComment) {
        try {
            int rows = enterpriseDemandService.reviewSolution(demandId, approved, reviewComment);
            if (rows > 0) {
                return AjaxResult.success(approved ? "审核通过" : "审核不通过");
            } else {
                return AjaxResult.error("审核失败");
            }
        } catch (ServiceException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
} 