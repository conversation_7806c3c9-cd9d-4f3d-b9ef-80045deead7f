package com.bocloud.admin.controller;

import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import  com.bocloud.domain.system.platform_service.Ads;
import com.bocloud.admin.business.service.IAdsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 广告申请Controller
 *
 * @date 2025-03-11
 */
@RestController
@RequestMapping("/system/ads")
public class AdsController extends BaseController
{
    @Autowired
    private IAdsService adsService;

    /**
     * 查询广告申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:ads:list')")
    @GetMapping("/list")
    public TableDataInfo list(Ads ads)
    {
        startPage();
        List<Ads> list = adsService.selectAdsList(ads);
        return getDataTable(list);
    }

    /**
     * 导出广告申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:ads:export')")
    @Log(title = "广告申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Ads ads)
    {
        List<Ads> list = adsService.selectAdsList(ads);
        ExcelUtil<Ads> util = new ExcelUtil<Ads>(Ads.class);
        util.exportExcel(response, list, "广告申请数据");
    }

    /**
     * 获取广告申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ads:query')")
    @GetMapping(value = "/{adId}")
    public AjaxResult getInfo(@PathVariable("adId") Long adId)
    {
        return success(adsService.selectAdsByAdId(adId));
    }

    /**
     * 新增广告申请
     */
    @PreAuthorize("@ss.hasPermi('system:ads:add')")
    @Log(title = "广告申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Ads ads)
    {
        return toAjax(adsService.insertAds(ads));
    }

    /**
     * 修改广告申请
     */
    @PreAuthorize("@ss.hasPermi('system:ads:edit')")
    @Log(title = "广告申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Ads ads)
    {
        return toAjax(adsService.updateAds(ads));
    }

    /**
     * 删除广告申请
     */
    @PreAuthorize("@ss.hasPermi('system:ads:remove')")
    @Log(title = "广告申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{adIds}")
    public AjaxResult remove(@PathVariable Long[] adIds)
    {
        return toAjax(adsService.deleteAdsByAdIds(adIds));
    }
}