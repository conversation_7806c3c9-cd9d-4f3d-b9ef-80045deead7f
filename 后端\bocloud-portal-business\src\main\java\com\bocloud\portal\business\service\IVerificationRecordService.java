package com.bocloud.portal.business.service;

import com.bocloud.domain.web.VerificationRecord;

/**
 * 认证记录Service接口
 */
public interface IVerificationRecordService {
    /**
     * 根据用户ID查询认证状态
     * @param userId 用户ID
     * @return 认证状态信息
     */
    VerificationRecord selectVerificationRecordByUserId(Long userId);

    /**
     * 新增认证状态
     * @param verificationStatus 认证状态信息
     * @return 结果
     */
    int insertVerificationRecord(VerificationRecord verificationRecord);

    /**
     * 修改认证状态
     * @param verificationStatus 认证状态信息
     * @return 结果
     */
    int updateVerificationRecord(VerificationRecord verificationRecord);
} 