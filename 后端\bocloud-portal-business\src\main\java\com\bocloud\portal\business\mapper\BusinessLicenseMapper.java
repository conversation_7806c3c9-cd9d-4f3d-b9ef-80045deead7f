package com.bocloud.portal.business.mapper;

import java.util.List;
import com.bocloud.domain.web.BusinessLicense;

/**
 * 营业执照Mapper接口
 */
public interface BusinessLicenseMapper {
    /**
     * 查询营业执照列表
     * 
     * @param businessLicense 营业执照
     * @return 营业执照集合
     */
    public List<BusinessLicense> selectBusinessLicenseList(BusinessLicense businessLicense);

    /**
     * 查询营业执照
     * 
     * @param licenseId 营业执照ID
     * @return 营业执照
     */
    public BusinessLicense selectBusinessLicenseById(Long licenseId);

    /**
     * 新增营业执照
     * 
     * @param businessLicense 营业执照
     * @return 结果
     */
    public int insertBusinessLicense(BusinessLicense businessLicense);

    /**
     * 修改营业执照
     * 
     * @param businessLicense 营业执照
     * @return 结果
     */
    public int updateBusinessLicense(BusinessLicense businessLicense);

    /**
     * 删除营业执照
     * 
     * @param licenseId 营业执照ID
     * @return 结果
     */
    public int deleteBusinessLicenseById(Long licenseId);

    /**
     * 批量删除营业执照
     * 
     * @param licenseIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteBusinessLicenseByIds(String[] licenseIds);
} 