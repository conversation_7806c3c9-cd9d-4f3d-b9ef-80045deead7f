package com.bocloud.admin.business.mapper;


import com.bocloud.domain.system.test_application.TestApplication;

import java.util.List;

/**
 * 检测申请Mapper接口
 *
 * @date 2025-03-12
 */
public interface TestApplicationMapper
{
    /**
     * 查询检测申请
     *
     * @param id 检测申请主键
     * @return 检测申请
     */
    public TestApplication selectTestApplicationById(Long id);

    /**
     * 查询检测申请列表
     *
     * @param testApplication 检测申请
     * @return 检测申请集合
     */
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication);

    /**
     * 新增检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    public int insertTestApplication(TestApplication testApplication);

    /**
     * 修改检测申请
     *
     * @param testApplication 检测申请
     * @return 结果
     */
    public int updateTestApplication(TestApplication testApplication);

    /**
     * 删除检测申请
     *
     * @param id 检测申请主键
     * @return 结果
     */
    public int deleteTestApplicationById(Long id);

    /**
     * 批量删除检测申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestApplicationByIds(Long[] ids);
}
