package com.bocloud.domain.web.dto;

import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 个人认证信息DTO
 */
@Data
public class PersonalVerificationDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 真实姓名 */
    @NotBlank(message = "真实姓名不能为空")
    @Length(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    /** 身份证号码 */
    @NotBlank(message = "身份证号码不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号码格式不正确")
    private String idCardNumber;


    /** 身份证人像面图片路径 */
    @NotBlank(message = "身份证人像面不能为空")
    private String idCardPortrait;

    /** 身份证国徽面图片路径 */
    @NotBlank(message = "身份证国徽面不能为空")
    private String idCardEmblem;

    /** 状态，0-待审核， 1-正常，2-未通过 */
    private String status;

    /** 认证失败原因 */
    @Length(max = 500, message = "认证失败原因长度不能超过500个字符")
    private String failReason;
} 