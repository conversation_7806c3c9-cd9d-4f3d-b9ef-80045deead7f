package com.bocloud.admin.controller;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.domain.system.demand.DemandSolution;
import com.bocloud.domain.system.demand.DemandSolutionAuditRequest;
import com.bocloud.admin.business.service.IDemandSolutionService;
import com.bocloud.admin.business.service.ISysAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import javax.validation.Valid;
import com.bocloud.admin.business.service.IEnterpriseDemandService;

/**
 * 需求方案管理
 */
@RestController
@RequestMapping("/admin/demandSolution")
public class DemandSolutionController extends BaseController {

    @Autowired
    private IDemandSolutionService demandSolutionService;

    @Autowired
    private ISysAuditService sysAuditService;

    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    /**
     * 获取方案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DemandSolution demandSolution) {
        startPage();
        List<DemandSolution> list = demandSolutionService.selectDemandSolutionList(demandSolution);
        return getDataTable(list);
    }

    /**
     * 获取方案详细信息
     */
    @GetMapping("/{solutionId}")
    public AjaxResult getInfo(@PathVariable Long solutionId) {
        return success(demandSolutionService.selectDemandSolutionById(solutionId));
    }

    /**
     * 更新方案状态
     */
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody DemandSolution demandSolution) {
        return toAjax(demandSolutionService.updateDemandSolution(demandSolution));
    }

    /**
     * 审核方案
     */
    @PutMapping("/audit")
    public AjaxResult audit(@Valid @RequestBody DemandSolutionAuditRequest request) {
        try {
            int rows = demandSolutionService.auditSolution(request, getUserId(), getLoginUser().getUsername());
            if (rows > 0) {
                return success();
            } else {
                return error("方案审核失败");
            }
        } catch (Exception e) {
            return error("方案审核失败：" + e.getMessage());
        }
    }
}