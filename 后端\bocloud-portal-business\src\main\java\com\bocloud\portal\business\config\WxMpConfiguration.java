package com.bocloud.portal.business.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信公众平台配置
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.mp")
public class WxMpConfiguration {

    /**
     * 微信公众号appId
     */
    private String appId;

    /**
     * 微信公众号secret
     */
    private String secret;

    /**
     * 微信公众号token
     */
    private String token;

    @Bean
    public WxMpService wxMpService() {
        log.info("[WxMpConfiguration] 开始初始化微信公众平台服务，appId: {}, token: {}", appId, token);
        
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(appId);
        config.setSecret(secret);
        config.setToken(token);
        // config.setAesKey(aesKey);
        
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);
        
        log.info("[WxMpConfiguration] 微信公众平台服务初始化完成，token: {}", token);
        return wxMpService;
    }
} 