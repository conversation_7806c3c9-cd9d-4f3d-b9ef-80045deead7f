package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.web.DemandSolutionInvite;
import com.bocloud.domain.web.dto.DemandSolutionInviteActionDTO;
import com.bocloud.portal.business.service.IEnterpriseDemandService;
import com.bocloud.portal.business.service.IDemandSolutionInviteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 需求解决方案邀请Controller
 */
@RestController
@RequestMapping("/membercenter/invite")
public class DemandSolutionInviteController extends BaseController {

    @Autowired
    private IDemandSolutionInviteService demandSolutionInviteService;

    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    /**
     * 获取我收到的需求邀请列表
     * dateRangeType 时间范围：7-最近7天，30-最近30天，90-最近90天
     */
    @GetMapping("/list")
    public TableDataInfo list(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "keyword", required = false) String keyword) {
        startPage();
        // 构建查询条件
        DemandSolutionInvite query = new DemandSolutionInvite();
        query.setInvitedUserId(SecurityUtils.getUserId());
        if (status != null) {
            query.setInviteStatus(status);
        }
        if (keyword != null) {
            query.setKeyword(keyword);
        }
        // 查询邀请列表
        List<DemandSolutionInvite> list = demandSolutionInviteService.selectDemandSolutionInviteList(query);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (DemandSolutionInvite invite : list) {
            Map<String, Object> result = new HashMap<>();
            result.put("invite", invite);
            // 只有当需求存在时才查询需求详情
            if (invite.getDemandExists() == 1) {
                EnterpriseDemand demand = enterpriseDemandService.selectEnterpriseDemandById(invite.getDemandId());
                result.put("demand", demand);
            } else {
                result.put("demand", null);
            }
            resultList.add(result);
        }
        return getDataTable(resultList);
    }

    /**
     * 获取我收到的邀请详情
     */
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam("inviteId") Long inviteId) {
        // 先查邀请信息
        DemandSolutionInvite invite = demandSolutionInviteService.selectDemandSolutionInviteById(inviteId);
        // 校验是否是当前用户
        if (invite == null || !invite.getInvitedUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权访问");
        }
        EnterpriseDemand demand = enterpriseDemandService.selectEnterpriseDemandById(invite.getDemandId());
        Map<String, Object> result = new HashMap<>();
        result.put("invite", invite);
        result.put("demand", demand);
        return AjaxResult.success(result);
    }

    /**
     * 接受我的邀请
     */
    @PostMapping("/accept")
    public AjaxResult accept(@Validated @RequestBody DemandSolutionInviteActionDTO actionDTO) {
        Long inviteId = actionDTO.getInviteId();
        // 判断是否是当前用户
        if (!demandSolutionInviteService.selectDemandSolutionInviteById(inviteId).getInvitedUserId()
                .equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权访问");
        }
        return AjaxResult.success(demandSolutionInviteService.acceptDemandSolutionInvite(inviteId));
    }

    /**
     * 拒绝我的邀请
     */
    @PostMapping("/reject")
    public AjaxResult reject(@Validated @RequestBody DemandSolutionInviteActionDTO actionDTO) {
        Long inviteId = actionDTO.getInviteId();
        // 判断是否是当前用户
        if (!demandSolutionInviteService.selectDemandSolutionInviteById(inviteId).getInvitedUserId()
                .equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权访问");
        }
        return AjaxResult.success(demandSolutionInviteService.rejectDemandSolutionInvite(inviteId));
    }

}
