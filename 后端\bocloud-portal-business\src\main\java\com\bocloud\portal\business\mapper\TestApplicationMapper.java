package com.bocloud.portal.business.mapper;

import com.bocloud.domain.system.test_application.TestApplication;
import java.util.List;

/**
 * 测试申请Mapper接口
 * 
 * <AUTHOR>
 */
public interface TestApplicationMapper {
    /**
     * 查询测试申请
     * 
     * @param id 测试申请主键
     * @return 测试申请
     */
    public TestApplication selectTestApplicationById(Long id);

    /**
     * 查询测试申请列表
     * 
     * @param testApplication 测试申请
     * @return 测试申请集合
     */
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication);

    /**
     * 新增测试申请
     * 
     * @param testApplication 测试申请
     * @return 结果
     */
    public int insertTestApplication(TestApplication testApplication);

    /**
     * 修改测试申请
     * 
     * @param testApplication 测试申请
     * @return 结果
     */
    public int updateTestApplication(TestApplication testApplication);

    /**
     * 删除测试申请
     * 
     * @param id 测试申请主键
     * @return 结果
     */
    public int deleteTestApplicationById(Long id);

    /**
     * 批量删除测试申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTestApplicationByIds(Long[] ids);
} 