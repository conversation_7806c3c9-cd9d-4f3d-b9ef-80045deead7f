package com.bocloud.admin.controller;


import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bocloud.admin.business.service.ICourseVideoService;
import com.bocloud.admin.business.service.ICoursesService;
import com.bocloud.domain.system.online_education.CourseVideo;
import com.bocloud.domain.system.online_education.Courses;
import com.bocloud.common.annotation.Log;
import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.BusinessType;
import com.bocloud.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课程Controller
 *
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/system/courses")
public class CoursesController extends BaseController
{
    @Autowired
    private ICoursesService coursesService;

    @Autowired
    private ICourseVideoService courseVideoService;

    /**
     * 查询课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:courses:list')")
    @GetMapping("/list")
    public TableDataInfo list(Courses courses)
    {
        startPage();
        List<Courses> list = coursesService.selectCoursesList(courses);
        return getDataTable(list);
    }

    /**
     * 导出课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:courses:export')")
    @Log(title = "课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Courses courses)
    {
        List<Courses> list = coursesService.selectCoursesList(courses);
        ExcelUtil<Courses> util = new ExcelUtil<Courses>(Courses.class);
        util.exportExcel(response, list, "课程数据");
    }

    /**
     * 获取课程详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:courses:query')")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Integer courseId)
    {
        Courses course = coursesService.selectCoursesByCourseId(courseId);
        // 获取课程视频列表并设置到course对象中
        CourseVideo courseVideo = new CourseVideo();
        courseVideo.setCourseId(courseId);
        List<CourseVideo> videos = courseVideoService.selectCourseVideoList(courseVideo);
        course.setVideos(videos);
        return success(course);
    }

    /**
     * 新增课程
     */
    @PreAuthorize("@ss.hasPermi('system:courses:add')")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Courses courses)
    {
        return toAjax(coursesService.insertCourses(courses));
    }

    /**
     * 修改课程
     */
    @PreAuthorize("@ss.hasPermi('system:courses:edit')")
    @Log(title = "课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Courses courses)
    {
        return toAjax(coursesService.updateCourses(courses));
    }

    /**
     * 删除课程
     */
    @PreAuthorize("@ss.hasPermi('system:courses:remove')")
    @Log(title = "课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Integer[] courseIds)
    {
        return toAjax(coursesService.deleteCoursesByCourseIds(courseIds));
    }

    /**
     * 审核通过课程
     */
    @PreAuthorize("@ss.hasPermi('system:courses:audit')")
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/pass/{courseId}")
    public AjaxResult audit(@PathVariable("courseId") Integer courseId) {
        return toAjax(coursesService.auditCourse(courseId));
    }

    /**
     * 下架课程
     */
    @PreAuthorize("@ss.hasPermi('system:courses:takedown')")
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    @PutMapping("/takedown/{courseId}")
    public AjaxResult takedown(@PathVariable("courseId") Integer courseId) {
        return toAjax(coursesService.takedownCourse(courseId));
    }
}
