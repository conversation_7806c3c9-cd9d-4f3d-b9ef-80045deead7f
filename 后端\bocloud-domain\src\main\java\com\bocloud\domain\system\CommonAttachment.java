package com.bocloud.domain.system;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 通用附件对象 common_attachment
 */
@Data
public class CommonAttachment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 附件ID */
    private Long attachmentId;

    /** 业务ID */
    @Excel(name = "业务ID")
    private Long businessId;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String originalFilename;

    /** 相对路径 */
    @Excel(name = "相对路径")
    private String relativePath;

    /** 完整URL */
    @Excel(name = "完整URL")
    private String fullUrl;

    /** 文件扩展名 */
    @Excel(name = "文件扩展名")
    private String fileExtension;

    /** 文件大小(字节) */
    @Excel(name = "文件大小(字节)")
    private Long fileSize;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 上传人ID */
    @Excel(name = "上传人ID")
    private Long uploadBy;

    /** 上传人ID */
    @Excel(name = "上传时间")
    private Date uploadDate;



    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("attachmentId", getAttachmentId())
            .append("businessId", getBusinessId())
            .append("businessType", getBusinessType())
            .append("originalFilename", getOriginalFilename())
            .append("relativePath", getRelativePath())
            .append("fullUrl", getFullUrl())
            .append("fileExtension", getFileExtension())
            .append("fileSize", getFileSize())
            .append("fileType", getFileType())
            .toString();
    }
} 