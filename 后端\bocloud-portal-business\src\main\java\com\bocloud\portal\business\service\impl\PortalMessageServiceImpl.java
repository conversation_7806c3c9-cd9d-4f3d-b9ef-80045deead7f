package com.bocloud.portal.business.service.impl;

import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import com.bocloud.domain.web.PortalMessageStatus;
import com.bocloud.portal.business.mapper.PortalMessageMapper;
import com.bocloud.portal.business.mapper.PortalMessageStatusMapper;
import com.bocloud.portal.business.service.IPortalMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息管理服务实现
 */
@Service
public class PortalMessageServiceImpl implements IPortalMessageService {
    
    @Autowired
    private PortalMessageMapper portalMessageMapper;
    
    @Autowired
    private PortalMessageStatusMapper portalMessageStatusMapper;
    
    @Override
    public List<PortalMessage> selectPortalMessageList(PortalMessageQuery query) {
        return portalMessageMapper.selectPortalMessageList(query);
    }
    
    @Override
    public PortalMessage selectPortalMessageById(Long messageId) {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        // 获取消息详情
        PortalMessage message = portalMessageMapper.selectPortalMessageById(messageId, userId);
        if (message != null) {
            // 更新消息状态为已读
            portalMessageStatusMapper.insertOrUpdateMessageStatus(messageId, userId, "1");
        }
        return message;
    }
    
    @Override
    public int updateMessageStatus(PortalMessageStatus status) {
        return portalMessageStatusMapper.insertOrUpdateMessageStatus(
            status.getMessageId(), 
            status.getUserId(), 
            status.getStatus()
        );
    }
    
    @Override
    public int updateMessageStatusBatch(PortalMessageStatus status) {
        // 构造批量状态对象列表
        List<PortalMessageStatus> statusList = new java.util.ArrayList<>();
        for (Long messageId : status.getMessageIds()) {
            PortalMessageStatus s = new PortalMessageStatus();
            s.setMessageId(messageId);
            s.setUserId(status.getUserId());
            s.setStatus(status.getStatus());
            statusList.add(s);
        }
        // 调用批量插入或更新方法
        return portalMessageStatusMapper.batchInsertOrUpdateMessageStatus(statusList);
    }
    
    @Override
    public int getUnreadMessageCount() {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();
        // 获取未读消息数量
        return portalMessageMapper.selectUnreadMessageCount(userId);
    }
} 