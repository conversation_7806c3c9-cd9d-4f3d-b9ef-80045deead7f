<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.PortalUserPersonalInfoMapper">
    
    <resultMap type="com.bocloud.domain.web.PersonalInfo" id="PersonalInfoResult">
        <id     property="id"           column="id"           />
        <result property="userId"       column="user_id"      />
        <result property="nickname"     column="nickname"     />
        <result property="avatar"       column="avatar"       />
        <result property="realName"     column="real_name"    />
        <result property="gender"       column="gender"       />
        <result property="idCardNumber" column="id_card_number"/>
        <result property="idCardPortrait" column="id_card_portrait"/>
        <result property="idCardEmblem" column="id_card_emblem"/>
        <result property="occupation"   column="occupation"   />
        <result property="graduationSchool" column="graduation_school"/>
        <result property="graduationYear" column="graduation_year"/>
        <result property="enrollmentYear" column="enrollment_year"/>
        <result property="major"        column="major"        />
        <result property="province"     column="province"     />
        <result property="city"         column="city"         />
        <result property="district"     column="district"     />
        <result property="detailAddress" column="detail_address"/>
        <result property="phone"        column="phone"        />
        <result property="email"        column="email"        />
        <result property="isExpert"     column="is_expert"    />
        <result property="status"       column="status"       />
        <result property="active"       column="active"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
    </resultMap>

    <sql id="selectPersonalInfoVo">
        select id, user_id, nickname, avatar, real_name, gender, id_card_number, occupation, graduation_school, 
               graduation_year, enrollment_year, major, province, city, district, detail_address, phone, email, is_expert, status, active, del_flag,
               create_by, create_time, update_by, update_time, id_card_portrait, id_card_emblem
        from portal_user_personal_info
    </sql>

    <select id="selectPortalUserPersonalInfoByUserId" parameterType="Long" resultMap="PersonalInfoResult">
        <include refid="selectPersonalInfoVo"/>
        where user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectLatestPersonalInfoByUserId" parameterType="Long" resultMap="PersonalInfoResult">
        select p.*
        from portal_user_personal_info p
        left join sys_verification_status v on p.user_id = v.user_id and v.verification_type = '1'
        where p.user_id = #{userId} and p.del_flag = '0'
        order by 
            case when v.status = '3' then 1  -- 已认证的记录优先
                 when v.status = '2' then 2  -- 其次是待审核的记录
                 else 3                      -- 最后是其他状态的记录
            end,
            p.create_time desc               -- 同状态的记录按创建时间倒序
        limit 1
    </select>

    <select id="selectLatestByUserIdAndStatus" resultMap="PersonalInfoResult">
       <include refid="selectPersonalInfoVo"/>
       where user_id = #{userId} 
       and status = #{status}
       and del_flag = '0'
       order by create_time desc
       limit 1
   </select>

   <select id="selectActiveByUserId" parameterType="Long" resultMap="PersonalInfoResult">
       <include refid="selectPersonalInfoVo"/>
       where user_id = #{userId} 
       and status = '1'
       and del_flag = '0'
       order by create_time desc
       limit 1
   </select>

   <update id="updatePortalUserPersonalInfo" parameterType="com.bocloud.domain.web.PersonalInfo">
        update portal_user_personal_info
        <set>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="idCardNumber != null and idCardNumber != ''">id_card_number = #{idCardNumber},</if>
            <if test="occupation != null and occupation != ''">occupation = #{occupation},</if>
            <if test="graduationSchool != null">graduation_school = #{graduationSchool},</if>
            <if test="graduationYear != null">graduation_year = #{graduationYear},</if>
            <if test="enrollmentYear != null">enrollment_year = #{enrollmentYear},</if>
            <if test="major != null">major = #{major},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="isExpert != null">is_expert = #{isExpert},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="active != null and active != ''">active = #{active},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="idCardPortrait != null">id_card_portrait = #{idCardPortrait},</if>
            <if test="idCardEmblem != null">id_card_emblem = #{idCardEmblem},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId} and del_flag = '0'
    </update>

    <select id="selectListByStatus" parameterType="String" resultMap="PersonalInfoResult">
        <include refid="selectPersonalInfoVo"/>
        where status = #{status} and del_flag = '0'
        order by create_time desc
    </select>

</mapper> 