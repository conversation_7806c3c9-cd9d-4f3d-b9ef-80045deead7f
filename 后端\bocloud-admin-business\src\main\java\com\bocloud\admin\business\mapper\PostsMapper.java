package com.bocloud.admin.business.mapper;


import com.bocloud.domain.system.post.Posts;

import java.util.List;

public interface PostsMapper
{
    /**
     * 查询信息发布
     *
     * @param postId 信息发布主键
     * @return 信息发布
     */
    public Posts selectPostsByPostId(Long postId);

    /**
     * 查询信息发布列表
     *
     * @param posts 信息发布
     * @return 信息发布集合
     */
    public List<Posts> selectPostsList(Posts posts);

    /**
     * 新增信息发布
     *
     * @param posts 信息发布
     * @return 结果
     */
    public int insertPosts(Posts posts);

    /**
     * 修改信息发布
     *
     * @param posts 信息发布
     * @return 结果
     */
    public int updatePosts(Posts posts);

    /**
     * 删除信息发布
     *
     * @param postId 信息发布主键
     * @return 结果
     */
    public int deletePostsByPostId(Long postId);

    /**
     * 批量删除信息发布
     *
     * @param postIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePostsByPostIds(Long[] postIds);
}