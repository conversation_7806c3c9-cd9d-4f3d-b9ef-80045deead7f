package com.bocloud.portal.controller;

import com.bocloud.portal.business.service.ISmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Random;

@RestController
@RequestMapping("/api/sms")
public class TestSmsController {

    @Autowired
    private ISmsService smsService;

    /**
     * 测试发送短信验证码（验证码由后端随机生成）
     * @param phone 手机号
     * @return 发送结果
     */
    @GetMapping("/send")
    public String sendSms(@RequestParam String phone) {
        String code = String.format("%04d", new Random().nextInt(10000));
        boolean success = smsService.sendVerificationCode(phone, code);
        return success ? ("发送成功，验证码：" + code) : "发送失败";
    }
}