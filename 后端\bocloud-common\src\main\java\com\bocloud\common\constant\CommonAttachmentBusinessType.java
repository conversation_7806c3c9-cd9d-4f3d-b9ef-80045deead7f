package com.bocloud.common.constant;

/**
 * 业务类型常量
 * 
 * <AUTHOR>
 */
public class CommonAttachmentBusinessType {

    /** 新闻 */
    public static final String POSTS = "posts";

    /** 解决方案 */
    public static final String DEMAND_SOLUTION = "demand_solution";
    
    /** 测试申请用户附件 */
    public static final String TEST_APPLICATION_ATTACHMENT = "test_application_attachment";

    /** 测试申请平台报告 */
    public static final String TEST_APPLICATION_REPORT = "test_application_report";
    
    /** 中试申请用户附件 */
    public static final String PILOT_APPLICATION_ATTACHMENT = "pilot_application_attachment";

    /** 中试申请平台报告 */
    public static final String PILOT_APPLICATION_REPORT = "pilot_application_report";

    public static final String ENTERPRISE_DEMAND = "enterprise_demand";
    
    /**
     * 获取业务类型名称
     * 
     * @param type 业务类型
     * @return 业务类型名称
     */
    public static String getBusinessTypeName(String type) {
        switch (type) {
            case DEMAND_SOLUTION:
                return "解决方案";
            case TEST_APPLICATION_ATTACHMENT:
                return "测试申请附件";
            case TEST_APPLICATION_REPORT:
                return "测试申请报告";
            case PILOT_APPLICATION_ATTACHMENT:
                return "中试申请用户附件";
            case PILOT_APPLICATION_REPORT:
                return "中试申请平台报告";
            default:
                return "未知类型";
        }
    }
} 