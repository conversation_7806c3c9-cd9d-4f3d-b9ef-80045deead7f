package com.bocloud.portal.controller.api;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.domain.system.demand.EnterpriseDemand;
import com.bocloud.domain.system.demand.EnterpriseDemandStatus;
import com.bocloud.portal.business.service.IEnterpriseDemandService;
import com.bocloud.portal.business.service.IPortalUserService;
import com.bocloud.domain.web.PortalUser;
import com.bocloud.domain.web.EnterpriseInfo;
import com.bocloud.domain.web.PersonalInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Arrays;
import com.bocloud.portal.vo.PublisherInfoVO;
import com.bocloud.portal.business.service.IEnterpriseInfoService;
import com.bocloud.portal.business.service.IPortalUserPersonalInfoService;
import com.bocloud.common.enums.PersonalInfoStatus;
import com.bocloud.common.enums.EnterpriseInfoStatus;

/**
 * 企业需求公开接口
 */
@RestController
@RequestMapping("/api/demand")
public class ApiDemandController extends BaseController {
    
    @Autowired
    private IEnterpriseDemandService enterpriseDemandService;

    @Autowired
    private IPortalUserService portalUserService;

    @Autowired
    private IEnterpriseInfoService enterpriseInfoService;

    @Autowired
    private IPortalUserPersonalInfoService personalInfoService;

    /**
     * 获取企业需求列表
     * @param enterpriseDemand 企业需求查询参数
     * @param timeRange 时间范围：1-最近一周，2-最近一月，3-最近三月，4-最近半年，0-全部
     * @param budgetRange 预算范围：1-5万以下，2-5-10万，3-10-20万，4-20-50万，5-50万以上，0-全部
     */
    @GetMapping("/list")
    public TableDataInfo list(
            EnterpriseDemand enterpriseDemand, 
            @RequestParam(required = false, defaultValue = "0") Integer timeRange,
            @RequestParam(required = false, defaultValue = "0") Integer budgetRange) {
        startPage();
        
        // 根据时间范围设置查询条件
        if (timeRange != null && timeRange > 0) {
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            Date startTime;
            
            switch (timeRange) {
                case 1: // 最近一周
                    calendar.add(Calendar.DAY_OF_MONTH, -7);
                    break;
                case 2: // 最近一月
                    calendar.add(Calendar.MONTH, -1);
                    break;
                case 3: // 最近三月
                    calendar.add(Calendar.MONTH, -3);
                    break;
                case 4: // 最近半年
                    calendar.add(Calendar.MONTH, -6);
                    break;
                default:
                    // 不设置时间范围
                    break;
            }
            
            if (timeRange <= 4) {
                startTime = calendar.getTime();
                if (enterpriseDemand.getParams() == null) {
                    enterpriseDemand.setParams(new HashMap<String, Object>());
                }
                enterpriseDemand.getParams().put("beginCreateTime", startTime);
                enterpriseDemand.getParams().put("endCreateTime", endTime);
            }
        }
        
        // 根据预算范围设置查询条件
        if (budgetRange != null && budgetRange > 0) {
            BigDecimal minBudgetValue = null;
            BigDecimal maxBudgetValue = null;
            
            switch (budgetRange) {
                case 1: // 5万以下
                    maxBudgetValue = new BigDecimal(50000);
                    break;
                case 2: // 5-10万
                    minBudgetValue = new BigDecimal(50000);
                    maxBudgetValue = new BigDecimal(100000);
                    break;
                case 3: // 10-20万
                    minBudgetValue = new BigDecimal(100000);
                    maxBudgetValue = new BigDecimal(200000);
                    break;
                case 4: // 20-50万
                    minBudgetValue = new BigDecimal(200000);
                    maxBudgetValue = new BigDecimal(500000);
                    break;
                case 5: // 50万以上
                    minBudgetValue = new BigDecimal(500000);
                    break;
                default:
                    // 不设置预算范围
                    break;
            }
            
            if (enterpriseDemand.getParams() == null) {
                enterpriseDemand.setParams(new HashMap<String, Object>());
            }
            
            if (minBudgetValue != null) {
                enterpriseDemand.getParams().put("minBudgetValue", minBudgetValue);
            }
            
            if (maxBudgetValue != null) {
                enterpriseDemand.getParams().put("maxBudgetValue", maxBudgetValue);
            }
        }
        // 只显示“公开以后”且不包括 WAIT_PUBLIC 和 FAILED 的所有状态的需求
        List<String> statusList = Arrays.asList(
            EnterpriseDemandStatus.BIDDING.getCode(),
            EnterpriseDemandStatus.SELECTING.getCode(),
            EnterpriseDemandStatus.PLATFORM_REVIEW.getCode(),
            EnterpriseDemandStatus.WAIT_SIGN.getCode(),
            EnterpriseDemandStatus.IN_PROGRESS.getCode(),
            EnterpriseDemandStatus.COMPLETED.getCode()
        );
        enterpriseDemand.setStatusList(statusList);
        List<EnterpriseDemand> list = enterpriseDemandService.selectEnterpriseDemandList(enterpriseDemand);
        return getDataTable(list);
    }

    /**
     * 获取企业需求详细信息
     */
    @GetMapping(value = "/{demandId}")
    public AjaxResult getInfo(@PathVariable("demandId") Long demandId) {
        EnterpriseDemand demand = enterpriseDemandService.selectEnterpriseDemandById(demandId);
        // 只显示“公开以后”且不包括 WAIT_PUBLIC 和 FAILED 的所有状态的需求
        List<String> statusList = Arrays.asList(
            EnterpriseDemandStatus.BIDDING.getCode(),
            EnterpriseDemandStatus.SELECTING.getCode(),
            EnterpriseDemandStatus.PLATFORM_REVIEW.getCode(),
            EnterpriseDemandStatus.WAIT_SIGN.getCode(),
            EnterpriseDemandStatus.IN_PROGRESS.getCode(),
            EnterpriseDemandStatus.COMPLETED.getCode()
        );
        if (statusList.contains(demand.getStatus())) {
            return success(demand);
        } else {
            return error("需求不存在");
        }
    }

    /**
     * 获取需求发布者信息（企业或个人）
     */
    @GetMapping("/{demandId}/publisher")
    public AjaxResult getPublisherInfo(@PathVariable("demandId") Long demandId) {
        EnterpriseDemand demand = enterpriseDemandService.selectEnterpriseDemandById(demandId);
        if (demand == null) {
            return error("需求不存在");
        }
        Long portalUserId = demand.getPortalUserId();
        PortalUser user = portalUserService.selectPortalUserById(portalUserId);
        if (user == null) {
            return error("发布者不存在");
        }
        
        // 根据用户类型加载详细信息
        if (user.isEnterpriseUser()) {
            // 企业用户，加载企业信息
            EnterpriseInfo ent = enterpriseInfoService.getEnterpriseInfoByStatus(user.getId(), EnterpriseInfoStatus.STATUS_NORMAL.getCode());
            user.setEnterpriseInfo(ent);
        } else if (user.isPersonalUser()) {
            // 个人用户，加载个人信息
            PersonalInfo per = personalInfoService.selectLatestByUserIdAndStatus(user.getId(), PersonalInfoStatus.STATUS_NORMAL.getCode());
            user.setPersonalInfo(per);
        }
        
        PublisherInfoVO vo = new PublisherInfoVO();
        vo.setType(user.getTypeDesc());
        vo.setCertified(VerificationStatus.APPROVED.getCode().equals(user.getVerificationStatus()));
        vo.setCertifiedDesc(user.getVerificationStatusDesc());
        vo.setDeadline(demand.getDeliveryTime());
        vo.setRegion(user.getDisplayAddress());
        
        if (user.isEnterpriseUser()) {
            EnterpriseInfo ent = user.getEnterpriseInfo();
            if (ent != null) {
                vo.setName(ent.getCompanyName());
                vo.setIndustry(ent.getIndustry());
            } else {
                vo.setName(user.getDisplayName() + "（企业信息未完善）");
            }
        } else if (user.isPersonalUser()) {
            PersonalInfo per = user.getPersonalInfo();
            if (per != null) {
                vo.setName(per.getRealName());
                vo.setIndustry(per.getOccupation());
            } else {
                vo.setName(user.getDisplayName() + "（个人信息未完善）");
            }
        } else {
            vo.setName(user.getDisplayName());
        }
        return success(vo);
    }
} 