<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.DemandContractMapper">
    
    <resultMap type="com.bocloud.domain.system.demand.DemandContract" id="DemandContractResult">
        <id     property="contractId"      column="contract_id"      />
        <result property="contractNo"      column="contract_no"      />
        <result property="demandId"        column="demand_id"        />
        <result property="solutionId"      column="solution_id"      />
        <result property="contractName"    column="contract_name"    />
        <result property="contractFile"    column="contract_file"    />
        <result property="signDate"        column="sign_date"        />
        <result property="effectiveDate"   column="effective_date"   />
        <result property="expiryDate"      column="expiry_date"      />
        <result property="contractAmount"  column="contract_amount"  />
        <result property="chargeType"      column="charge_type"      />
        <result property="fixedAmount"     column="fixed_amount"     />
        <result property="chargeRate"      column="charge_rate"      />
        <result property="status"          column="status"           />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="delFlag"         column="del_flag"         />
        <result property="remark"          column="remark"           />
    </resultMap>

    <sql id="selectDemandContractVo">
        select contract_id, contract_no, demand_id, solution_id, contract_name, contract_file, 
        sign_date, effective_date, expiry_date, contract_amount, charge_type, fixed_amount, charge_rate, status, create_by, create_time, 
        update_by, update_time, del_flag, remark
        from demand_contract
    </sql>

    <select id="selectDemandContractList" parameterType="com.bocloud.domain.system.demand.DemandContract" resultMap="DemandContractResult">
        <include refid="selectDemandContractVo"/>
        <where>
            <if test="contractNo != null  and contractNo != ''"> and contract_no like concat('%', #{contractNo}, '%')</if>
            <if test="demandId != null "> and demand_id = #{demandId}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="signDate != null "> and sign_date = #{signDate}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectDemandContractById" parameterType="Long" resultMap="DemandContractResult">
        <include refid="selectDemandContractVo"/>
        where contract_id = #{contractId} and del_flag = '0'
    </select>

    <select id="selectDemandContractByDemandId" parameterType="long" resultMap="DemandContractResult">
        <include refid="selectDemandContractVo"/>
        where demand_id = #{demandId} and del_flag = '0'
        limit 1
    </select>

    <update id="updateDemandContract" parameterType="com.bocloud.domain.system.demand.DemandContract">
        update demand_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractNo != null">contract_no = #{contractNo},</if>
            <if test="demandId != null">demand_id = #{demandId},</if>
            <if test="solutionId != null">solution_id = #{solutionId},</if>
            <if test="contractName != null">contract_name = #{contractName},</if>
            <if test="contractFile != null">contract_file = #{contractFile},</if>
            <if test="signDate != null">sign_date = #{signDate},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="fixedAmount != null">fixed_amount = #{fixedAmount},</if>
            <if test="chargeRate != null">charge_rate = #{chargeRate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where contract_id = #{contractId}
    </update>

    <update id="updateDemandContractStatus">
        update demand_contract set status = #{status} where contract_id in
        <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </update>

</mapper> 