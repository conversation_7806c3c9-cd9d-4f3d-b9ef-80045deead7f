package com.bocloud.domain.web;

import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.domain.web.enums.ExpertStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 专家信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpertInfo extends BaseEntity {

    /** 专家ID */
    private Long expertId;

    /** 用户ID */
    private Long userId;

    /** 专家姓名 */
    private String expertName;
    
    /** 专家头像 */
    private String avatar;
    
    /** 专家类型 */
    private String expertType;

    /** 职称 */
    private String title;

    /** 研究领域 */
    private String researchField;

    /** 所在单位 */
    private String organization;

    /** 所在部门 */
    private String department;

    /** 研究方向 */
    private String researchDirection;

    /** 关键词 */
    private String keywords;

    /** 个人简介 */
    private String introduction;

    /** 从业年限 */
    private Integer yearsOfExperience;

    /** 总咨询次数 */
    private Integer totalConsultations;

    /** 平均响应时间(分钟) */
    private Integer averageResponseTime;

    /** 论文列表 */
    private List<ExpertPaper> expertPapers;

    /** 工作履历列表 */
    private List<ExpertWorkExperience> expertWorkExperiences;

    /** 状态，0-待审核， 1-正常，2-未通过 */
    private String status;

    /** 删除标志（0代表存在 1代表删除） */
    private String delFlag;

    /**
     * 判断专家状态是否为正常
     * 
     * @return 是否为正常状态
     */
    public boolean isNormalStatus() {
        return ExpertStatusEnum.isNormal(this.status);
    }

    /**
     * 判断专家状态是否为待审核
     * 
     * @return 是否为待审核状态
     */
    public boolean isPendingStatus() {
        return ExpertStatusEnum.isPending(this.status);
    }

    /**
     * 判断专家状态是否为未通过
     * 
     * @return 是否为未通过状态
     */
    public boolean isRejectedStatus() {
        return ExpertStatusEnum.isRejected(this.status);
    }

    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDesc() {
        return ExpertStatusEnum.getDesc(this.status);
    }
} 