package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.core.page.TableDataInfo;
import com.bocloud.common.utils.SecurityUtils;
import com.bocloud.domain.web.PortalMessage;
import com.bocloud.domain.web.PortalMessageQuery;
import com.bocloud.domain.web.PortalMessageStatus;
import com.bocloud.portal.business.service.IPortalMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 个人中心-消息管理
 */
@RestController
@RequestMapping("/membercenter/message")
public class MemberCenterMessageController extends BaseController {
    
    @Autowired
    private IPortalMessageService portalMessageService;
    
    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PortalMessageQuery query) {
        // 设置当前用户ID
        query.setReceiverId(SecurityUtils.getUserId());
        query.setUserId(getUserId());
        // 分页查询
        startPage();
        List<PortalMessage> list = portalMessageService.selectPortalMessageList(query);
        return getDataTable(list);
    }
    
    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    public AjaxResult getInfo(@PathVariable Long messageId) {
        // 获取消息详情并自动更新为已读
        PortalMessage message = portalMessageService.selectPortalMessageById(messageId);
        return success(message);
    }
    
    /**
     * 更新消息状态
     */
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody PortalMessageStatus status) {
        // 设置用户ID
        status.setUserId(SecurityUtils.getUserId());
        return toAjax(portalMessageService.updateMessageStatus(status));
    }
    
    /**
     * 批量更新消息状态
     */
    @PutMapping("/status/batch")
    public AjaxResult updateStatusBatch(@RequestBody PortalMessageStatus status) {
        // 设置用户ID
        status.setUserId(SecurityUtils.getUserId());
        return toAjax(portalMessageService.updateMessageStatusBatch(status));
    }

    @GetMapping("/unread/count")
    public AjaxResult getUnreadMessageCount() {
        return success(portalMessageService.getUnreadMessageCount());
    }
} 