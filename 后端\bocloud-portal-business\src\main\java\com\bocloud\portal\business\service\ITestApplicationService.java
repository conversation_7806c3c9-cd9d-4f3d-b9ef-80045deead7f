package com.bocloud.portal.business.service;

import com.bocloud.domain.system.test_application.TestApplication;
import java.util.List;

/**
 * 测试申请Service接口
 * 
 * <AUTHOR>
 */
public interface ITestApplicationService {
    /**
     * 查询测试申请
     * 
     * @param id 测试申请主键
     * @return 测试申请
     */
    public TestApplication selectTestApplicationById(Long id);

    /**
     * 查询测试申请列表
     * 
     * @param testApplication 测试申请
     * @return 测试申请集合
     */
    public List<TestApplication> selectTestApplicationList(TestApplication testApplication);

    /**
     * 新增测试申请
     * 
     * @param testApplication 测试申请
     * @return 结果
     */
    public int insertTestApplication(TestApplication testApplication);

    /**
     * 修改测试申请
     * 
     * @param testApplication 测试申请
     * @return 结果
     */
    public int updateTestApplication(TestApplication testApplication);

    /**
     * 获取下一个申请序号
     * 
     * @param prefix 申请编号前缀
     * @return 下一个序号
     */
    public int getNextApplicationSeq(String prefix);

    /**
     * 删除测试申请信息
     * 
     * @param id 测试申请主键
     * @param userId 用户id
     * @return 结果
     */
    public int deleteTestApplicationById(Long id, Long userId);
} 