package com.bocloud.admin.business.service;

import java.util.List;
import java.math.BigDecimal;
import com.bocloud.domain.system.demand.DemandContract;
import com.bocloud.domain.system.demand.dto.ContractAuditDTO;

/**
 * 需求合同Service接口
 * 
 * <AUTHOR>
 */
public interface IDemandContractService {
    /**
     * 查询需求合同列表
     * 
     * @param demandContract 需求合同
     * @return 需求合同集合
     */
    public List<DemandContract> selectDemandContractList(DemandContract demandContract);

    /**
     * 查询需求合同
     * 
     * @param contractId 需求合同主键
     * @return 需求合同
     */
    public DemandContract selectDemandContractById(Long contractId);

    /**
     * 修改需求合同
     * 
     * @param demandContract 需求合同
     * @return 结果
     */
    public int updateDemandContract(DemandContract demandContract);

    /**
     * 批量审核通过
     * 
     * @param contractIds 合同ID数组
     * @param chargeType 收费类型（1固定收费 2比例收费）
     * @param fixedAmount 固定收费金额
     * @return 结果
     */
    public int approveContracts(Long[] contractIds, String chargeType, BigDecimal fixedAmount);

    /**
     * 合同审核（通过/不通过，单个审核）
     * @param auditDTO 合同审核DTO
     * @return 结果
     */
    int auditContract(ContractAuditDTO auditDTO);

    /**
     * 根据需求ID获取合同信息
     */
    DemandContract selectDemandContractByDemandId(Long demandId);
} 