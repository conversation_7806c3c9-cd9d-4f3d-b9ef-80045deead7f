package com.bocloud.domain.system.demand;

import com.bocloud.common.annotation.Excel;
import com.bocloud.common.core.domain.BaseEntity;
import com.bocloud.common.enums.DemandSolutionStatus;
import com.bocloud.domain.system.CommonAttachment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 需求方案对象 demand_solution
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DemandSolution extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 方案ID */
    private Long solutionId;

    /** 关联的需求ID */
    @Excel(name = "需求ID")
    private Long demandId;

    /** 方案提交者ID */
    @Excel(name = "提交者ID")
    private Long portalUserId;

    /** 方案标题 */
    @Excel(name = "方案标题")
    private String solutionTitle;

    /** 背景理解 */
    @Excel(name = "背景理解")
    private String backgroundUnderstanding;

    /** 技术方案 */
    @Excel(name = "技术方案")
    private String technicalSolution;

    /** 预估费用 */
    @Excel(name = "预估费用")
    private BigDecimal estimatedBudget;

    /** 预计工期(天) */
    @Excel(name = "预计工期")
    private Integer estimatedDuration;

    /** 实施计划 */
    @Excel(name = "实施计划")
    private String implementationPlan;

    /** 附件URL列表 */
    private List<CommonAttachment> attachments = new ArrayList<>();

    /** 状态（0=待审核,1=待采纳，2=已采纳，待审核,3=已中标，-1=已驳回，-2=未中标） */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=待采纳，2=已采纳，待审核,3=已中标，-1=已驳回，-2=未中标")
    private String status;

    /** 方案编号 */
    @Excel(name = "方案编号")
    private String solutionNo;

    /** 需求编号 */
    @Excel(name = "需求编号")
    private String demandNo;

    /** 邀请ID（关联平台邀请） */
    @Excel(name = "邀请ID")
    private Long inviteId;

    /** 技术方案（最多5条） */
    private List<String> technicalSolutions;

    /** 技术指标（最多5条） */
    private List<String> technicalIndicators;

    /** 补充说明（最多5条） */
    private List<String> additionalNotes;

    /** 关键字搜索 */
    private String keyword;

    /** 删除标志（0代表存在 1代表删除） */
    @Excel(name = "删除标志", readConverterExp = "0=存在,1=删除")
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("solutionId", getSolutionId())
                .append("demandId", getDemandId())
                .append("portalUserId", getPortalUserId())
                .append("solutionTitle", getSolutionTitle())
                .append("backgroundUnderstanding", getBackgroundUnderstanding())
                .append("technicalSolution", getTechnicalSolution())
                .append("estimatedBudget", getEstimatedBudget())
                .append("estimatedDuration", getEstimatedDuration())
                .append("implementationPlan", getImplementationPlan())
                .append("additionalNotes", getAdditionalNotes())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
} 