package com.bocloud.portal.controller.membercenter;

import com.bocloud.common.core.controller.BaseController;
import com.bocloud.common.core.domain.AjaxResult;
import com.bocloud.common.enums.VerificationStatus;
import com.bocloud.domain.web.VerificationRecord;
import com.bocloud.portal.business.service.IVerificationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 实名认证接口
 */
@RestController
@RequestMapping("/membercenter/verification")
public class MemberCenterVerificationController extends BaseController {
    
    @Autowired
    private IVerificationRecordService verificationRecordService;

    /**
     * 获取当前用户的认证状态
     */
    @GetMapping("/status")
    public AjaxResult getVerificationStatus() {
        VerificationRecord verificationRecord = verificationRecordService.selectVerificationRecordByUserId(getUserId());
        
        // 如果没有认证记录，返回默认的待认证状态
        if (verificationRecord == null) {
            verificationRecord = new VerificationRecord();
            verificationRecord.setStatus(VerificationStatus.PENDING.getCode()); // 待认证状态
        }
        
        return AjaxResult.success(verificationRecord);
    }

} 