<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bocloud.admin.business.mapper.DemandChargeMapper">
    
    <resultMap type="com.bocloud.domain.system.demand.DemandCharge" id="DemandChargeResult">
        <id     property="chargeId"        column="charge_id"        />
        <result property="demandId"        column="demand_id"        />
        <result property="contractId"      column="contract_id"      />
        <result property="chargeType"      column="charge_type"      />
        <result property="chargeAmount"    column="charge_amount"    />
        <result property="contractAmount"  column="contract_amount"  />
        <result property="chargeRate"      column="charge_rate"      />
        <result property="status"          column="status"           />
        <result property="chargeTime"      column="charge_time"      />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="delFlag"         column="del_flag"         />
        <result property="remark"          column="remark"           />
    </resultMap>

    <sql id="selectDemandChargeVo">
        select charge_id, demand_id, contract_id, charge_type, charge_amount, contract_amount, 
        charge_rate, status, charge_time, create_by, create_time, update_by, update_time, 
        del_flag, remark
        from demand_charge
    </sql>

    <insert id="insertDemandCharge" parameterType="com.bocloud.domain.system.demand.DemandCharge" useGeneratedKeys="true" keyProperty="chargeId">
        insert into demand_charge (
            demand_id,
            contract_id,
            charge_type,
            charge_amount,
            contract_amount,
            charge_rate,
            status,
            create_by,
            create_time,
            update_by,
            update_time,
            del_flag,
            remark
        ) values (
            #{demandId},
            #{contractId},
            #{chargeType},
            #{chargeAmount},
            #{contractAmount},
            #{chargeRate},
            #{status},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            '0',
            #{remark}
        )
    </insert>

    <select id="selectDemandChargeList" parameterType="com.bocloud.domain.system.demand.DemandCharge" resultMap="DemandChargeResult">
        <include refid="selectDemandChargeVo"/>
        <where>
            <if test="demandId != null"> and demand_id = #{demandId}</if>
            <if test="contractId != null"> and contract_id = #{contractId}</if>
            <if test="chargeType != null and chargeType != ''"> and charge_type = #{chargeType}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectDemandChargeById" parameterType="Long" resultMap="DemandChargeResult">
        <include refid="selectDemandChargeVo"/>
        where charge_id = #{chargeId} and del_flag = '0'
    </select>

    <select id="selectDemandChargeByContractId" parameterType="Long" resultMap="DemandChargeResult">
        <include refid="selectDemandChargeVo"/>
        where contract_id = #{contractId} and del_flag = '0'
    </select>

    <update id="updateDemandCharge" parameterType="com.bocloud.domain.system.demand.DemandCharge">
        update demand_charge
        <trim prefix="SET" suffixOverrides=",">
            <if test="demandId != null">demand_id = #{demandId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="chargeAmount != null">charge_amount = #{chargeAmount},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="chargeRate != null">charge_rate = #{chargeRate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="chargeTime != null">charge_time = #{chargeTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where charge_id = #{chargeId}
    </update>

    <update id="updateDemandChargeStatus">
        update demand_charge set status = #{status} where charge_id in
        <foreach collection="chargeIds" item="chargeId" open="(" separator="," close=")">
            #{chargeId}
        </foreach>
    </update>

</mapper> 