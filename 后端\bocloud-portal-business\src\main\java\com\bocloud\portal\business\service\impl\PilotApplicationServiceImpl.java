package com.bocloud.portal.business.service.impl;

import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bocloud.domain.system.pilot_application.PilotApplication;
import com.bocloud.domain.system.CommonAttachment;
import com.bocloud.portal.business.mapper.PilotApplicationMapper;
import com.bocloud.portal.business.service.IPilotApplicationService;
import com.bocloud.portal.business.service.ICommonAttachmentService;
import com.bocloud.common.constant.CommonAttachmentBusinessType;
import com.bocloud.common.constant.PilotApplicationStatus;
import com.bocloud.common.exception.ServiceException;
import com.bocloud.common.utils.DateUtils;
import com.bocloud.common.utils.SecurityUtils;

/**
 * 中试申请Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PilotApplicationServiceImpl implements IPilotApplicationService 
{
    @Autowired
    private PilotApplicationMapper pilotApplicationMapper;

    @Autowired
    private ICommonAttachmentService attachmentService;

    /**
     * 查询中试申请
     * 
     * @param id 中试申请主键
     * @return 中试申请
     */
    @Override
    public PilotApplication selectPilotApplicationById(Long id)
    {
        PilotApplication pilotApplication = pilotApplicationMapper.selectPilotApplicationById(id);
        if (pilotApplication != null) {
            // 查询附件信息
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(id);
            query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
            List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
            pilotApplication.setAttachments(attachments);

            // 查询报告列表
            query = new CommonAttachment();
            query.setBusinessId(id);
            query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_REPORT);
            List<CommonAttachment> reports = attachmentService.selectCommonAttachmentList(query);
            pilotApplication.setReports(reports);
        }
        return pilotApplication;
    }

    /**
     * 查询中试申请列表
     * 
     * @param pilotApplication 中试申请
     * @return 中试申请
     */
    @Override
    public List<PilotApplication> selectPilotApplicationList(PilotApplication pilotApplication)
    {
        return pilotApplicationMapper.selectPilotApplicationList(pilotApplication);
    }

    /**
     * 获取下一个申请编号
     * 
     * @param prefix 前缀
     * @return 下一个序号
     */
    @Override
    public synchronized int getNextApplicationSeq(String prefix) {
        // 查询当天最大的序号
        PilotApplication query = new PilotApplication();
        query.setApplicationNo(prefix + "%");
        List<PilotApplication> list = pilotApplicationMapper.selectPilotApplicationList(query);
        int maxSeq = 0;
        if (list != null && !list.isEmpty()) {
            for (PilotApplication app : list) {
                String appNo = app.getApplicationNo();
                if (appNo != null && appNo.startsWith(prefix)) {
                    try {
                        int seq = Integer.parseInt(appNo.substring(prefix.length()));
                        maxSeq = Math.max(maxSeq, seq);
                    } catch (NumberFormatException e) {
                        // 忽略非数字序号
                    }
                }
            }
        }
        return maxSeq + 1;
    }

    /**
     * 新增中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPilotApplication(PilotApplication pilotApplication)
    {
        // 生成申请编号
        String datePrefix = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String prefix = "PILOT-" + datePrefix + "-";
        int seq = getNextApplicationSeq(prefix);
        pilotApplication.setApplicationNo(prefix + String.format("%04d", seq));
        
        // 设置初始状态为待审核
        pilotApplication.setStatus(PilotApplicationStatus.PENDING_REVIEW);
        
        // 保存申请信息
        int rows = pilotApplicationMapper.insertPilotApplication(pilotApplication);
        
        // 保存附件信息
        if (pilotApplication.getAttachments() != null && !pilotApplication.getAttachments().isEmpty()) {
            pilotApplication.getAttachments().forEach(attachment -> {
                attachment.setBusinessId(pilotApplication.getId());
                attachment.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
                attachmentService.insertCommonAttachment(attachment);
            });
        }
        
        return rows;
    }

    /**
     * 修改中试申请
     * 
     * @param pilotApplication 中试申请
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePilotApplication(PilotApplication pilotApplication)
    {
        // 更新申请信息
        int rows = pilotApplicationMapper.updatePilotApplication(pilotApplication);
        
        // 处理附件
        if (pilotApplication.getAttachments() != null) {
            // 删除旧的附件
            CommonAttachment query = new CommonAttachment();
            query.setBusinessId(pilotApplication.getId());
            query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
            List<CommonAttachment> oldAttachments = attachmentService.selectCommonAttachmentList(query);
            if (!oldAttachments.isEmpty()) {
                Long[] attachmentIds = oldAttachments.stream()
                    .map(CommonAttachment::getAttachmentId)
                    .toArray(Long[]::new);
                attachmentService.deleteCommonAttachmentByIds(attachmentIds);
            }
            
            // 保存新的附件
            if (!pilotApplication.getAttachments().isEmpty()) {
                pilotApplication.getAttachments().forEach(attachment -> {
                    attachment.setBusinessId(pilotApplication.getId());
                    attachment.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
                    attachmentService.insertCommonAttachment(attachment);
                });
            }
        }
        
        return rows;
    }

    /**
     * 删除中试申请信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePilotApplicationById(Long id, Long userId) {
        // 获取申请信息
        PilotApplication application = selectPilotApplicationById(id);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }
        // 验证是否是当前用户的申请
        if (!application.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权删除该申请");
        }
        // 验证状态是否可以删除
        if (!PilotApplicationStatus.PENDING_REVIEW.equals(application.getStatus())) {
            throw new ServiceException("只有待审核的申请可以删除");
        }

        // 删除附件
        CommonAttachment query = new CommonAttachment();
        query.setBusinessId(id);
        query.setBusinessType(CommonAttachmentBusinessType.PILOT_APPLICATION_ATTACHMENT);
        List<CommonAttachment> attachments = attachmentService.selectCommonAttachmentList(query);
        if (!attachments.isEmpty()) {
            Long[] attachmentIds = attachments.stream()
                .map(CommonAttachment::getAttachmentId)
                .toArray(Long[]::new);
            attachmentService.deleteCommonAttachmentByIds(attachmentIds);
        }
        
        return pilotApplicationMapper.deletePilotApplicationById(id);
    }

    @Override
    public PilotApplication getPilotApplicationDetail(Long id, Long userId) {
        // 获取申请信息
        PilotApplication application = selectPilotApplicationById(id);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }
        // 验证是否是当前用户的申请
        if (!application.getPortalUserId().equals(userId)) {
            throw new ServiceException("无权访问该申请");
        }
        return application;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PilotApplication createPilotApplication(PilotApplication pilotApplication) {
        // 设置基础信息
        pilotApplication.setStatus(PilotApplicationStatus.PENDING_REVIEW);  // 待审核状态
        pilotApplication.setCreateTime(DateUtils.getNowDate());
        pilotApplication.setCreateBy(SecurityUtils.getUsername());

        // 插入申请
        int rows = insertPilotApplication(pilotApplication);
        if (rows <= 0) {
            throw new ServiceException("申请提交失败，请稍后重试");
        }

        // 查询并返回创建的申请信息
        return selectPilotApplicationById(pilotApplication.getId());
    }

} 